package main

import (
	"backend/internetal"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// 创建机器人
func CreateRobotRepository(c *gin.Context) {
	var robot internetal.Robot
	if err := c.ShouldBindJSON(&robot); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "参数绑定失败"})
		return
	}

	// 从上下文中获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"code": 401, "msg": "未认证"})
		return
	}

	// 安全地断言userID为uint类型
	userIDUint, ok := userID.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "用户ID类型错误"})
		return
	}

	// 设置机器人的用户ID
	robot.UserID = userIDUint

	createdRobot, err := CreateRobotService(robot)
	if err != nil {
		c.<PERSON>(http.StatusInternalServerError, gin.H{"code": 500, "msg": "创建失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"code": 200, "data": createdRobot, "msg": "创建成功"})
}

// 删除机器人（从 JSON 体读取 id）
func DeleteRobotRepository(c *gin.Context) {
	// 从上下文中获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"code": 401, "msg": "未认证"})
		return
	}

	// 安全地断言userID为uint类型
	userIDUint, ok := userID.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "用户ID类型错误"})
		return
	}

	var req struct {
		ID int `json:"id"`
	}
	if err := c.ShouldBindJSON(&req); err != nil || req.ID == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "缺少或非法的id"})
		return
	}

	// 检查机器人是否存在
	robot, err := FindByIdService(req.ID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"code": 404, "msg": "未查找到相关机器人"})
		return
	}

	// 检查机器人是否属于当前用户或是否是默认机器人
	if robot.UserID != userIDUint && !IsDefaultRobot(robot) {
		c.JSON(http.StatusForbidden, gin.H{"code": 403, "msg": "无权删除该机器人"})
		return
	}

	err = DeleteRobotService(req.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "删除失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"code": 200, "msg": "删除成功"})
}

// 通过ID查找机器人，id通过query参数传递
func FindByIdRepository(c *gin.Context) {
	// 从上下文中获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"code": 401, "msg": "未认证"})
		return
	}

	// 安全地断言userID为uint类型
	userIDUint, ok := userID.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "用户ID类型错误"})
		return
	}

	idStr := c.Query("id")
	id, err := strconv.Atoi(idStr)
	if err != nil || id == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "缺少或非法的id"})
		return
	}

	robot, err := FindByIdService(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"code": 404, "msg": "未查找到相关机器人"})
		return
	}

	// 检查机器人是否属于当前用户或是否是默认机器人
	if robot.UserID != userIDUint && !IsDefaultRobot(robot) {
		c.JSON(http.StatusForbidden, gin.H{"code": 403, "msg": "无权访问该机器人"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"code": 200, "data": robot})
}

// 通过名称查找机器人，name通过query参数传递
func FindByNameRepository(c *gin.Context) {
	// 从上下文中获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"code": 401, "msg": "未认证"})
		return
	}

	// 安全地断言userID为uint类型
	userIDUint, ok := userID.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "用户ID类型错误"})
		return
	}

	name := c.Query("name")
	if name == "" {
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "缺少name参数"})
		return
	}

	robot, err := FindByNameService(name)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"code": 404, "msg": "未查找到相关机器人"})
		return
	}

	// 检查机器人是否属于当前用户或是否是默认机器人
	if robot.UserID != userIDUint && !IsDefaultRobot(robot) {
		c.JSON(http.StatusForbidden, gin.H{"code": 403, "msg": "无权访问该机器人"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"code": 200, "data": robot})
}

// 更新机器人
func UpdateRobotRepository(c *gin.Context) {
	// 从上下文中获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"code": 401, "msg": "未认证"})
		return
	}

	// 安全地断言userID为uint类型
	userIDUint, ok := userID.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "用户ID类型错误"})
		return
	}

	var robot internetal.Robot
	if err := c.ShouldBindJSON(&robot); err != nil || robot.ID == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "参数绑定失败或缺少id"})
		return
	}

	// 检查机器人是否存在
	existingRobot, err := FindByIdService(int(robot.ID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"code": 404, "msg": "未查找到相关机器人"})
		return
	}

	// 检查机器人是否属于当前用户或是否是默认机器人
	if existingRobot.UserID != userIDUint && !IsDefaultRobot(existingRobot) {
		c.JSON(http.StatusForbidden, gin.H{"code": 403, "msg": "无权更新该机器人"})
		return
	}

	err = UpdateRobotService(robot)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "更新失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"code": 200, "msg": "更新成功"})
}

// 列表查询机器人
func ListRobotsRepository(c *gin.Context) {
	// 从上下文中获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"code": 401, "msg": "未认证"})
		return
	}

	// 安全地断言userID为uint类型
	userIDUint, ok := userID.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "用户ID类型错误"})
		return
	}

	robots, err := ListRobotsService(userIDUint)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "获取列表失败"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"code": 200, "data": robots})
}

// 通过ID查找机器人
func FindByRobotIdRepository(c *gin.Context) {
	// 从上下文中获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"code": 401, "msg": "未认证"})
		return
	}

	// 安全地断言userID为uint类型
	userIDUint, ok := userID.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "用户ID类型错误"})
		return
	}

	idStr := c.Query("id")
	if idStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "缺少id参数"})
		return
	}

	// 将字符串ID转换为整数
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "无效的id格式，必须是整数"})
		return
	}

	robot, err := FindByRobotIdService(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"code": 404, "msg": "未查找到相关机器人"})
		return
	}

	// 检查机器人是否属于当前用户或是否是默认机器人
	if robot.UserID != userIDUint && !IsDefaultRobot(robot) {
		c.JSON(http.StatusForbidden, gin.H{"code": 403, "msg": "无权访问该机器人"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"code": 200, "data": robot})
}

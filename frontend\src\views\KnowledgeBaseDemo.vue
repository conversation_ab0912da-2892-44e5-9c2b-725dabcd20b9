<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-6xl mx-auto px-4">
      <!-- 页面标题 -->
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">知识库管理系统</h1>
        <p class="text-gray-600">完整的知识库创建、管理和编辑流程演示</p>
      </div>

      <!-- 功能区域 -->
      <div class="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6 mb-8">
        <button
            @click="showCreateKnowledgeBase"
            class="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-semibold shadow-md hover:shadow-lg"
        >
          创建新知识库
        </button>

        <!-- 知识库搜索框 -->
        <div class="relative w-full max-w-md">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
          <input
              v-model="searchQuery"
              type="text"
              placeholder="搜索知识库名称、描述或类型..."
              class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-white shadow-sm"
              @input="handleSearch"
              @keydown.esc="clearSearch"
          >
          <!-- 清除按钮 -->
          <div v-if="searchQuery" class="absolute inset-y-0 right-0 pr-3 flex items-center">
            <button
                @click="clearSearch"
                class="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- 知识库列表 -->
      <div class="bg-white rounded-xl shadow-lg p-6">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-xl font-bold text-gray-900">
            {{ searchQuery ? '搜索结果' : '我的知识库' }}
          </h2>
          <div class="flex items-center space-x-4">
            <span v-if="searchQuery" class="text-sm text-blue-600 bg-blue-50 px-3 py-1 rounded-full">
              找到 {{ filteredKnowledgeBases.length }} 个结果
            </span>
            <span class="text-sm text-gray-600">共 {{ knowledgeBases.length }} 个知识库</span>
          </div>
        </div>

        <!-- 搜索提示 -->
        <div v-if="searchQuery && filteredKnowledgeBases.length === 0" class="text-center py-12">
          <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">未找到匹配的知识库</h3>
          <p class="text-gray-500">尝试使用不同的关键词搜索，或者创建新的知识库</p>
        </div>

        <div v-if="filteredKnowledgeBases.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div
              v-for="kb in filteredKnowledgeBases"
              :key="kb.id"
              @click="selectedKnowledgeBaseId = kb.id"
              :class="[
              'border rounded-lg p-6 cursor-pointer transition-all duration-200',
              selectedKnowledgeBaseId === kb.id
                ? 'border-blue-500 bg-blue-50 shadow-lg ring-2 ring-blue-200'
                : 'border-gray-200 hover:border-blue-300 hover:shadow-md'
            ]"
          >
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center space-x-3">
                <div class="relative">
                  <div :class="[
                    'w-12 h-12 rounded-lg flex items-center justify-center text-white text-xl',
                    kb.type === 'text' ? 'bg-blue-500' : kb.type === 'table' ? 'bg-green-500' : 'bg-orange-500'
                  ]">
                    {{ kb.type === 'text' ? '📄' : kb.type === 'table' ? '📊' : '🖼️' }}
                  </div>
                  <!-- 选中状态指示器 -->
                  <div
                      v-if="selectedKnowledgeBaseId === kb.id"
                      class="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center shadow-lg"
                  >
                    <span class="text-white text-sm">✓</span>
                  </div>
                </div>
                <div>
                  <h3 class="font-semibold text-gray-900" v-html="highlightSearchTerm(kb.name)"></h3>
                  <p class="text-sm text-gray-600" v-html="highlightSearchTerm(getTypeLabel(kb.type))"></p>
                </div>
              </div>
              <div class="flex space-x-2">
                <button
                    @click="editKnowledgeBase(kb)"
                    class="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                    title="编辑内容"
                >
                  ✏️
                </button>
                <button
                    @click="deleteKnowledgeBase(kb.id)"
                    class="p-2 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                    title="删除"
                >
                  🗑️
                </button>
              </div>
            </div>

            <p class="text-sm text-gray-600 mb-4" v-html="highlightSearchTerm(kb.description || '')"></p>

            <div class="flex items-center justify-between text-sm text-gray-500">
              <span>{{ kb.itemCount }} 项内容</span>
              <span>{{ formatDate(kb.updateTime) }}</span>
            </div>

            <div class="mt-4 pt-4 border-t border-gray-100">
              <div class="flex space-x-2">
                <button
                    @click.stop="viewKnowledgeBase(kb)"
                    class="flex-1 px-3 py-2 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 transition-colors"
                >
                  查看详情
                </button>
                <button
                    @click.stop="editKnowledgeBase(kb)"
                    class="flex-1 px-3 py-2 bg-gray-500 text-white rounded text-sm hover:bg-gray-600 transition-colors"
                >
                  编辑内容
                </button>
              </div>
              <!-- 选中状态提示 -->
              <div
                  v-if="selectedKnowledgeBaseId === kb.id"
                  class="mt-3 px-3 py-2 bg-green-100 border border-green-300 rounded text-sm text-green-800 text-center"
              >
                ✓ 已选中此知识库
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else-if="!searchQuery && knowledgeBases.length === 0" class="text-center py-12">
          <div class="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <span class="text-4xl text-gray-400">📚</span>
          </div>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">还没有知识库</h3>
          <p class="text-gray-600 mb-6">创建您的第一个知识库，开始管理文档和数据</p>
          <button
              @click="showCreateKnowledgeBase"
              class="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            创建知识库
          </button>
        </div>
      </div>

      <!-- 流程说明 -->
      <div class="mt-8 bg-white rounded-xl shadow-lg p-6">
        <h2 class="text-xl font-bold text-gray-900 mb-4">知识库管理流程</h2>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div class="text-center">
            <div class="w-16 h-16 mx-auto mb-3 bg-blue-100 rounded-full flex items-center justify-center">
              <span class="text-2xl">📁</span>
            </div>
            <h3 class="font-semibold text-gray-900 mb-2">1. 上传文件</h3>
            <p class="text-sm text-gray-600">支持 PDF、DOC、DOCX、MD、TXT 等多种格式</p>
          </div>
          <div class="text-center">
            <div class="w-16 h-16 mx-auto mb-3 bg-green-100 rounded-full flex items-center justify-center">
              <span class="text-2xl">⚙️</span>
            </div>
            <h3 class="font-semibold text-gray-900 mb-2">2. 创建设置</h3>
            <p class="text-sm text-gray-600">配置解析模式、分段策略和索引设置</p>
          </div>
          <div class="text-center">
            <div class="w-16 h-16 mx-auto mb-3 bg-yellow-100 rounded-full flex items-center justify-center">
              <span class="text-2xl">👁️</span>
            </div>
            <h3 class="font-semibold text-gray-900 mb-2">3. 分段预览</h3>
            <p class="text-sm text-gray-600">预览文档分段结果，支持手动调整</p>
          </div>
          <div class="text-center">
            <div class="w-16 h-16 mx-auto mb-3 bg-purple-100 rounded-full flex items-center justify-center">
              <span class="text-2xl">🚀</span>
            </div>
            <h3 class="font-semibold text-gray-900 mb-2">4. 数据处理</h3>
            <p class="text-sm text-gray-600">自动建立索引，完成知识库创建</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 知识库管理器 -->
    <KnowledgeBaseManager
        :show="showManager"
        :knowledgeBase="currentKnowledgeBase"
        @close="showManager = false"
        @complete="onKnowledgeBaseComplete"
    />

    <!-- 知识库编辑器 -->
    <KnowledgeBaseEditor
        :show="showEditor"
        :knowledgeBase="currentKnowledgeBase"
        @close="showEditor = false"
        @save="onKnowledgeBaseSave"
        @knowledge-base-updated="onKnowledgeBaseUpdated"
    />

    <!-- 知识库详情模态框 -->
    <div
        v-if="showKnowledgeBaseDetail"
        class="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50"
        @click.self="closeKnowledgeBaseDetail"
    >
      <div class="bg-white rounded-2xl shadow-2xl w-[600px] max-h-[80vh] overflow-hidden transform transition-all duration-300 scale-100">
        <!-- 头部 -->
        <div class="bg-gradient-to-r from-blue-500 to-purple-600 px-6 py-4 text-white">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center mr-3">
                <span class="text-xl">{{ getKnowledgeBaseIcon(selectedKnowledgeBaseDetail?.type) }}</span>
              </div>
              <div>
                <h3 class="text-xl font-bold">{{ selectedKnowledgeBaseDetail?.name }}</h3>
                <p class="text-blue-100 text-sm">{{ getTypeLabel(selectedKnowledgeBaseDetail?.type) }}</p>
              </div>
            </div>
            <button
                @click="closeKnowledgeBaseDetail"
                class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center hover:bg-white/30 transition-colors"
            >
              <span class="text-xl">×</span>
            </button>
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="p-6 overflow-y-auto max-h-[calc(80vh-120px)]">
          <!-- 基本信息 -->
          <div class="mb-6">
            <h4 class="text-lg font-semibold text-gray-900 mb-3 flex items-center">
              <span class="w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center mr-2">
                <span class="text-sm">📋</span>
              </span>
              基本信息
            </h4>
            <div class="bg-gray-50 rounded-lg p-4 space-y-3">
              <div class="flex justify-between">
                <span class="text-gray-600">名称：</span>
                <span class="font-medium">{{ selectedKnowledgeBaseDetail?.name }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">类型：</span>
                <span class="font-medium">{{ getTypeLabel(selectedKnowledgeBaseDetail?.type) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">创建时间：</span>
                <span class="font-medium">{{ formatDate(selectedKnowledgeBaseDetail?.createdAt || selectedKnowledgeBaseDetail?.createTime) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">更新时间：</span>
                <span class="font-medium">{{ formatDate(selectedKnowledgeBaseDetail?.updatedAt || selectedKnowledgeBaseDetail?.updateTime) }}</span>
              </div>
              <div v-if="selectedKnowledgeBaseDetail?.description" class="border-t pt-3">
                <span class="text-gray-600">描述：</span>
                <p class="mt-1 text-gray-800">{{ selectedKnowledgeBaseDetail.description }}</p>
              </div>
            </div>
          </div>

          <!-- 内容统计 -->
          <div class="mb-6">
            <h4 class="text-lg font-semibold text-gray-900 mb-3 flex items-center">
              <span class="w-6 h-6 bg-green-100 rounded-lg flex items-center justify-center mr-2">
                <span class="text-sm">📊</span>
              </span>
              内容统计
            </h4>
            <div class="grid grid-cols-2 gap-4">
              <div class="bg-blue-50 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-blue-600">{{ selectedKnowledgeBaseDetail?.itemCount || selectedKnowledgeBaseDetail?.segmentCount || 0 }}</div>
                <div class="text-sm text-blue-800">内容条数</div>
              </div>
              <div class="bg-green-50 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-green-600">{{ (selectedKnowledgeBaseDetail?.files || []).length || 0 }}</div>
                <div class="text-sm text-green-800">文件数量</div>
              </div>
            </div>
          </div>

          <!-- 文件列表 -->
          <div v-if="selectedKnowledgeBaseDetail?.files && selectedKnowledgeBaseDetail.files.length > 0" class="mb-6">
            <h4 class="text-lg font-semibold text-gray-900 mb-3 flex items-center">
              <span class="w-6 h-6 bg-purple-100 rounded-lg flex items-center justify-center mr-2">
                <span class="text-sm">📁</span>
              </span>
              文件列表
            </h4>
            <div class="space-y-2 max-h-40 overflow-y-auto">
              <div
                  v-for="file in selectedKnowledgeBaseDetail.files"
                  :key="file.id"
                  class="bg-gray-50 rounded-lg p-3 flex items-center justify-between"
              >
                <div class="flex items-center">
                  <span class="text-lg mr-2">📄</span>
                  <div>
                    <div class="font-medium text-gray-900">{{ file.name }}</div>
                    <div class="text-sm text-gray-500">{{ (file.segments || []).length }} 个分段</div>
                  </div>
                </div>
                <div class="text-sm text-gray-500">
                  {{ formatFileSize(file.size) }}
                </div>
              </div>
            </div>
          </div>

          <!-- 设置信息 -->
          <div v-if="selectedKnowledgeBaseDetail?.settings" class="mb-6">
            <h4 class="text-lg font-semibold text-gray-900 mb-3 flex items-center">
              <span class="w-6 h-6 bg-orange-100 rounded-lg flex items-center justify-center mr-2">
                <span class="text-sm">⚙️</span>
              </span>
              处理设置
            </h4>
            <div class="bg-gray-50 rounded-lg p-4 space-y-2">
              <div class="flex justify-between">
                <span class="text-gray-600">分段策略：</span>
                <span class="font-medium">{{ selectedKnowledgeBaseDetail.settings.segmentStrategy || '自动' }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">分段长度：</span>
                <span class="font-medium">{{ selectedKnowledgeBaseDetail.settings.segmentLength || 1000 }} 字符</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">重叠长度：</span>
                <span class="font-medium">{{ selectedKnowledgeBaseDetail.settings.segmentOverlap || 100 }} 字符</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部操作按钮 -->
        <div class="border-t bg-gray-50 px-6 py-4 flex space-x-3">
          <button
              @click="editKnowledgeBase(selectedKnowledgeBaseDetail)"
              class="flex-1 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            编辑知识库
          </button>
          <button
              @click="closeKnowledgeBaseDetail"
              class="flex-1 px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import KnowledgeBaseManager from '../components/KnowledgeBaseManager.vue'
import KnowledgeBaseEditor from '../components/KnowledgeBaseEditor.vue'


export default {
  name: 'KnowledgeBaseDemo',
  components: {
    KnowledgeBaseManager: KnowledgeBaseManager,
    KnowledgeBaseEditor
  },

  data() {
    return {
      showManager: false,
      showEditor: false,
      currentKnowledgeBase: null,

      // 知识库数据（从数据库加载）
      knowledgeBases: [],

      // 知识库详情模态框
      showKnowledgeBaseDetail: false,
      selectedKnowledgeBaseDetail: null,

      // 选中的知识库
      selectedKnowledgeBaseId: null,

      // 搜索相关
      searchQuery: ''
    }
  },

  computed: {
    // 过滤后的知识库列表
    filteredKnowledgeBases() {
      if (!this.searchQuery.trim()) {
        return this.knowledgeBases
      }

      const query = this.searchQuery.toLowerCase().trim()
      return this.knowledgeBases.filter(kb => {
        return (
            kb.name.toLowerCase().includes(query) ||
            (kb.description && kb.description.toLowerCase().includes(query)) ||
            this.getTypeLabel(kb.type).toLowerCase().includes(query)
        )
      })
    }
  },

  async mounted() {
    // 组件挂载时加载知识库列表
    await this.loadKnowledgeBases()
  },

  methods: {
    // 从数据库加载知识库列表
    async loadKnowledgeBases() {
      try {
        console.log('开始加载知识库列表...')
        const response = await fetch('http://localhost:8080/api/knowledge-base/list', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const result = await response.json()
        console.log('知识库列表响应:', result)

        if (result.code === 200) {
          // 转换数据格式以匹配前端显示需求
          this.knowledgeBases = result.data.map(kb => ({
            id: kb.id,
            name: kb.name,
            description: kb.description,
            type: kb.type,
            itemCount: kb.segmentCount || 0,
            updateTime: new Date(kb.updatedAt),
            status: kb.status || 'completed'
          }))
          console.log('知识库列表加载成功:', this.knowledgeBases)
        } else {
          console.error('加载知识库列表失败:', result.msg)
          // 如果加载失败，保持空数组
          this.knowledgeBases = []
        }
      } catch (error) {
        console.error('加载知识库列表出错:', error)
        // 如果出错，保持空数组
        this.knowledgeBases = []
      }
    },

    showCreateKnowledgeBase() {
      this.currentKnowledgeBase = {
        name: '',
        description: '',
        type: 'text'
      }
      this.showManager = true
    },

    handleSearch() {
      // 搜索处理（实时搜索，无需额外处理）
      console.log('搜索关键词:', this.searchQuery)

      // 如果有搜索词，记录搜索时间
      if (this.searchQuery.trim()) {
        const startTime = performance.now()
        // 触发重新计算（Vue会自动处理）
        this.$nextTick(() => {
          const endTime = performance.now()
          console.log(`搜索耗时: ${(endTime - startTime).toFixed(2)}ms`)
        })
      }
    },

    clearSearch() {
      this.searchQuery = ''
    },

    editKnowledgeBase(kb) {
      this.currentKnowledgeBase = kb
      this.showEditor = true
    },

    async viewKnowledgeBase(kb) {
      this.selectedKnowledgeBaseDetail = kb
      this.showKnowledgeBaseDetail = true

      // 如果有ID，尝试从后端获取详细信息
      if (kb.id) {
        try {
          const response = await fetch(`http://localhost:8080/api/knowledge-base/detail/${kb.id}`)
          if (response.ok) {
            const result = await response.json()
            if (result.code === 200) {
              this.selectedKnowledgeBaseDetail = { ...kb, ...result.data }
            }
          }
        } catch (error) {
          console.error('获取知识库详情失败:', error)
        }
      }
    },

    closeKnowledgeBaseDetail() {
      this.showKnowledgeBaseDetail = false
      this.selectedKnowledgeBaseDetail = null
    },

    deleteKnowledgeBase(id) {
      if (confirm('确定要删除这个知识库吗？此操作不可恢复。')) {
        this.knowledgeBases = this.knowledgeBases.filter(kb => kb.id !== id)
      }
    },

    async onKnowledgeBaseComplete(result) {
      console.log('创建知识库完成:', result)

      try {
        console.log('准备创建知识库:', result)
        console.log('后端API地址:', 'http://localhost:8080/api/knowledge-base/create')

        // 构建请求数据
        const requestData = {
          name: result.name,
          description: result.description,
          type: result.type,
          files: result.files || [],
          settings: {
            parseText: true,
            parseTable: false,
            contentFilter: '',
            segmentStrategy: result.settings?.segmentStrategy || 'auto',
            segmentLength: result.settings?.segmentLength || 500,
            segmentOverlap: result.settings?.segmentOverlap || 50,
            separator: '',
            extractKeywords: result.settings?.enableKeywordExtraction !== false,
            generateSummary: result.settings?.enableSummary !== false,
            useAI: result.settings?.enableAI !== false,
            fallbackToLocal: result.settings?.fallbackToLocal !== false
          }
        }

        console.log('发送的请求数据:', JSON.stringify(requestData, null, 2))

        // 调用后端API保存知识库
        const response = await fetch('http://localhost:8080/api/knowledge-base/create', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestData)
        })

        console.log('响应状态:', response.status, response.statusText)

        if (!response.ok) {
          const errorText = await response.text()
          console.error('API响应错误:', errorText)
          alert('保存知识库失败: ' + errorText)
          return
        }

        const apiResult = await response.json()
        if (apiResult.code === 200) {
          console.log('知识库保存到数据库成功:', apiResult.data)

          // 重新加载知识库列表以确保数据同步
          await this.loadKnowledgeBases()

          alert('知识库创建成功！')
        } else {
          console.error('保存知识库失败:', apiResult.msg)
          alert('保存知识库失败: ' + apiResult.msg)
        }
      } catch (error) {
        console.error('保存知识库失败:', error)
        alert('保存知识库失败: ' + error.message)
      }

      this.showManager = false
    },

    onKnowledgeBaseSave(result) {
      // 更新知识库信息
      const index = this.knowledgeBases.findIndex(kb => kb.id === result.knowledgeBase.id)
      if (index !== -1) {
        this.knowledgeBases[index] = {
          ...this.knowledgeBases[index],
          ...result.knowledgeBase,
          updateTime: new Date()
        }
      }
      this.showEditor = false
      alert('知识库保存成功！')
    },

    onKnowledgeBaseUpdated(updatedData) {
      console.log('知识库信息已更新:', updatedData)

      // 更新当前知识库对象
      if (this.currentKnowledgeBase && this.currentKnowledgeBase.id === updatedData.id) {
        this.currentKnowledgeBase = {
          ...this.currentKnowledgeBase,
          name: updatedData.name,
          description: updatedData.description
        }
      }

      // 更新知识库列表中的对应项
      const index = this.knowledgeBases.findIndex(kb => kb.id === updatedData.id)
      if (index !== -1) {
        this.knowledgeBases[index] = {
          ...this.knowledgeBases[index],
          name: updatedData.name,
          description: updatedData.description
        }
      }

      // 如果详情页面正在显示，也要更新
      if (this.selectedKnowledgeBaseDetail && this.selectedKnowledgeBaseDetail.id === updatedData.id) {
        this.selectedKnowledgeBaseDetail = {
          ...this.selectedKnowledgeBaseDetail,
          name: updatedData.name,
          description: updatedData.description
        }
      }
    },

    getTypeLabel(type) {
      const labels = {
        text: '文本格式',
        table: '表格格式',
        image: '图片格式'
      }
      return labels[type] || '未知格式'
    },

    formatDate(date) {
      if (!date) return '未知'
      try {
        const dateObj = new Date(date)
        if (isNaN(dateObj.getTime())) return '未知'
        return dateObj.toLocaleDateString('zh-CN')
      } catch (error) {
        console.error('日期格式化错误:', error, date)
        return '未知'
      }
    },

    getKnowledgeBaseIcon(type) {
      const icons = {
        'text': '📄',
        'table': '📊',
        'image': '🖼️'
      }
      return icons[type] || '📄'
    },

    formatFileSize(bytes) {
      if (!bytes) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    highlightSearchTerm(text) {
      if (!this.searchQuery.trim() || !text) {
        return text
      }

      const query = this.searchQuery.trim()
      const regex = new RegExp(`(${query})`, 'gi')
      return text.replace(regex, '<mark class="bg-yellow-200 px-1 rounded">$1</mark>')
    }
  }
}
</script>

<style scoped>
/* 可以添加一些自定义样式 */
</style>

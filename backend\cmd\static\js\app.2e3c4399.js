(function(){"use strict";var e={2065:function(e,t,s){var o=s(5130),n=s(6768),a=s(4232);const r={class:"h-screen bg-gradient-to-br from-gray-50 to-gray-100 font-serif"},l={class:"bg-white shadow-sm border-b border-gray-200 px-6 py-3"},i={class:"flex items-center justify-between"},c={class:"flex items-center space-x-6"},d={class:"flex space-x-4"},g={class:"text-sm text-gray-500"},u={class:"h-[calc(100vh-64px)]"},h={key:0,class:"flex h-full"},m={key:1,class:"h-full"},p={key:2,class:"h-full overflow-y-auto"};function b(e,t,s,o,b,f){const x=(0,n.g2)("AssistantList"),y=(0,n.g2)("VoiceSettingsModal"),k=(0,n.g2)("ChatArea"),v=(0,n.g2)("KnowledgeBaseDemo"),w=(0,n.g2)("DocumentParserTest"),L=(0,n.g2)("ModalComponents"),C=(0,n.g2)("HistoryModal"),S=(0,n.g2)("RobotSettingsModal");return(0,n.uX)(),(0,n.CE)("div",r,[(0,n.Lk)("div",l,[(0,n.Lk)("div",i,[(0,n.Lk)("div",c,[t[17]||(t[17]=(0,n.Lk)("h1",{class:"text-xl font-bold text-gray-900"},"AI助手平台",-1)),(0,n.Lk)("nav",d,[(0,n.Lk)("button",{onClick:t[0]||(t[0]=e=>b.currentView="chat"),class:(0,a.C4)(["px-4 py-2 rounded-lg transition-colors","chat"===b.currentView?"bg-blue-500 text-white":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"])}," 对话助手 ",2),(0,n.Lk)("button",{onClick:t[1]||(t[1]=e=>b.currentView="knowledge"),class:(0,a.C4)(["px-4 py-2 rounded-lg transition-colors","knowledge"===b.currentView?"bg-blue-500 text-white":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"])}," 知识库管理 ",2),(0,n.Lk)("button",{onClick:t[2]||(t[2]=e=>b.currentView="parser-test"),class:(0,a.C4)(["px-4 py-2 rounded-lg transition-colors","parser-test"===b.currentView?"bg-blue-500 text-white":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"])}," 解析测试 ",2)])]),(0,n.Lk)("div",g," 当前视图: "+(0,a.v_)(f.getViewName()),1)])]),(0,n.Lk)("div",u,["chat"===b.currentView?((0,n.uX)(),(0,n.CE)("div",h,[(0,n.bF)(x,{assistants:b.assistants,"current-selected-assistant":b.currentSelectedAssistant,"search-query":b.searchQuery,onAdd:f.openAddModal,onSearch:t[3]||(t[3]=e=>b.searchQuery=e),onSelect:f.selectAssistant,onEdit:f.openEditModal,onSettings:f.openSettingsModal,onDelete:f.openDeleteModal,onHistory:t[4]||(t[4]=e=>b.showHistoryModal=!0)},null,8,["assistants","current-selected-assistant","search-query","onAdd","onSelect","onEdit","onSettings","onDelete"]),(0,n.bF)(y,{show:b.showVoiceSettingsModal,"current-settings":e.voiceSettings,onClose:t[5]||(t[5]=e=>b.showVoiceSettingsModal=!1),onSettingsChanged:e.handleVoiceSettingsChanged},null,8,["show","current-settings","onSettingsChanged"]),(0,n.bF)(k,{ref:"chatAreaRef","current-selected-assistant":b.currentSelectedAssistant,messages:f.currentMessages,"message-input":b.messageInput,onReset:f.resetChat,onInput:t[6]||(t[6]=e=>b.messageInput=e),onSend:f.sendMessage,onDial:t[7]||(t[7]=e=>b.showDialModal=!0),onChatSettings:t[8]||(t[8]=e=>b.showChatSettingsModal=!0),onVoiceSettings:t[9]||(t[9]=e=>b.showVoiceSettingsModal=!0)},null,8,["current-selected-assistant","messages","message-input","onReset","onSend"])])):"knowledge"===b.currentView?((0,n.uX)(),(0,n.CE)("div",m,[(0,n.bF)(v)])):"parser-test"===b.currentView?((0,n.uX)(),(0,n.CE)("div",p,[(0,n.bF)(w)])):(0,n.Q3)("",!0)]),(0,n.bF)(L,{show:b.showVoiceSettingsModal,"show-add-modal":b.showAddModal,"show-settings-modal":b.showSettingsModal,"show-delete-modal":b.showDeleteModal,"show-dial-modal":b.showDialModal,"show-chat-settings-modal":b.showChatSettingsModal,"is-editing":b.isEditing,"current-assistant":b.currentAssistant,"current-settings-assistant":b.currentSettingsAssistant,"current-personality":b.currentPersonality,"delete-assistant-name":b.deleteAssistantName,"dial-number":b.dialNumber,"chat-settings":b.chatSettings,"current-selected-assistant":b.currentSelectedAssistant,assistants:b.assistants,onCloseAdd:f.closeModal,onUpdateName:t[10]||(t[10]=e=>b.currentAssistant.name=e),onUpdateDescription:t[11]||(t[11]=e=>b.currentAssistant.description=e),onSave:f.saveAssistant,onCloseSettings:f.closeSettingsModal,onUpdatePersonality:t[12]||(t[12]=e=>b.currentPersonality=e),onSavePersonality:f.savePersonality,onCloseDelete:f.closeDeleteModal,onConfirmDelete:f.confirmDelete,onCloseDial:t[13]||(t[13]=e=>b.showDialModal=!1),onAddNumber:f.addNumber,onMakeCall:f.makeCall,onClearNumber:f.clearNumber,onCloseChatSettings:t[14]||(t[14]=e=>b.showChatSettingsModal=!1),onSaveChatSettings:f.saveChatSettings,onUpdateChatSetting:f.updateChatSetting,onVoiceSettings:t[15]||(t[15]=e=>b.showVoiceSettingsModal=!0),onSelectRobot:f.selectAssistant},null,8,["show","show-add-modal","show-settings-modal","show-delete-modal","show-dial-modal","show-chat-settings-modal","is-editing","current-assistant","current-settings-assistant","current-personality","delete-assistant-name","dial-number","chat-settings","current-selected-assistant","assistants","onCloseAdd","onSave","onCloseSettings","onSavePersonality","onCloseDelete","onConfirmDelete","onAddNumber","onMakeCall","onClearNumber","onSaveChatSettings","onUpdateChatSetting","onSelectRobot"]),(0,n.bF)(C,{show:b.showHistoryModal,assistants:b.assistants,onClose:t[16]||(t[16]=e=>b.showHistoryModal=!1),onRestoreConversation:f.restoreConversation},null,8,["show","assistants","onRestoreConversation"]),(0,n.bF)(S,{show:b.showRobotSettingsModal,robot:b.currentSettingsAssistant,onClose:f.closeRobotSettingsModal,onSave:f.handleRobotSettingsSave},null,8,["show","robot","onClose","onSave"])])}s(4114),s(8111),s(2489),s(116),s(7588),s(1701),s(3579);const f={class:"w-80 bg-white shadow-xl border-r border-gray-100 flex flex-col"},x={class:"p-6 flex-shrink-0"},y={class:"flex space-x-3 mb-6"},k={class:"relative w-1/2"},v=["value"],w={class:"flex-1 overflow-y-auto px-6 pb-6"},L={class:"space-y-3"},C={key:0,class:"text-center py-6"};function S(e,t,s,o,a,r){const l=(0,n.g2)("AssistantCard");return(0,n.uX)(),(0,n.CE)("div",f,[(0,n.Lk)("div",x,[t[9]||(t[9]=(0,n.Lk)("div",{class:"mb-6"},[(0,n.Lk)("h1",{class:"text-2xl font-bold text-gray-900 mb-2 flex items-center font-sans"},[(0,n.Lk)("span",{class:"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white text-sm mr-3 font-sans"},"🤖"),(0,n.eW)(" 智能语音机器人 ")]),(0,n.Lk)("p",{class:"text-gray-500 text-sm leading-relaxed font-sans"},"提供一站式的智能语音解决方案")],-1)),(0,n.Lk)("div",y,[(0,n.Lk)("button",{onClick:t[0]||(t[0]=t=>e.$emit("add")),class:"w-1/2 bg-gradient-to-r from-blue-500/90 to-cyan-500/90 backdrop-blur-sm border border-white/20 hover:from-blue-600/95 hover:to-cyan-600/95 text-white font-medium py-3 px-4 rounded-xl transition-all duration-300 flex items-center justify-center shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 font-sans"}," 新增 "),(0,n.Lk)("div",k,[(0,n.Lk)("input",{value:s.searchQuery,onInput:t[1]||(t[1]=t=>e.$emit("search",t.target.value)),type:"text",placeholder:"搜索助手...",class:"w-full px-4 py-3 pl-10 border border-white/20 rounded-xl focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-200 bg-gradient-to-r from-red-400/80 to-pink-400/80 backdrop-blur-sm focus:from-red-500/90 focus:to-pink-500/90 text-white placeholder-white/70 font-sans"},null,40,v),t[7]||(t[7]=(0,n.Lk)("span",{class:"absolute left-3 top-1/2 transform -translate-y-1/2 text-white/80 font-sans"},"🔍",-1))])]),(0,n.Lk)("button",{onClick:t[2]||(t[2]=t=>e.$emit("history")),class:"w-full bg-gradient-to-r from-yellow-400/80 via-orange-400/80 via-pink-400/80 via-purple-400/80 to-blue-400/80 backdrop-blur-sm border border-white/30 hover:from-yellow-500/90 hover:via-orange-500/90 hover:via-pink-500/90 hover:via-purple-500/90 hover:to-blue-500/90 text-white font-medium py-3 px-4 rounded-xl transition-all duration-300 flex items-center justify-center shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 font-sans mt-3"},t[8]||(t[8]=[(0,n.Lk)("span",{class:"mr-2 text-lg font-sans"},"⏳",-1),(0,n.eW)(" 历史对话 ",-1)]))]),(0,n.Lk)("div",w,[(0,n.Lk)("div",L,[((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(r.filteredAssistants,o=>((0,n.uX)(),(0,n.Wv)(l,{key:o.id,assistant:o,"is-selected":s.currentSelectedAssistant?.id===o.id,onSelect:t[3]||(t[3]=t=>e.$emit("select",t)),onEdit:t[4]||(t[4]=t=>e.$emit("edit",t)),onSettings:t[5]||(t[5]=t=>e.$emit("settings",t)),onDelete:t[6]||(t[6]=t=>e.$emit("delete",t))},null,8,["assistant","is-selected"]))),128)),0===r.filteredAssistants.length&&s.searchQuery?((0,n.uX)(),(0,n.CE)("div",C,t[10]||(t[10]=[(0,n.Lk)("p",{class:"text-gray-500 font-sans"},"未找到匹配的助手",-1),(0,n.Lk)("p",{class:"text-gray-400 text-sm mt-2 font-sans"},"请尝试其他关键词",-1)]))):(0,n.Q3)("",!0)])])])}const T={class:"flex items-start justify-between mb-3 relative z-10"},E={class:"font-semibold text-gray-900 text-base group-hover:text-blue-700 transition-colors font-serif"},M={class:"robot-number"},A={class:"text-gray-600 text-sm mb-4 leading-relaxed font-serif line-clamp-2 relative z-10"},B={class:"flex items-center justify-between relative z-10"};function K(e,t,s,r,l,i){return(0,n.uX)(),(0,n.CE)("div",{onClick:t[3]||(t[3]=t=>e.$emit("select",s.assistant)),class:(0,a.C4)(["bg-gradient-to-br from-white via-blue-50/30 to-indigo-50/50 rounded-2xl p-5 border border-gray-200/60 hover:shadow-xl transition-all duration-300 hover:border-blue-300/60 group cursor-pointer relative overflow-hidden backdrop-blur-sm",s.isSelected?"border-blue-400/80 bg-gradient-to-br from-blue-50/80 via-indigo-50/60 to-purple-50/40 shadow-lg ring-2 ring-blue-200/50":""])},[t[7]||(t[7]=(0,n.Lk)("div",{class:"absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-blue-100/40 to-transparent rounded-full -translate-y-10 translate-x-10 group-hover:scale-110 transition-transform duration-300"},null,-1)),t[8]||(t[8]=(0,n.Lk)("div",{class:"absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-indigo-100/30 to-transparent rounded-full translate-y-8 -translate-x-8 group-hover:scale-110 transition-transform duration-300"},null,-1)),(0,n.Lk)("div",T,[(0,n.Lk)("h3",E,(0,a.v_)(s.assistant.name),1),(0,n.Lk)("div",M,"编号："+(0,a.v_)(s.assistant.id),1)]),(0,n.Lk)("p",A,(0,a.v_)(s.assistant.description),1),(0,n.Lk)("div",B,[(0,n.Lk)("button",{onClick:t[0]||(t[0]=(0,o.D$)(t=>e.$emit("edit",s.assistant),["stop"])),class:"text-blue-600 hover:text-blue-700 text-sm font-medium transition-colors flex items-center space-x-1 hover:bg-blue-50/50 px-2 py-1 rounded-lg font-serif"},t[4]||(t[4]=[(0,n.Lk)("svg",{class:"w-3.5 h-3.5",fill:"currentColor",viewBox:"0 0 24 24"},[(0,n.Lk)("path",{d:"M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z"})],-1),(0,n.Lk)("span",null,"编辑",-1)])),(0,n.Lk)("button",{onClick:t[1]||(t[1]=(0,o.D$)(t=>e.$emit("settings",s.assistant),["stop"])),class:"text-purple-600 hover:text-purple-700 text-sm font-medium transition-colors flex items-center space-x-1 hover:bg-purple-50/50 px-2 py-1 rounded-lg font-serif"},t[5]||(t[5]=[(0,n.Lk)("svg",{class:"w-3.5 h-3.5",fill:"currentColor",viewBox:"0 0 24 24"},[(0,n.Lk)("path",{d:"M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"})],-1),(0,n.Lk)("span",null,"设置",-1)])),(0,n.Lk)("button",{onClick:t[2]||(t[2]=(0,o.D$)(t=>e.$emit("delete",s.assistant),["stop"])),class:"text-red-600 hover:text-red-700 text-sm font-medium transition-colors flex items-center space-x-1 hover:bg-red-50/50 px-2 py-1 rounded-lg font-serif"},t[6]||(t[6]=[(0,n.Lk)("svg",{class:"w-3.5 h-3.5",fill:"currentColor",viewBox:"0 0 24 24"},[(0,n.Lk)("path",{d:"M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"})],-1),(0,n.Lk)("span",null,"删除",-1)]))])],2)}var I={name:"AssistantCard",props:{assistant:{type:Object,required:!0},isSelected:{type:Boolean,default:!1}},emits:["select","edit","settings","delete"]},_=s(1241);const j=(0,_.A)(I,[["render",K],["__scopeId","data-v-61ba2564"]]);var D=j,F={name:"AssistantList",components:{AssistantCard:D},props:{assistants:{type:Array,required:!0},currentSelectedAssistant:{type:Object,default:null},searchQuery:{type:String,default:""}},computed:{filteredAssistants(){return this.searchQuery.trim()?this.assistants.filter(e=>e.name.toLowerCase().includes(this.searchQuery.toLowerCase())||e.description.toLowerCase().includes(this.searchQuery.toLowerCase())):this.assistants}},emits:["add","search","select","edit","settings","delete","history"]};const $=(0,_.A)(F,[["render",S]]);var P=$;const R={class:"flex-1 flex flex-col bg-gradient-to-br from-blue-50/30 to-purple-50/30"},X={class:"bg-white/80 backdrop-blur-sm border-b border-gray-100 px-8 py-5 flex items-center justify-between"},V={class:"text-xl font-serif font-semibold text-gray-900 flex items-center"},z={class:"flex items-center space-x-3"},O={ref:"chatContainer",class:"flex-1 overflow-y-auto p-8 space-y-6"},N={class:"bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 py-3 rounded-2xl max-w-xs shadow-lg break-words"},U={class:"text-sm leading-relaxed font-serif"},W={class:"bg-white border border-gray-100 px-4 py-3 rounded-2xl shadow-lg max-w-md break-words"},Q=["innerHTML"],J={class:"bg-white/90 backdrop-blur-sm border-t border-gray-100 p-6"},H={class:"flex items-center space-x-4"},G=["value"];function q(e,t,s,r,l,i){const c=(0,n.g2)("VoiceRecorder");return(0,n.uX)(),(0,n.CE)("div",R,[(0,n.Lk)("div",X,[(0,n.Lk)("h2",V,[t[6]||(t[6]=(0,n.Lk)("span",{class:"w-6 h-6 bg-gradient-to-r from-green-400 to-blue-500 rounded-full mr-3"},null,-1)),(0,n.eW)(" "+(0,a.v_)(s.currentSelectedAssistant?s.currentSelectedAssistant.name:"效果测试"),1)]),(0,n.Lk)("div",z,[(0,n.Lk)("button",{onClick:t[0]||(t[0]=t=>e.$emit("voice-settings")),class:"bg-purple-100 hover:bg-purple-200 text-purple-700 px-5 py-2.5 rounded-xl transition-all duration-200 flex items-center shadow-sm hover:shadow-md font-serif"},t[7]||(t[7]=[(0,n.Lk)("span",{class:"mr-2 font-serif"},"🎵",-1),(0,n.eW)(" 语音设置 ",-1)])),(0,n.Lk)("button",{onClick:t[1]||(t[1]=t=>e.$emit("reset")),class:"bg-gray-100 hover:bg-gray-200 text-gray-700 px-5 py-2.5 rounded-xl transition-all duration-200 flex items-center shadow-sm hover:shadow-md font-serif"},t[8]||(t[8]=[(0,n.Lk)("span",{class:"mr-2 font-serif"},"🔄",-1),(0,n.eW)(" 重置对话 ",-1)]))])]),(0,n.Lk)("div",O,[((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(s.messages,e=>((0,n.uX)(),(0,n.CE)("div",{key:e.id,class:(0,a.C4)(["flex items-start space-x-4",e.isUser?"justify-end":""])},[e.isUser?((0,n.uX)(),(0,n.CE)(n.FK,{key:0},[(0,n.Lk)("div",N,[(0,n.Lk)("p",U,(0,a.v_)(e.content),1)]),t[9]||(t[9]=(0,n.Lk)("div",{class:"w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full flex items-center justify-center text-sm font-medium shadow-lg flex-shrink-0 font-serif"}," 用户 ",-1))],64)):((0,n.uX)(),(0,n.CE)(n.FK,{key:1},[t[10]||(t[10]=(0,n.Lk)("div",{class:"w-10 h-10 bg-gradient-to-r from-gray-400 to-gray-500 text-white rounded-full flex items-center justify-center text-lg shadow-lg flex-shrink-0 font-serif"}," 🤖 ",-1)),(0,n.Lk)("div",W,[(0,n.Lk)("div",{class:"text-gray-800 text-sm leading-relaxed font-serif markdown-content",innerHTML:i.renderMarkdown(e.content)},null,8,Q)])],64))],2))),128))],512),(0,n.Lk)("div",J,[(0,n.Lk)("div",H,[(0,n.Lk)("input",{value:s.messageInput,onInput:t[2]||(t[2]=t=>e.$emit("input",t.target.value)),onKeypress:t[3]||(t[3]=(0,o.jR)(t=>e.$emit("send"),["enter"])),type:"text",placeholder:"输入消息...",class:"flex-1 px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-serif"},null,40,G),(0,n.bF)(c,{onVoiceMessage:i.handleVoiceMessage},null,8,["onVoiceMessage"]),(0,n.Lk)("button",{onClick:t[4]||(t[4]=t=>e.$emit("send")),class:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-xl transition-colors font-serif"}," 发送 "),(0,n.Lk)("button",{onClick:t[5]||(t[5]=t=>e.$emit("dial")),class:"bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-xl transition-colors font-serif"}," 拨号 ")])])])}var Z=s(1451),Y=s.n(Z);const ee={class:"voice-recorder"},te={class:"mr-2"},se={key:0,class:"mt-2 text-sm text-green-600"};function oe(e,t,s,o,r,l){return(0,n.uX)(),(0,n.CE)("div",ee,[(0,n.Lk)("button",{onClick:t[0]||(t[0]=(...e)=>l.toggleWebRTCCall&&l.toggleWebRTCCall(...e)),class:(0,a.C4)([l.webrtcCallClass,"px-6 py-3 rounded-xl transition-all duration-200 font-serif flex items-center"])},[(0,n.Lk)("span",te,(0,a.v_)(r.isWebRTCConnected?"📞":"📱"),1),(0,n.eW)(" "+(0,a.v_)(r.isWebRTCConnected?"挂断通话":"语音通话"),1)],2),r.isWebRTCConnected?((0,n.uX)(),(0,n.CE)("div",se," 通话中... ")):(0,n.Q3)("",!0)])}var ne={name:"VoiceRecorder",emits:["voice-message"],data(){return{currentAssistantId:null,isWebRTCConnected:!1,webrtcSocket:null,peerConnection:null,pendingCandidates:[],localStream:null,lastAsrResult:null,isTTSPlaying:!1}},computed:{webrtcCallClass(){return this.isWebRTCConnected?"bg-red-600/80 hover:bg-red-700/90 text-white animate-pulse backdrop-blur-sm border border-white/20":"bg-gradient-to-r from-purple-500/80 via-pink-500/80 via-blue-500/80 via-indigo-500/80 to-violet-500/80 hover:from-purple-600/90 hover:via-pink-600/90 hover:via-blue-600/90 hover:via-indigo-600/90 hover:to-violet-600/90 text-white backdrop-blur-sm border border-white/20"}},methods:{async toggleWebRTCCall(){this.isWebRTCConnected?this.disconnectWebRTC():await this.connectWebRTC()},async connectWebRTC(){const e=this.$parent.currentSelectedAssistant;if(e)try{console.log("[WebRTC] 开始连接..."),this.webrtcSocket=new WebSocket("ws://localhost:8080/ws"),this.webrtcSocket.onopen=async()=>{console.log("[WebSocket] 已连接"),await this.initWebRTC()},this.webrtcSocket.onmessage=async e=>{const t=JSON.parse(e.data);await this.handleWebRTCMessage(t)},this.webrtcSocket.onerror=e=>{console.error("[WebSocket] 连接出错:",e),this.isWebRTCConnected=!1},this.webrtcSocket.onclose=()=>{console.log("[WebSocket] 连接关闭"),this.isWebRTCConnected=!1}}catch(t){console.error("[WebRTC] 连接失败:",t),alert("WebRTC连接失败，请检查网络")}else alert("请先选择一个助手")},async initWebRTC(){try{this.peerConnection=new RTCPeerConnection({iceServers:[{urls:"stun:stun.l.google.com:19302"}]}),this.localStream=await navigator.mediaDevices.getUserMedia({audio:{echoCancellation:!0}}),this.localStream.getTracks().forEach(e=>{this.peerConnection.addTrack(e,this.localStream)}),this.peerConnection.onicecandidate=e=>{e.candidate&&this.webrtcSocket&&this.webrtcSocket.send(JSON.stringify({type:"ice-candidate",candidate:e.candidate}))},this.peerConnection.ontrack=e=>{const t=new Audio;t.srcObject=e.streams[0],t.play().catch(e=>{console.error("[WebRTC] 播放远端音频失败:",e)})},this.peerConnection.onconnectionstatechange=()=>{switch(this.peerConnection.connectionState){case"connected":console.log("[WebRTC] 已连接"),this.isWebRTCConnected=!0;break;case"disconnected":case"failed":case"closed":console.log("[WebRTC] 连接关闭/失败"),this.isWebRTCConnected=!1;break}};const e=await this.peerConnection.createOffer();await this.peerConnection.setLocalDescription(e),this.webrtcSocket.send(JSON.stringify({type:"offer",sdp:e.sdp}))}catch(e){console.error("[WebRTC] 初始化失败:",e),alert("无法访问麦克风，请检查权限设置")}},async handleWebRTCMessage(e){switch(e.type){case"answer":if(this.peerConnection&&e.sdp){const s=new RTCSessionDescription({type:"answer",sdp:e.sdp});await this.peerConnection.setRemoteDescription(s),console.log("[WebRTC] 已设置远端 SDP answer");for(const e of this.pendingCandidates)try{await this.peerConnection.addIceCandidate(new RTCIceCandidate(e)),console.log("[WebRTC] 添加缓存 ICE 候选成功")}catch(t){console.error("[WebRTC] 添加缓存 ICE 候选失败:",t)}this.pendingCandidates=[]}break;case"asrFinal":if(e.text&&"你好 - "!==e.text){const t=this.findAppComponent();t&&t.addMessage({id:Date.now(),content:e.text,isUser:!0,messageType:"voice",timestamp:new Date})}break;case"aiResponse":if(e.text){const t=this.findAppComponent();t&&t.addMessage({id:Date.now()+1,content:e.text,isUser:!1,messageType:"voice",timestamp:new Date})}break;case"ice-candidate":if(this.peerConnection){const s=new RTCIceCandidate(e.candidate);if(this.peerConnection.remoteDescription&&this.peerConnection.remoteDescription.type)try{await this.peerConnection.addIceCandidate(s),console.log("[WebRTC] 添加 ICE 候选成功")}catch(t){console.error("[WebRTC] 添加 ICE 候选失败:",t)}else this.pendingCandidates.push(e.candidate),console.log("[WebRTC] 缓存 ICE 候选，等待 remoteDescription 设置")}break}},disconnectWebRTC(){console.log("[WebRTC] 断开连接..."),this.localStream&&(this.localStream.getTracks().forEach(e=>e.stop()),this.localStream=null),this.peerConnection&&(this.peerConnection.close(),this.peerConnection=null),this.webrtcSocket&&(this.webrtcSocket.close(),this.webrtcSocket=null),this.isWebRTCConnected=!1,this.pendingCandidates=[];const e=this.findAppComponent();e&&e.saveConversation()},findAppComponent(){let e=this.$parent;while(e){if("App"===e.$options.name)return e;e=e.$parent}return null}},beforeUnmount(){this.disconnectWebRTC()}};const ae=(0,_.A)(ne,[["render",oe]]);var re=ae;const le=new(Y())({breaks:!0,linkify:!0});var ie={name:"ChatArea",components:{VoiceRecorder:re},props:{currentSelectedAssistant:{type:Object,default:null},messages:{type:Array,default:()=>[]},messageInput:{type:String,default:""}},emits:["reset","input","send","dial","add-message","voice-settings"],methods:{renderMarkdown(e){return le.render(e)},handleVoiceMessage(e){switch(console.log("ChatArea收到语音消息:",e),e.type){case"asr_result":console.log("收到ASR识别结果:",e.data),this.$emit("add-message",{id:Date.now(),content:e.data,isUser:!0,timestamp:new Date});break;case"llm_result":console.log("收到LLM回复:",e.data),this.$emit("add-message",{id:Date.now()+1,content:e.data,isUser:!1,timestamp:new Date});break;case"tts_result":console.log("收到TTS音频URL:",e.data),this.playAudio(e.data);break;case"error":console.error("语音处理错误:",e.data),this.$emit("add-message",{id:Date.now(),content:`错误: ${e.data}`,isUser:!1,timestamp:new Date});break;default:console.log("未知消息类型:",e.type)}},async playAudio(e){console.log("正在播放音频:",e);try{const t=new Audio(e);t.oncanplaythrough=()=>{console.log("音频可以完整播放"),t.play()},t.onerror=e=>{console.error("音频播放错误:",e)},t.onended=()=>{console.log("音频播放完成")},t.load()}catch(t){console.error("音频播放准备失败:",t)}}},watch:{messages:{handler(){this.$nextTick(()=>{const e=this.$refs.chatContainer;e&&(e.scrollTop=e.scrollHeight)})},deep:!0}}};const ce=(0,_.A)(ie,[["render",q],["__scopeId","data-v-9d7a3b3e"]]);var de=ce;const ge={class:"bg-white rounded-2xl shadow-2xl w-96 p-8 transform transition-all duration-300 scale-100"},ue={class:"text-xl font-bold text-gray-900 mb-6 flex items-center font-serif"},he={class:"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white text-sm mr-3 font-serif"},me={class:"space-y-6"},pe=["value"],be=["value"],fe={class:"flex justify-end space-x-4 mt-8"},xe={class:"bg-white rounded-2xl shadow-2xl w-[500px] p-8 transform transition-all duration-300 scale-100"},ye={class:"text-xl font-bold text-gray-900 mb-6 flex items-center font-serif"},ke={class:"space-y-6"},ve=["value"],we={class:"flex justify-end space-x-4 mt-8"},Le={class:"bg-white rounded-2xl shadow-2xl w-96 p-8 transform transition-all duration-300 scale-100"},Ce={class:"mb-6"},Se={class:"text-gray-700 text-sm leading-relaxed font-serif"},Te={class:"font-semibold text-red-600 font-serif"},Ee={class:"flex justify-end space-x-4"},Me={class:"bg-white rounded-3xl shadow-2xl w-80 p-8 transform transition-all duration-300 scale-100"},Ae={class:"flex justify-between items-center mb-8"},Be={class:"flex items-center"},Ke={class:"text-gray-700 text-lg font-medium font-serif"},Ie={class:"grid grid-cols-3 gap-4 mb-8"},_e=["onClick"],je={class:"flex flex-col items-center space-y-4"},De={class:"flex justify-center space-x-6 w-full"},Fe={class:"bg-white rounded-3xl shadow-2xl w-80 p-8 transform transition-all duration-300 scale-100"},$e={class:"flex justify-between items-center mb-8"},Pe={class:"flex items-center"},Re={class:"text-gray-700 text-lg font-medium font-serif"},Xe={class:"grid grid-cols-3 gap-4 mb-8"},Ve=["onClick"],ze={class:"flex flex-col items-center space-y-4"},Oe={class:"flex justify-center space-x-6 w-full"};function Ne(e,t,s,r,l,i){const c=(0,n.g2)("VoiceRecorder");return(0,n.uX)(),(0,n.CE)(n.FK,null,[s.showAddModal?((0,n.uX)(),(0,n.CE)("div",{key:0,class:"fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50",onClick:t[4]||(t[4]=(0,o.D$)(t=>e.$emit("close-add"),["self"]))},[(0,n.Lk)("div",ge,[(0,n.Lk)("h3",ue,[(0,n.Lk)("span",he,(0,a.v_)(s.isEditing?"✎":"+"),1),(0,n.eW)(" "+(0,a.v_)(s.isEditing?"编辑助手":"新增助手"),1)]),(0,n.Lk)("div",me,[(0,n.Lk)("div",null,[t[20]||(t[20]=(0,n.Lk)("label",{class:"block text-sm font-semibold text-gray-700 mb-3 font-serif"},"助手名称",-1)),(0,n.Lk)("input",{value:s.currentAssistant.name,onInput:t[0]||(t[0]=t=>e.$emit("update-name",t.target.value)),type:"text",placeholder:"请输入助手名称",class:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white font-serif"},null,40,pe)]),(0,n.Lk)("div",null,[t[21]||(t[21]=(0,n.Lk)("label",{class:"block text-sm font-semibold text-gray-700 mb-3 font-serif"},"助手描述",-1)),(0,n.Lk)("textarea",{value:s.currentAssistant.description,onInput:t[1]||(t[1]=t=>e.$emit("update-description",t.target.value)),placeholder:"请输入助手描述",rows:"4",class:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none transition-all duration-200 bg-gray-50 focus:bg-white font-serif"},null,40,be)])]),(0,n.Lk)("div",fe,[(0,n.Lk)("button",{onClick:t[2]||(t[2]=t=>e.$emit("save")),class:"px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 font-serif"},(0,a.v_)(s.isEditing?"保存":"确定"),1),(0,n.Lk)("button",{onClick:t[3]||(t[3]=t=>e.$emit("close-add")),class:"px-6 py-3 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white font-medium rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 font-serif"}," 取消 ")])])])):(0,n.Q3)("",!0),s.showSettingsModal?((0,n.uX)(),(0,n.CE)("div",{key:1,class:"fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50",onClick:t[8]||(t[8]=(0,o.D$)(t=>e.$emit("close-settings"),["self"]))},[(0,n.Lk)("div",xe,[(0,n.Lk)("h3",ye,[t[22]||(t[22]=(0,n.Lk)("span",{class:"w-8 h-8 bg-gradient-to-r from-green-500 to-blue-600 rounded-lg flex items-center justify-center text-white text-sm mr-3 font-serif"}," ⚙️ ",-1)),(0,n.eW)(" "+(0,a.v_)(s.currentSettingsAssistant?.name)+" - 人设配置 ",1)]),(0,n.Lk)("div",ke,[(0,n.Lk)("div",null,[t[23]||(t[23]=(0,n.Lk)("label",{class:"block text-sm font-semibold text-gray-700 mb-3 font-serif"},"人设配置",-1)),(0,n.Lk)("textarea",{value:s.currentPersonality,onInput:t[5]||(t[5]=t=>e.$emit("update-personality",t.target.value)),placeholder:"请输入助手的人设配置，例如：你是一个专业的客服助手，性格温和，善于解决问题...",rows:"8",class:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none transition-all duration-200 bg-gray-50 focus:bg-white font-serif"},null,40,ve)])]),(0,n.Lk)("div",we,[(0,n.Lk)("button",{onClick:t[6]||(t[6]=t=>e.$emit("save-personality")),class:"px-6 py-3 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-medium rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 font-serif"}," 保存 "),(0,n.Lk)("button",{onClick:t[7]||(t[7]=t=>e.$emit("close-settings")),class:"px-6 py-3 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white font-medium rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 font-serif"}," 取消 ")])])])):(0,n.Q3)("",!0),s.showDeleteModal?((0,n.uX)(),(0,n.CE)("div",{key:2,class:"fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50",onClick:t[11]||(t[11]=(0,o.D$)(t=>e.$emit("close-delete"),["self"]))},[(0,n.Lk)("div",Le,[t[27]||(t[27]=(0,n.Lk)("h3",{class:"text-xl font-bold text-gray-900 mb-6 flex items-center font-serif"},[(0,n.Lk)("span",{class:"w-8 h-8 bg-gradient-to-r from-red-500 to-red-600 rounded-lg flex items-center justify-center text-white text-sm mr-3 font-serif"}," ⚠️ "),(0,n.eW)(" 确认删除 ")],-1)),(0,n.Lk)("div",Ce,[(0,n.Lk)("p",Se,[t[24]||(t[24]=(0,n.eW)(" 确定要删除助手 ",-1)),(0,n.Lk)("span",Te,'"'+(0,a.v_)(s.deleteAssistantName)+'"',1),t[25]||(t[25]=(0,n.eW)(" 吗？ ",-1))]),t[26]||(t[26]=(0,n.Lk)("p",{class:"text-gray-500 text-xs mt-2 font-serif"}," 删除后将无法恢复，包括所有对话记录和人设配置。 ",-1))]),(0,n.Lk)("div",Ee,[(0,n.Lk)("button",{onClick:t[9]||(t[9]=t=>e.$emit("confirm-delete")),class:"px-6 py-3 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-medium rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 font-serif"}," 确认删除 "),(0,n.Lk)("button",{onClick:t[10]||(t[10]=t=>e.$emit("close-delete")),class:"px-6 py-3 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white font-medium rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 font-serif"}," 取消 ")])])])):(0,n.Q3)("",!0),s.showDialModal?((0,n.uX)(),(0,n.CE)("div",{key:3,class:"fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50",onClick:t[15]||(t[15]=(0,o.D$)(t=>e.$emit("close-dial"),["self"]))},[(0,n.Lk)("div",Me,[(0,n.Lk)("div",Ae,[(0,n.Lk)("div",Be,[t[28]||(t[28]=(0,n.Lk)("span",{class:"text-gray-300 mr-3 text-lg font-serif"},"|",-1)),(0,n.Lk)("span",Ke,(0,a.v_)(s.dialNumber||"请输入号码"),1)]),(0,n.Lk)("button",{onClick:t[12]||(t[12]=t=>e.$emit("close-dial")),class:"text-gray-400 hover:text-gray-600 text-2xl w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-all duration-200 font-serif"}," × ")]),(0,n.Lk)("div",Ie,[((0,n.uX)(),(0,n.CE)(n.FK,null,(0,n.pI)([1,2,3,4,5,6,7,8,9,"*",0,"#"],t=>(0,n.Lk)("button",{key:t,onClick:s=>e.$emit("add-number",t),class:"w-16 h-16 bg-gradient-to-b from-gray-50 to-gray-100 hover:from-blue-50 hover:to-blue-100 rounded-2xl flex items-center justify-center text-xl font-semibold text-gray-700 hover:text-blue-700 transition-all duration-200 mx-auto shadow-sm hover:shadow-md transform hover:-translate-y-0.5 font-serif"},(0,a.v_)(t),9,_e)),64))]),(0,n.Lk)("div",je,[(0,n.bF)(c,{ref:"voiceRecorderRef",onVoiceMessage:i.handleVoiceMessage,class:"hidden"},null,8,["onVoiceMessage"]),(0,n.Lk)("div",De,[(0,n.Lk)("button",{onClick:t[13]||(t[13]=(...e)=>i.toggleCall&&i.toggleCall(...e)),class:(0,a.C4)([l.isCalling?"bg-red-600/80 hover:bg-red-700/90 animate-pulse":"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700","w-14 h-14 text-white rounded-full flex items-center justify-center transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 font-serif"])},(0,a.v_)(l.isCalling?"正在通话...":"📞 拨号"),3),(0,n.Lk)("button",{onClick:t[14]||(t[14]=t=>e.$emit("clear-number")),class:"w-14 h-14 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-full flex items-center justify-center transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 font-serif"}," 🔢 ")])])])])):(0,n.Q3)("",!0),s.showDialModal?((0,n.uX)(),(0,n.CE)("div",{key:4,class:"fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50",onClick:t[19]||(t[19]=(0,o.D$)(t=>e.$emit("close-dial"),["self"]))},[(0,n.Lk)("div",Fe,[(0,n.Lk)("div",$e,[(0,n.Lk)("div",Pe,[t[29]||(t[29]=(0,n.Lk)("span",{class:"text-gray-300 mr-3 text-lg font-serif"},"|",-1)),(0,n.Lk)("span",Re,(0,a.v_)(s.dialNumber||"请输入号码"),1)]),(0,n.Lk)("button",{onClick:t[16]||(t[16]=t=>e.$emit("close-dial")),class:"text-gray-400 hover:text-gray-600 text-2xl w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-all duration-200 font-serif"}," × ")]),(0,n.Lk)("div",Xe,[((0,n.uX)(),(0,n.CE)(n.FK,null,(0,n.pI)([1,2,3,4,5,6,7,8,9,"*",0,"#"],t=>(0,n.Lk)("button",{key:t,onClick:s=>e.$emit("add-number",t),class:"w-16 h-16 bg-gradient-to-b from-gray-50 to-gray-100 hover:from-blue-50 hover:to-blue-100 rounded-2xl flex items-center justify-center text-xl font-semibold text-gray-700 hover:text-blue-700 transition-all duration-200 mx-auto shadow-sm hover:shadow-md transform hover:-translate-y-0.5 font-serif"},(0,a.v_)(t),9,Ve)),64))]),(0,n.Lk)("div",ze,[(0,n.bF)(c,{ref:"voiceRecorderRef",onVoiceMessage:i.handleVoiceMessage,class:"hidden"},null,8,["onVoiceMessage"]),(0,n.Lk)("div",Oe,[(0,n.Lk)("button",{onClick:t[17]||(t[17]=(...e)=>i.toggleCall&&i.toggleCall(...e)),class:(0,a.C4)([l.isCalling?"bg-red-600/80 hover:bg-red-700/90 animate-pulse":"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700","w-14 h-14 text-white rounded-full flex items-center justify-center transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 font-serif"])},(0,a.v_)(l.isCalling?"正在通话...":"📞 拨号"),3),(0,n.Lk)("button",{onClick:t[18]||(t[18]=t=>e.$emit("clear-number")),class:"w-14 h-14 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-full flex items-center justify-center transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 font-serif"}," 🔢 ")])])])])):(0,n.Q3)("",!0)],64)}var Ue={name:"ModalComponents",components:{VoiceRecorder:re},props:{showAddModal:Boolean,showSettingsModal:Boolean,showDeleteModal:Boolean,showDialModal:Boolean,showChatSettingsModal:Boolean,isEditing:Boolean,currentAssistant:Object,currentSettingsAssistant:Object,currentPersonality:String,deleteAssistantName:String,dialNumber:String,chatSettings:Object,assistants:Array,currentSelectedAssistant:{type:Object,default:null}},emits:["close-add","update-name","update-description","save","close-settings","update-personality","save-personality","close-delete","confirm-delete","close-dial","add-number","make-call","clear-number","close-chat-settings","save-chat-settings","update-chat-setting","voice-message","select-robot"],data(){return{isCalling:!1}},methods:{handleVoiceMessage(e){this.$emit("voice-message",e),"call_ended"===e.type&&(this.isCalling=!1)},async toggleCall(){if(this.isCalling)this.endCall();else{if(!this.dialNumber)return void alert("请输入编号");const e=parseInt(this.dialNumber,10);if(isNaN(e))return void alert("请输入有效的数字编号");const t=this.assistants.find(t=>t.id===e);if(!t)return void alert("该机器人不存在，请重新输入");this.$emit("select-robot",t),this.$refs.voiceRecorderRef&&(await this.$refs.voiceRecorderRef.toggleWebRTCCall(t),this.isCalling=!0)}},endCall(){this.$refs.voiceRecorderRef&&(this.$refs.voiceRecorderRef.disconnectWebRTC(),this.isCalling=!1)}}};const We=(0,_.A)(Ue,[["render",Ne]]);var Qe=We;const Je={key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},He={class:"bg-white rounded-2xl shadow-2xl w-4/5 h-4/5 max-w-6xl flex"},Ge={class:"w-1/3 border-r border-gray-200 p-6"},qe={class:"flex items-center justify-between mb-6"},Ze=["value"],Ye={class:"space-y-3 overflow-y-auto max-h-96",style:{"scroll-behavior":"smooth"}},et=["onClick"],tt={class:"font-serif font-medium text-gray-900"},st={class:"text-sm text-gray-600 mt-1"},ot={class:"text-sm text-gray-500 mt-1 truncate"},nt={class:"flex-1 p-6 flex flex-col"},at={key:0,class:"flex-1"},rt={class:"text-lg font-serif font-semibold mb-4"},lt={class:"flex-1 overflow-y-auto space-y-4 max-h-96",style:{"scroll-behavior":"smooth"}},it={class:"flex items-center mb-1"},ct={key:0,class:"text-xs mr-1"},dt={class:"font-serif"},gt={class:"text-xs mt-1 opacity-70"},ut={class:"mt-4 flex space-x-3"},ht={key:1,class:"flex-1 flex items-center justify-center text-gray-500 font-serif"};function mt(e,t,s,r,l,i){return s.show?((0,n.uX)(),(0,n.CE)("div",Je,[(0,n.Lk)("div",He,[(0,n.Lk)("div",Ge,[(0,n.Lk)("div",qe,[t[6]||(t[6]=(0,n.Lk)("h3",{class:"text-xl font-serif font-semibold text-gray-900"},"历史对话",-1)),(0,n.Lk)("button",{onClick:t[0]||(t[0]=t=>e.$emit("close")),class:"text-gray-500 hover:text-gray-700"},t[5]||(t[5]=[(0,n.Lk)("span",{class:"text-2xl"},"×",-1)]))]),(0,n.bo)((0,n.Lk)("select",{"onUpdate:modelValue":t[1]||(t[1]=e=>l.selectedAssistantId=e),onChange:t[2]||(t[2]=(...e)=>i.fetchHistoryList&&i.fetchHistoryList(...e)),class:"w-full mb-4 px-3 py-2 border border-gray-300 rounded-lg font-serif"},[t[7]||(t[7]=(0,n.Lk)("option",{value:""},"所有助手",-1)),((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(s.assistants,e=>((0,n.uX)(),(0,n.CE)("option",{key:e.id,value:e.id},(0,a.v_)(e.name),9,Ze))),128))],544),[[o.u1,l.selectedAssistantId]]),(0,n.Lk)("div",Ye,[((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(l.historyList,e=>((0,n.uX)(),(0,n.CE)("div",{key:e.id,onClick:t=>i.selectHistory(e),class:(0,a.C4)(["p-4 rounded-lg cursor-pointer transition-all duration-200 ease-in-out transform hover:scale-[1.02]",l.selectedHistory?.id===e.id?"bg-blue-100 border-blue-300 shadow-md":"bg-gray-50 hover:bg-gray-100 hover:shadow-sm"])},[(0,n.Lk)("div",tt,(0,a.v_)(e.assistant_name),1),(0,n.Lk)("div",st,(0,a.v_)(i.formatDate(e.created_at)),1),(0,n.Lk)("div",ot,(0,a.v_)(e.first_message),1)],10,et))),128))])]),(0,n.Lk)("div",nt,[l.selectedHistory?((0,n.uX)(),(0,n.CE)("div",at,[(0,n.Lk)("h4",rt," 与 "+(0,a.v_)(l.selectedHistory.assistant_name)+" 的对话 ",1),(0,n.Lk)("div",lt,[((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(l.historyMessages,e=>((0,n.uX)(),(0,n.CE)("div",{key:e.id,class:(0,a.C4)(["flex transition-all duration-300 ease-in-out",e.is_user?"justify-end":"justify-start"])},[(0,n.Lk)("div",{class:(0,a.C4)(["max-w-xs lg:max-w-md px-4 py-2 rounded-lg transform transition-all duration-200 ease-in-out hover:scale-[1.02]",e.is_user?"bg-blue-600 text-white":"bg-gray-200 text-gray-900"])},[(0,n.Lk)("div",it,["voice"===e.message_type?((0,n.uX)(),(0,n.CE)("span",ct,(0,a.v_)(e.is_user?"🎤":"🔊"),1)):(0,n.Q3)("",!0),(0,n.Lk)("span",dt,(0,a.v_)(e.content),1)]),(0,n.Lk)("div",gt,(0,a.v_)(i.formatTime(e.created_at)),1)],2)],2))),128))]),(0,n.Lk)("div",ut,[(0,n.Lk)("button",{onClick:t[3]||(t[3]=(...e)=>i.restoreConversation&&i.restoreConversation(...e)),class:"bg-gradient-to-r from-green-400/80 via-emerald-400/80 via-teal-400/80 via-cyan-400/80 to-blue-400/80 hover:from-green-500/90 hover:via-emerald-500/90 hover:via-teal-500/90 hover:via-cyan-500/90 hover:to-blue-500/90 text-white px-4 py-2 rounded-lg font-serif transition-all duration-200 ease-in-out transform hover:scale-105 hover:shadow-lg active:scale-95"}," 恢复此对话 "),(0,n.Lk)("button",{onClick:t[4]||(t[4]=(...e)=>i.deleteHistory&&i.deleteHistory(...e)),class:"bg-gradient-to-r from-red-400/80 via-pink-400/80 via-rose-400/80 via-orange-400/80 to-yellow-400/80 hover:from-red-500/90 hover:via-pink-500/90 hover:via-rose-500/90 hover:via-orange-500/90 hover:to-yellow-500/90 text-white px-4 py-2 rounded-lg font-serif transition-all duration-200 ease-in-out transform hover:scale-105 hover:shadow-lg active:scale-95"}," 删除记录 ")])])):((0,n.uX)(),(0,n.CE)("div",ht," 请选择一个对话记录查看详情 "))])])])):(0,n.Q3)("",!0)}var pt={name:"HistoryModal",props:{show:Boolean,assistants:Array},emits:["close","restore-conversation"],data(){return{selectedAssistantId:"",historyList:[],selectedHistory:null,historyMessages:[],isLoading:!1}},methods:{async fetchHistoryList(){if(!this.isLoading){this.isLoading=!0;try{const e=await fetch("http://localhost:8080/api/history/list",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({assistant_id:this.selectedAssistantId||null})}),t=await e.json();200===t.code&&(this.historyList=t.data||[])}catch(e){console.error("获取历史对话列表失败:",e)}finally{this.isLoading=!1}}},async selectHistory(e){if(this.selectedHistory?.id!==e.id){this.selectedHistory=e,this.historyMessages=[];try{const t=await fetch("http://localhost:8080/api/history/messages",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({conversation_id:e.id})}),s=await t.json();200===s.code&&(this.historyMessages=s.data||[],this.$nextTick(()=>{const e=document.querySelector(".max-h-96.overflow-y-auto");e&&(e.scrollTop=e.scrollHeight)}))}catch(t){console.error("获取历史消息失败:",t)}}},async deleteHistory(){if(this.selectedHistory&&confirm("确定要删除这个对话记录吗？"))try{const e=await fetch("http://localhost:8080/api/history/delete",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({conversation_id:this.selectedHistory.id})}),t=await e.json();200===t.code&&(await this.fetchHistoryList(),this.selectedHistory=null,this.historyMessages=[])}catch(e){console.error("删除历史记录失败:",e)}},restoreConversation(){if(this.selectedHistory&&this.historyMessages.length>0){const e=this.assistants.some(e=>e.id==this.selectedHistory.assistant_id);if(!e){const e=confirm(`助手 "${this.selectedHistory.assistant_name}" 已被删除，是否仍要恢复此对话？`);if(!e)return}this.$emit("restore-conversation",{assistant:{id:this.selectedHistory.assistant_id,name:this.selectedHistory.assistant_name},messages:this.historyMessages}),this.$emit("close")}},formatDate(e){return new Date(e).toLocaleDateString("zh-CN")},formatTime(e){return new Date(e).toLocaleTimeString("zh-CN")}},watch:{show:{handler(e){e?this.$nextTick(()=>{this.fetchHistoryList()}):(this.selectedHistory=null,this.historyMessages=[],this.selectedAssistantId="")},immediate:!1}}};const bt=(0,_.A)(pt,[["render",mt],["__scopeId","data-v-52975cb8"]]);var ft=bt;const xt={class:"bg-white rounded-2xl shadow-2xl w-[1200px] h-[800px] transform transition-all duration-300 scale-100 flex flex-col"},yt={class:"flex items-center justify-between p-6 border-b border-gray-200"},kt={class:"flex items-center"},vt={class:"text-xl font-bold text-gray-900"},wt={class:"flex flex-1 overflow-hidden"},Lt={class:"w-64 bg-gray-50 border-r border-gray-200 p-4"},Ct={class:"space-y-2"},St=["onClick"],Tt={class:"text-lg"},Et={class:"font-medium"},Mt={class:"flex-1 flex flex-col overflow-hidden"},At={key:0,class:"flex-1 p-6 overflow-y-auto"},Bt={class:"max-w-4xl space-y-8"},Kt={class:"bg-white border border-gray-200 rounded-xl p-6"},It={class:"flex items-center justify-between mb-4"},_t={class:"bg-white border border-gray-200 rounded-xl p-6"},jt={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},Dt={class:"block text-sm font-semibold text-gray-700 mb-2"},Ft={class:"block text-sm font-semibold text-gray-700 mb-2"},$t={key:1,class:"flex-1 p-6 overflow-y-auto"},Pt={class:"max-w-4xl"},Rt={class:"bg-white border border-gray-200 rounded-xl p-6"},Xt={class:"space-y-6"},Vt={class:"mb-4"},zt={class:"flex space-x-4"},Ot={class:"flex items-center"},Nt={class:"flex items-center"},Ut={class:"mb-4"},Wt={class:"flex space-x-4"},Qt={class:"flex items-center"},Jt={class:"flex items-center"},Ht={class:"flex items-center"},Gt={class:"mb-4"},qt={class:"flex items-center space-x-4"},Zt={class:"w-12 h-8 border border-gray-300 rounded text-center text-sm flex items-center justify-center"},Yt={class:"mb-6"},es={class:"flex items-center space-x-4"},ts={class:"w-16 h-8 border border-gray-300 rounded text-center text-sm flex items-center justify-center"},ss={class:"space-y-4"},os={class:"flex items-center justify-between"},ns={class:"relative inline-flex items-center cursor-pointer"},as={class:"flex items-center justify-between"},rs={class:"relative inline-flex items-center cursor-pointer"},ls={class:"mt-6 space-y-4"},is={key:0,class:"bg-gray-50 border border-gray-200 rounded-xl p-6"},cs={class:"flex items-center justify-between mb-6"},ds={class:"flex items-center"},gs={class:"text-lg font-semibold text-gray-900"},us={class:"flex items-center space-x-4 mb-4"},hs={class:"relative flex-1"},ms={key:0,class:"space-y-3 max-h-60 overflow-y-auto"},ps={class:"flex items-center space-x-3"},bs={class:"font-semibold text-gray-900 text-sm"},fs={class:"text-xs text-gray-600"},xs={class:"flex items-center space-x-3 mt-1"},ys={class:"text-xs text-gray-500"},ks={class:"text-xs text-gray-500"},vs={class:"flex space-x-2"},ws=["onClick"],Ls=["onClick"],Cs={class:"relative"},Ss=["onClick"],Ts={key:0,class:"absolute right-0 top-full mt-1 w-32 bg-white border border-gray-200 rounded-lg shadow-lg z-50"},Es=["onClick"],Ms=["onClick"],As={key:1,class:"text-center py-8"},Bs={key:1,class:"bg-gray-50 border border-gray-200 rounded-xl p-6"},Ks={class:"flex items-center mb-6"},Is={class:"mb-4"},_s={class:"space-y-2"},js={class:"mb-4"},Ds={class:"grid grid-cols-3 gap-2"},Fs={class:"space-y-3"},$s={class:"text-right text-xs text-gray-500 mt-1"},Ps={class:"text-right text-xs text-gray-500 mt-1"},Rs={class:"flex justify-end space-x-2 pt-2"},Xs=["disabled"],Vs={key:2},zs={class:"bg-white border border-gray-200 rounded-xl p-6"},Os={class:"flex items-center justify-between mb-4"},Ns={class:"bg-white border border-gray-200 rounded-xl p-6"},Us={class:"flex items-center justify-between mb-4"},Ws={class:"bg-white border border-gray-200 rounded-xl p-6"},Qs={class:"flex items-center justify-between mb-4"},Js={key:2,class:"flex-1 p-6 overflow-y-auto"},Hs={class:"max-w-4xl"},Gs={class:"bg-white border border-gray-200 rounded-xl p-6 h-full"},qs={class:"grid grid-cols-1 lg:grid-cols-2 gap-6 h-full"},Zs={class:"border border-gray-200 rounded-xl p-4"},Ys={class:"bg-gray-50 rounded-lg p-4 h-64 overflow-y-auto mb-4"},eo={class:"flex space-x-2"},to={class:"border border-gray-200 rounded-xl p-4"},so={class:"bg-gray-50 rounded-lg p-4 h-64 overflow-y-auto"},oo={class:"text-sm text-gray-600 space-y-2"},no={class:"mt-4 space-y-2"},ao={class:"flex items-center justify-end space-x-4 p-6 border-t border-gray-200 bg-gray-50"},ro={key:0,class:"absolute -top-12 left-1/2 transform -translate-x-1/2 bg-green-500 text-white px-3 py-1 rounded-lg text-sm whitespace-nowrap shadow-lg"},lo={class:"bg-white rounded-2xl shadow-2xl w-[800px] max-h-[90vh] overflow-hidden transform transition-all duration-300 scale-100 flex flex-col"},io={class:"flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-purple-50 to-blue-50"},co={class:"flex-1 p-6 overflow-y-auto"},go={class:"mb-6"},uo={class:"flex space-x-4 mb-4"},ho={class:"mb-6"},mo={class:"relative"},po=["disabled"],bo={key:0},fo={key:1,class:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"},xo={key:0,class:"mb-6"},yo={class:"bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-xl p-4"},ko={class:"text-sm text-gray-700 whitespace-pre-wrap"},vo={class:"mb-6"},wo={class:"bg-gray-50 border border-gray-200 rounded-xl p-4"},Lo={class:"text-sm text-gray-700 whitespace-pre-wrap"},Co={class:"flex items-center justify-end space-x-4 p-6 border-t border-gray-200 bg-gray-50"},So=["disabled"],To={class:"bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden"},Eo={class:"flex items-center justify-between p-6 border-b border-gray-200"},Mo={class:"p-6 overflow-y-auto max-h-[calc(90vh-120px)]"},Ao={class:"mb-6"},Bo={class:"relative"},Ko={class:"flex flex-wrap gap-2 mb-6"},Io={key:0,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},_o={class:"flex items-center mb-3"},jo={class:"ml-3"},Do={class:"font-semibold text-gray-900 text-sm"},Fo={class:"text-xs text-gray-500"},$o={class:"text-sm text-gray-600 mb-3"},Po={class:"flex items-center justify-between"},Ro={class:"text-xs text-gray-500"},Xo={class:"flex space-x-2"},Vo=["onClick"],zo=["onClick"],Oo={key:1,class:"text-center py-12"},No={class:"bg-white rounded-xl shadow-2xl w-full max-w-md p-6"},Uo={class:"text-gray-700 mb-6"},Wo={class:"flex space-x-3"};function Qo(e,t,s,r,l,i){const c=(0,n.g2)("KnowledgeBaseManager"),d=(0,n.g2)("KnowledgeBaseEditor");return s.show?((0,n.uX)(),(0,n.CE)("div",{key:0,class:"fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50",onClick:t[64]||(t[64]=(0,o.D$)(t=>e.$emit("close"),["self"]))},[(0,n.Lk)("div",xt,[(0,n.Lk)("div",yt,[(0,n.Lk)("div",kt,[t[65]||(t[65]=(0,n.Lk)("div",{class:"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center text-white text-sm mr-3"}," ⚙️ ",-1)),(0,n.Lk)("h3",vt,(0,a.v_)(s.robot?.name)+" - 机器人设置",1)]),(0,n.Lk)("button",{onClick:t[0]||(t[0]=t=>e.$emit("close")),class:"w-8 h-8 rounded-lg hover:bg-gray-100 flex items-center justify-center text-gray-500 hover:text-gray-700 transition-colors"}," ✕ ")]),(0,n.Lk)("div",wt,[(0,n.Lk)("div",Lt,[(0,n.Lk)("nav",Ct,[((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(l.tabs,e=>((0,n.uX)(),(0,n.CE)("button",{key:e.id,onClick:t=>l.activeTab=e.id,class:(0,a.C4)(["w-full text-left px-4 py-3 rounded-lg transition-all duration-200 flex items-center space-x-3",l.activeTab===e.id?"bg-blue-500 text-white shadow-md":"text-gray-600 hover:bg-gray-100 hover:text-gray-900"])},[(0,n.Lk)("span",Tt,(0,a.v_)(e.icon),1),(0,n.Lk)("span",Et,(0,a.v_)(e.name),1)],10,St))),128))])]),(0,n.Lk)("div",Mt,["personality"===l.activeTab?((0,n.uX)(),(0,n.CE)("div",At,[(0,n.Lk)("div",Bt,[(0,n.Lk)("div",Kt,[(0,n.Lk)("div",It,[t[67]||(t[67]=(0,n.Lk)("h4",{class:"text-lg font-semibold text-gray-900 flex items-center"},[(0,n.Lk)("span",{class:"w-6 h-6 bg-purple-500 rounded-lg flex items-center justify-center text-white text-sm mr-3"},"👤"),(0,n.eW)(" 人设配置 ")],-1)),(0,n.Lk)("button",{onClick:t[1]||(t[1]=e=>l.showOptimizeModal=!0),class:"flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-lg hover:from-purple-600 hover:to-blue-600 transition-all duration-200 shadow-md hover:shadow-lg"},t[66]||(t[66]=[(0,n.Lk)("span",{class:"text-sm"},"✨",-1),(0,n.Lk)("span",{class:"text-sm font-medium"},"自动优化提示词",-1)]))]),(0,n.bo)((0,n.Lk)("textarea",{"onUpdate:modelValue":t[2]||(t[2]=e=>l.localPersonality=e),placeholder:"请输入助手的人设配置，例如：你是一个专业的客服助手，性格温和，善于解决问题...",rows:"6",class:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none transition-all duration-200 bg-gray-50 focus:bg-white"},null,512),[[o.Jo,l.localPersonality]])]),(0,n.Lk)("div",_t,[t[77]||(t[77]=(0,n.Lk)("h4",{class:"text-lg font-semibold text-gray-900 mb-6 flex items-center"},[(0,n.Lk)("span",{class:"w-6 h-6 bg-green-500 rounded-lg flex items-center justify-center text-white text-sm mr-3"},"🧠"),(0,n.eW)(" 回复逻辑配置 ")],-1)),(0,n.Lk)("div",jt,[(0,n.Lk)("div",null,[t[68]||(t[68]=(0,n.Lk)("label",{class:"block text-sm font-semibold text-gray-700 mb-2"},"系统角色",-1)),(0,n.bo)((0,n.Lk)("textarea",{"onUpdate:modelValue":t[3]||(t[3]=e=>l.replyLogic.systemRole=e),placeholder:"你是一个专业的对话大模型工程师...",rows:"3",class:"w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"},null,512),[[o.Jo,l.replyLogic.systemRole]])]),(0,n.Lk)("div",null,[t[69]||(t[69]=(0,n.Lk)("label",{class:"block text-sm font-semibold text-gray-700 mb-2"},"对话指令",-1)),(0,n.bo)((0,n.Lk)("textarea",{"onUpdate:modelValue":t[4]||(t[4]=e=>l.replyLogic.dialogCommand=e),placeholder:"请以简洁、专业的方式回答用户的问题...",rows:"3",class:"w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"},null,512),[[o.Jo,l.replyLogic.dialogCommand]])]),(0,n.Lk)("div",null,[t[71]||(t[71]=(0,n.Lk)("label",{class:"block text-sm font-semibold text-gray-700 mb-2"},"回复模式",-1)),(0,n.bo)((0,n.Lk)("select",{"onUpdate:modelValue":t[5]||(t[5]=e=>l.replyLogic.responseMode=e),class:"w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"},t[70]||(t[70]=[(0,n.Lk)("option",{value:"simple"},"简洁模式",-1),(0,n.Lk)("option",{value:"detailed"},"详细模式",-1),(0,n.Lk)("option",{value:"creative"},"创意模式",-1)]),512),[[o.u1,l.replyLogic.responseMode]])]),(0,n.Lk)("div",null,[(0,n.Lk)("label",Dt," 温度 ("+(0,a.v_)(l.replyLogic.temperature)+") ",1),(0,n.bo)((0,n.Lk)("input",{type:"range","onUpdate:modelValue":t[6]||(t[6]=e=>l.replyLogic.temperature=e),min:"0",max:"1",step:"0.1",class:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"},null,512),[[o.Jo,l.replyLogic.temperature,void 0,{number:!0}]])]),(0,n.Lk)("div",null,[t[72]||(t[72]=(0,n.Lk)("label",{class:"block text-sm font-semibold text-gray-700 mb-2"},"最大Token数",-1)),(0,n.bo)((0,n.Lk)("input",{type:"number","onUpdate:modelValue":t[7]||(t[7]=e=>l.replyLogic.maxTokens=e),min:"1",max:"4000",class:"w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"},null,512),[[o.Jo,l.replyLogic.maxTokens,void 0,{number:!0}]])]),(0,n.Lk)("div",null,[t[74]||(t[74]=(0,n.Lk)("label",{class:"block text-sm font-semibold text-gray-700 mb-2"},"语言",-1)),(0,n.bo)((0,n.Lk)("select",{"onUpdate:modelValue":t[8]||(t[8]=e=>l.replyLogic.language=e),class:"w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"},t[73]||(t[73]=[(0,n.Lk)("option",{value:"zh-cn"},"中文",-1),(0,n.Lk)("option",{value:"en"},"English",-1),(0,n.Lk)("option",{value:"ja"},"日本語",-1)]),512),[[o.u1,l.replyLogic.language]])]),(0,n.Lk)("div",null,[t[76]||(t[76]=(0,n.Lk)("label",{class:"block text-sm font-semibold text-gray-700 mb-2"},"发音人",-1)),(0,n.bo)((0,n.Lk)("select",{"onUpdate:modelValue":t[9]||(t[9]=e=>l.replyLogic.speaker=e),class:"w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"},t[75]||(t[75]=[(0,n.Lk)("option",{value:"601002"},"女声-温柔",-1),(0,n.Lk)("option",{value:"601001"},"男声-沉稳",-1),(0,n.Lk)("option",{value:"601003"},"女声-活泼",-1)]),512),[[o.u1,l.replyLogic.speaker]])]),(0,n.Lk)("div",null,[(0,n.Lk)("label",Ft," 语速 ("+(0,a.v_)(l.replyLogic.speechSpeed)+") ",1),(0,n.bo)((0,n.Lk)("input",{type:"range","onUpdate:modelValue":t[10]||(t[10]=e=>l.replyLogic.speechSpeed=e),min:"0.5",max:"2",step:"0.1",class:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"},null,512),[[o.Jo,l.replyLogic.speechSpeed,void 0,{number:!0}]])])])])])])):(0,n.Q3)("",!0),"orchestration"===l.activeTab?((0,n.uX)(),(0,n.CE)("div",$t,[(0,n.Lk)("div",Pt,[(0,n.Lk)("div",Rt,[t[92]||(t[92]=(0,n.Fv)('<div class="flex items-center justify-between mb-6" data-v-9e32fa80><h4 class="text-lg font-semibold text-gray-900 flex items-center" data-v-9e32fa80><span class="w-6 h-6 bg-blue-500 rounded-lg flex items-center justify-center text-white text-sm mr-3" data-v-9e32fa80>📚</span> 知识库设置 </h4><div class="flex items-center space-x-2" data-v-9e32fa80><button class="p-2 text-gray-500 hover:text-gray-700 transition-colors" data-v-9e32fa80> ⚙️ </button></div></div>',1)),(0,n.Lk)("div",Xt,[(0,n.Lk)("div",null,[t[91]||(t[91]=(0,n.Lk)("h5",{class:"text-md font-semibold text-gray-800 mb-4"},"召回",-1)),(0,n.Lk)("div",Vt,[t[80]||(t[80]=(0,n.Lk)("label",{class:"block text-sm font-medium text-gray-700 mb-2 flex items-center"},[(0,n.eW)(" 调用方式 "),(0,n.Lk)("span",{class:"ml-2 w-4 h-4 text-gray-400 cursor-help"},"ℹ️")],-1)),(0,n.Lk)("div",zt,[(0,n.Lk)("label",Ot,[(0,n.bo)((0,n.Lk)("input",{type:"radio","onUpdate:modelValue":t[11]||(t[11]=e=>l.knowledgeConfig.callMethod=e),value:"auto",class:"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"},null,512),[[o.XL,l.knowledgeConfig.callMethod]]),t[78]||(t[78]=(0,n.Lk)("span",{class:"ml-2 text-sm text-gray-700"},"自动调用",-1))]),(0,n.Lk)("label",Nt,[(0,n.bo)((0,n.Lk)("input",{type:"radio","onUpdate:modelValue":t[12]||(t[12]=e=>l.knowledgeConfig.callMethod=e),value:"manual",class:"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"},null,512),[[o.XL,l.knowledgeConfig.callMethod]]),t[79]||(t[79]=(0,n.Lk)("span",{class:"ml-2 text-sm text-gray-700"},"按需调用",-1))])])]),(0,n.Lk)("div",Ut,[t[84]||(t[84]=(0,n.Lk)("label",{class:"block text-sm font-medium text-gray-700 mb-2 flex items-center"},[(0,n.eW)(" 搜索策略 "),(0,n.Lk)("span",{class:"ml-2 w-4 h-4 text-gray-400 cursor-help"},"ℹ️")],-1)),(0,n.Lk)("div",Wt,[(0,n.Lk)("label",Qt,[(0,n.bo)((0,n.Lk)("input",{type:"radio","onUpdate:modelValue":t[13]||(t[13]=e=>l.knowledgeConfig.searchStrategy=e),value:"mixed",class:"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"},null,512),[[o.XL,l.knowledgeConfig.searchStrategy]]),t[81]||(t[81]=(0,n.Lk)("span",{class:"ml-2 text-sm text-gray-700"},"混合",-1))]),(0,n.Lk)("label",Jt,[(0,n.bo)((0,n.Lk)("input",{type:"radio","onUpdate:modelValue":t[14]||(t[14]=e=>l.knowledgeConfig.searchStrategy=e),value:"semantic",class:"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"},null,512),[[o.XL,l.knowledgeConfig.searchStrategy]]),t[82]||(t[82]=(0,n.Lk)("span",{class:"ml-2 text-sm text-gray-700"},"语义",-1))]),(0,n.Lk)("label",Ht,[(0,n.bo)((0,n.Lk)("input",{type:"radio","onUpdate:modelValue":t[15]||(t[15]=e=>l.knowledgeConfig.searchStrategy=e),value:"full_text",class:"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"},null,512),[[o.XL,l.knowledgeConfig.searchStrategy]]),t[83]||(t[83]=(0,n.Lk)("span",{class:"ml-2 text-sm text-gray-700"},"全文",-1))])])]),(0,n.Lk)("div",Gt,[t[85]||(t[85]=(0,n.Lk)("label",{class:"block text-sm font-medium text-gray-700 mb-2 flex items-center"},[(0,n.eW)(" 最大召回数量 "),(0,n.Lk)("span",{class:"ml-2 w-4 h-4 text-gray-400 cursor-help"},"ℹ️")],-1)),(0,n.Lk)("div",qt,[(0,n.bo)((0,n.Lk)("input",{type:"range","onUpdate:modelValue":t[16]||(t[16]=e=>l.knowledgeConfig.maxRecall=e),min:"1",max:"20",step:"1",class:"flex-1 h-2 bg-blue-200 rounded-lg appearance-none cursor-pointer"},null,512),[[o.Jo,l.knowledgeConfig.maxRecall,void 0,{number:!0}]]),(0,n.Lk)("div",Zt,(0,a.v_)(l.knowledgeConfig.maxRecall),1)])]),(0,n.Lk)("div",Yt,[t[86]||(t[86]=(0,n.Lk)("label",{class:"block text-sm font-medium text-gray-700 mb-2 flex items-center"},[(0,n.eW)(" 最小匹配度 "),(0,n.Lk)("span",{class:"ml-2 w-4 h-4 text-gray-400 cursor-help"},"ℹ️")],-1)),(0,n.Lk)("div",es,[(0,n.bo)((0,n.Lk)("input",{type:"range","onUpdate:modelValue":t[17]||(t[17]=e=>l.knowledgeConfig.minScore=e),min:"0",max:"1",step:"0.01",class:"flex-1 h-2 bg-blue-200 rounded-lg appearance-none cursor-pointer"},null,512),[[o.Jo,l.knowledgeConfig.minScore,void 0,{number:!0}]]),(0,n.Lk)("div",ts,(0,a.v_)(l.knowledgeConfig.minScore.toFixed(2)),1)])]),(0,n.Lk)("div",ss,[(0,n.Lk)("div",os,[t[88]||(t[88]=(0,n.Lk)("label",{class:"text-sm font-medium text-gray-700 flex items-center"},[(0,n.eW)(" 查询改写 "),(0,n.Lk)("span",{class:"ml-2 w-4 h-4 text-gray-400 cursor-help"},"ℹ️")],-1)),(0,n.Lk)("label",ns,[(0,n.bo)((0,n.Lk)("input",{type:"checkbox","onUpdate:modelValue":t[18]||(t[18]=e=>l.knowledgeConfig.queryRewrite=e),class:"sr-only peer"},null,512),[[o.lH,l.knowledgeConfig.queryRewrite]]),t[87]||(t[87]=(0,n.Lk)("div",{class:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-500"},null,-1))])]),(0,n.Lk)("div",as,[t[90]||(t[90]=(0,n.Lk)("label",{class:"text-sm font-medium text-gray-700 flex items-center"},[(0,n.eW)(" 结果重排 "),(0,n.Lk)("span",{class:"ml-2 w-4 h-4 text-gray-400 cursor-help"},"ℹ️")],-1)),(0,n.Lk)("label",rs,[(0,n.bo)((0,n.Lk)("input",{type:"checkbox","onUpdate:modelValue":t[19]||(t[19]=e=>l.knowledgeConfig.resultRerank=e),class:"sr-only peer"},null,512),[[o.lH,l.knowledgeConfig.resultRerank]]),t[89]||(t[89]=(0,n.Lk)("div",{class:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-500"},null,-1))])])])])])]),(0,n.Lk)("div",ls,[l.showKnowledgeBaseSelection?((0,n.uX)(),(0,n.CE)("div",is,[(0,n.Lk)("div",cs,[(0,n.Lk)("div",ds,[(0,n.Lk)("button",{onClick:t[20]||(t[20]=(...e)=>i.closeKnowledgeBaseSelection&&i.closeKnowledgeBaseSelection(...e)),class:"mr-3 w-8 h-8 bg-white rounded-lg hover:bg-gray-100 flex items-center justify-center text-gray-600 transition-colors"}," ← "),(0,n.Lk)("h4",gs,(0,a.v_)("text"===l.currentKnowledgeBaseType?"选择文本知识库":"table"===l.currentKnowledgeBaseType?"选择表格知识库":"选择照片知识库"),1)]),(0,n.Lk)("button",{onClick:t[21]||(t[21]=(...e)=>i.openCreateKnowledgeBase&&i.openCreateKnowledgeBase(...e)),class:"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm"}," 创建知识库 ")]),(0,n.Lk)("div",us,[(0,n.Lk)("div",hs,[(0,n.bo)((0,n.Lk)("input",{"onUpdate:modelValue":t[22]||(t[22]=e=>l.searchQuery=e),placeholder:"搜索知识库...",class:"w-full pl-8 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"},null,512),[[o.Jo,l.searchQuery]]),t[93]||(t[93]=(0,n.Lk)("span",{class:"absolute left-2 top-2.5 text-gray-400"},"🔍",-1))])]),i.filteredKnowledgeBases.length>0?((0,n.uX)(),(0,n.CE)("div",ms,[((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(i.filteredKnowledgeBases,e=>((0,n.uX)(),(0,n.CE)("div",{key:e.id,class:"flex items-center justify-between p-4 bg-white border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50/30 transition-all duration-200"},[(0,n.Lk)("div",ps,[(0,n.Lk)("div",{class:(0,a.C4)(["w-8 h-8 rounded-lg flex items-center justify-center text-white text-sm","text"===e.type?"bg-blue-500":"table"===e.type?"bg-green-500":"bg-orange-500"])},(0,a.v_)("text"===e.type?"📄":"table"===e.type?"📊":"🖼️"),3),(0,n.Lk)("div",null,[(0,n.Lk)("h5",bs,(0,a.v_)(e.name),1),(0,n.Lk)("p",fs,(0,a.v_)(e.description),1),(0,n.Lk)("div",xs,[(0,n.Lk)("span",ys,(0,a.v_)(e.segmentCount||0)+" 个分段",1),(0,n.Lk)("span",ks,(0,a.v_)(i.formatDate(e.updateTime)),1)])])]),(0,n.Lk)("div",vs,[(0,n.Lk)("button",{onClick:t=>i.selectKnowledgeBase(e),class:"px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 transition-colors"}," 选择 ",8,ws),(0,n.Lk)("button",{onClick:t=>i.editKnowledgeBase(e),class:"px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600 transition-colors"}," 编辑 ",8,Ls),(0,n.Lk)("div",Cs,[(0,n.Lk)("button",{onClick:t=>i.toggleKnowledgeBaseMenu(e.id),class:"px-3 py-1 bg-gray-300 text-gray-700 rounded text-sm hover:bg-gray-400 transition-colors"}," 更多 ",8,Ss),l.activeKnowledgeBaseMenu===e.id?((0,n.uX)(),(0,n.CE)("div",Ts,[(0,n.Lk)("button",{onClick:t=>i.viewKnowledgeBaseDetails(e),class:"w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 rounded-t-lg"}," 查看详情 ",8,Es),(0,n.Lk)("button",{onClick:t[23]||(t[23]=(...e)=>i.openKnowledgeBaseMarket&&i.openKnowledgeBaseMarket(...e)),class:"w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50"}," 资源广场 "),(0,n.Lk)("button",{onClick:t=>i.confirmDeleteKnowledgeBase(e),class:"w-full px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50 rounded-b-lg"}," 删除知识库 ",8,Ms)])):(0,n.Q3)("",!0)])])]))),128))])):((0,n.uX)(),(0,n.CE)("div",As,[t[94]||(t[94]=(0,n.Lk)("div",{class:"w-16 h-16 mx-auto mb-3 bg-gray-100 rounded-full flex items-center justify-center"},[(0,n.Lk)("span",{class:"text-2xl text-gray-400"},"📚")],-1)),t[95]||(t[95]=(0,n.Lk)("h5",{class:"text-md font-semibold text-gray-900 mb-2"},"暂无知识库",-1)),t[96]||(t[96]=(0,n.Lk)("p",{class:"text-sm text-gray-600 mb-4"},"请先创建后添加",-1)),(0,n.Lk)("button",{onClick:t[24]||(t[24]=(...e)=>i.openCreateKnowledgeBase&&i.openCreateKnowledgeBase(...e)),class:"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm"}," 创建知识库 ")]))])):l.showCreateKnowledgeBase?((0,n.uX)(),(0,n.CE)("div",Bs,[(0,n.Lk)("div",Ks,[(0,n.Lk)("button",{onClick:t[25]||(t[25]=(...e)=>i.closeCreateKnowledgeBase&&i.closeCreateKnowledgeBase(...e)),class:"mr-3 w-8 h-8 bg-white rounded-lg hover:bg-gray-100 flex items-center justify-center text-gray-600 transition-colors"}," ← "),t[97]||(t[97]=(0,n.Lk)("h4",{class:"text-lg font-semibold text-gray-900"},"创建知识库",-1))]),(0,n.Lk)("div",Is,[t[100]||(t[100]=(0,n.Lk)("h5",{class:"text-sm font-semibold text-gray-700 mb-3"},"知识库创建方式",-1)),(0,n.Lk)("div",_s,[(0,n.Lk)("div",{onClick:t[26]||(t[26]=e=>l.createMethod="volcano"),class:(0,a.C4)(["p-3 border-2 rounded-lg cursor-pointer transition-all duration-200","volcano"===l.createMethod?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"])},t[98]||(t[98]=[(0,n.Lk)("div",{class:"flex items-center space-x-2"},[(0,n.Lk)("div",{class:"w-6 h-6 bg-blue-500 rounded-lg flex items-center justify-center text-white text-xs"}," 🏔️ "),(0,n.Lk)("div",null,[(0,n.Lk)("h6",{class:"font-semibold text-gray-900 text-sm"},"关联火山知识库"),(0,n.Lk)("p",{class:"text-xs text-gray-600"},"支持文本、表格知识库，精细化切片管理")])],-1)]),2),(0,n.Lk)("div",{onClick:t[27]||(t[27]=e=>l.createMethod="local"),class:(0,a.C4)(["p-3 border-2 rounded-lg cursor-pointer transition-all duration-200","local"===l.createMethod?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"])},t[99]||(t[99]=[(0,n.Lk)("div",{class:"flex items-center space-x-2"},[(0,n.Lk)("div",{class:"w-6 h-6 bg-purple-500 rounded-lg flex items-center justify-center text-white text-xs"}," 👻 "),(0,n.Lk)("div",null,[(0,n.Lk)("h6",{class:"font-semibold text-gray-900 text-sm"},"创建扣子知识库"),(0,n.Lk)("p",{class:"text-xs text-gray-600"},"支持图片、文本、表格知识库，智能切片管理")])],-1)]),2)])]),(0,n.Lk)("div",js,[t[104]||(t[104]=(0,n.Lk)("h5",{class:"text-sm font-semibold text-gray-700 mb-3"},"知识库类型",-1)),(0,n.Lk)("div",Ds,[(0,n.Lk)("div",{onClick:t[28]||(t[28]=e=>l.newKbType="text"),class:(0,a.C4)(["p-3 border-2 rounded-lg cursor-pointer transition-all duration-200 text-center","text"===l.newKbType?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"])},t[101]||(t[101]=[(0,n.Lk)("div",{class:"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center text-white text-sm mx-auto mb-1"}," 📄 ",-1),(0,n.Lk)("h6",{class:"font-semibold text-gray-900 text-xs"},"文本格式",-1)]),2),(0,n.Lk)("div",{onClick:t[29]||(t[29]=e=>l.newKbType="table"),class:(0,a.C4)(["p-3 border-2 rounded-lg cursor-pointer transition-all duration-200 text-center","table"===l.newKbType?"border-green-500 bg-green-50":"border-gray-200 hover:border-gray-300"])},t[102]||(t[102]=[(0,n.Lk)("div",{class:"w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center text-white text-sm mx-auto mb-1"}," 📊 ",-1),(0,n.Lk)("h6",{class:"font-semibold text-gray-900 text-xs"},"表格格式",-1)]),2),(0,n.Lk)("div",{onClick:t[30]||(t[30]=e=>l.newKbType="image"),class:(0,a.C4)(["p-3 border-2 rounded-lg cursor-pointer transition-all duration-200 text-center","image"===l.newKbType?"border-orange-500 bg-orange-50":"border-gray-200 hover:border-gray-300"])},t[103]||(t[103]=[(0,n.Lk)("div",{class:"w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center text-white text-sm mx-auto mb-1"}," 🖼️ ",-1),(0,n.Lk)("h6",{class:"font-semibold text-gray-900 text-xs"},"照片类型",-1)]),2)])]),(0,n.Lk)("div",Fs,[(0,n.Lk)("div",null,[t[105]||(t[105]=(0,n.Lk)("label",{class:"block text-xs font-semibold text-gray-700 mb-1"},[(0,n.eW)(" 名称 "),(0,n.Lk)("span",{class:"text-red-500"},"*")],-1)),(0,n.bo)((0,n.Lk)("input",{"onUpdate:modelValue":t[31]||(t[31]=e=>l.newKbName=e),placeholder:"输入数据集名称",class:"w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"},null,512),[[o.Jo,l.newKbName]]),(0,n.Lk)("div",$s,(0,a.v_)(l.newKbName.length)+"/100",1)]),(0,n.Lk)("div",null,[t[106]||(t[106]=(0,n.Lk)("label",{class:"block text-xs font-semibold text-gray-700 mb-1"},"描述",-1)),(0,n.bo)((0,n.Lk)("textarea",{"onUpdate:modelValue":t[32]||(t[32]=e=>l.newKbDescription=e),placeholder:"输入数据集内容的描述",rows:"2",class:"w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none text-sm"},null,512),[[o.Jo,l.newKbDescription]]),(0,n.Lk)("div",Ps,(0,a.v_)(l.newKbDescription.length)+"/2000",1)]),(0,n.Lk)("div",Rs,[(0,n.Lk)("button",{onClick:t[33]||(t[33]=(...e)=>i.closeCreateKnowledgeBase&&i.closeCreateKnowledgeBase(...e)),class:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors text-sm"}," 取消 "),(0,n.Lk)("button",{onClick:t[34]||(t[34]=(...e)=>i.createKnowledgeBase&&i.createKnowledgeBase(...e)),disabled:!l.newKbName.trim(),class:"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors text-sm"}," 完成创建 ",8,Xs)])])])):((0,n.uX)(),(0,n.CE)("div",Vs,[(0,n.Lk)("div",zs,[(0,n.Lk)("div",Os,[t[107]||(t[107]=(0,n.Lk)("h5",{class:"text-md font-semibold text-gray-800 flex items-center"},[(0,n.Lk)("span",{class:"mr-2"},"📄"),(0,n.eW)(" 文本 ")],-1)),(0,n.Lk)("button",{onClick:t[35]||(t[35]=e=>i.openKnowledgeBaseSelection("text")),class:"w-6 h-6 bg-gray-100 rounded text-gray-600 hover:bg-gray-200 transition-colors"}," + ")]),t[108]||(t[108]=(0,n.Lk)("p",{class:"text-sm text-gray-600"}," 将文档、URL、三方数据源上传为文本知识库后，用户发送消息时，智能体能够引用文本知识中的内容回答用户问题。 ",-1))]),(0,n.Lk)("div",Ns,[(0,n.Lk)("div",Us,[t[109]||(t[109]=(0,n.Lk)("h5",{class:"text-md font-semibold text-gray-800 flex items-center"},[(0,n.Lk)("span",{class:"mr-2"},"📊"),(0,n.eW)(" 表格 ")],-1)),(0,n.Lk)("button",{onClick:t[36]||(t[36]=e=>i.openKnowledgeBaseSelection("table")),class:"w-6 h-6 bg-gray-100 rounded text-gray-600 hover:bg-gray-200 transition-colors"}," + ")]),t[110]||(t[110]=(0,n.Lk)("p",{class:"text-sm text-gray-600"}," 用户上传表格后，支持按照表格的某列来匹配适合的行给智能体引用，同时也支持基于自然语言对数据库进行查询和计算。 ",-1))]),(0,n.Lk)("div",Ws,[(0,n.Lk)("div",Qs,[t[111]||(t[111]=(0,n.Lk)("h5",{class:"text-md font-semibold text-gray-800 flex items-center"},[(0,n.Lk)("span",{class:"mr-2"},"🖼️"),(0,n.eW)(" 照片 ")],-1)),(0,n.Lk)("button",{onClick:t[37]||(t[37]=e=>i.openKnowledgeBaseSelection("image")),class:"w-6 h-6 bg-gray-100 rounded text-gray-600 hover:bg-gray-200 transition-colors"}," + ")]),t[112]||(t[112]=(0,n.Lk)("p",{class:"text-sm text-gray-600"}," 照片上传到知识库后自动/手动添加适文描述，智能体可以基于照片的描述匹配到最合适的照片。 ",-1))])]))])])])):(0,n.Q3)("",!0),"preview"===l.activeTab?((0,n.uX)(),(0,n.CE)("div",Js,[(0,n.Lk)("div",Hs,[(0,n.Lk)("div",Gs,[t[116]||(t[116]=(0,n.Lk)("h4",{class:"text-lg font-semibold text-gray-900 mb-6 flex items-center"},[(0,n.Lk)("span",{class:"w-6 h-6 bg-orange-500 rounded-lg flex items-center justify-center text-white text-sm mr-3"},"🔍"),(0,n.eW)(" 预览与调试 ")],-1)),(0,n.Lk)("div",qs,[(0,n.Lk)("div",Zs,[t[113]||(t[113]=(0,n.Lk)("h5",{class:"text-md font-semibold text-gray-800 mb-4"},"人设与回复逻辑测试",-1)),(0,n.Lk)("div",Ys,[((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(l.testMessages,(e,t)=>((0,n.uX)(),(0,n.CE)("div",{key:t,class:"mb-3"},[(0,n.Lk)("div",{class:(0,a.C4)(["max-w-xs p-3 rounded-lg text-sm",e.isUser?"bg-blue-500 text-white ml-auto":"bg-white border border-gray-200"])},(0,a.v_)(e.content),3)]))),128))]),(0,n.Lk)("div",eo,[(0,n.bo)((0,n.Lk)("input",{"onUpdate:modelValue":t[38]||(t[38]=e=>l.testInput=e),onKeyup:t[39]||(t[39]=(0,o.jR)((...e)=>i.sendTestMessage&&i.sendTestMessage(...e),["enter"])),placeholder:"输入测试消息...",class:"flex-1 px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"},null,544),[[o.Jo,l.testInput]]),(0,n.Lk)("button",{onClick:t[40]||(t[40]=(...e)=>i.sendTestMessage&&i.sendTestMessage(...e)),class:"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"}," 发送 ")])]),(0,n.Lk)("div",to,[t[115]||(t[115]=(0,n.Lk)("h5",{class:"text-md font-semibold text-gray-800 mb-4"},"预览与调试",-1)),(0,n.Lk)("div",so,[(0,n.Lk)("div",oo,[t[114]||(t[114]=(0,n.Lk)("div",null,[(0,n.Lk)("strong",null,"当前配置:")],-1)),(0,n.Lk)("div",null,"• 人设: "+(0,a.v_)(l.localPersonality?"已配置":"未配置"),1),(0,n.Lk)("div",null,"• 回复模式: "+(0,a.v_)(l.replyLogic.responseMode),1),(0,n.Lk)("div",null,"• 温度: "+(0,a.v_)(l.replyLogic.temperature),1),(0,n.Lk)("div",null,"• 最大Token: "+(0,a.v_)(l.replyLogic.maxTokens),1),(0,n.Lk)("div",null,"• 知识库调用: "+(0,a.v_)(l.knowledgeConfig.callMethod),1),(0,n.Lk)("div",null,"• 搜索策略: "+(0,a.v_)(l.knowledgeConfig.searchStrategy),1)])]),(0,n.Lk)("div",no,[(0,n.Lk)("button",{onClick:t[41]||(t[41]=(...e)=>i.exportConfiguration&&i.exportConfiguration(...e)),class:"w-full px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"}," 导出配置 ")])])])])])])):(0,n.Q3)("",!0)])]),(0,n.Lk)("div",ao,[(0,n.Lk)("button",{onClick:t[42]||(t[42]=t=>e.$emit("close")),class:"px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors"}," 取消 "),(0,n.Lk)("button",{onClick:t[43]||(t[43]=(...e)=>i.saveSettings&&i.saveSettings(...e)),class:"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors relative"},[t[117]||(t[117]=(0,n.eW)(" 保存设置 ",-1)),l.saveSuccessMessage?((0,n.uX)(),(0,n.CE)("div",ro," ✅ 保存成功！ ")):(0,n.Q3)("",!0)])])]),l.showOptimizeModal?((0,n.uX)(),(0,n.CE)("div",{key:0,class:"fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-60",onClick:t[51]||(t[51]=(0,o.D$)((...e)=>i.closeOptimizeModal&&i.closeOptimizeModal(...e),["self"]))},[(0,n.Lk)("div",lo,[(0,n.Lk)("div",io,[t[118]||(t[118]=(0,n.Lk)("div",{class:"flex items-center"},[(0,n.Lk)("div",{class:"w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center text-white text-sm mr-3"}," ✨ "),(0,n.Lk)("h3",{class:"text-xl font-bold text-gray-900"},"自动优化提示词")],-1)),(0,n.Lk)("button",{onClick:t[44]||(t[44]=(...e)=>i.closeOptimizeModal&&i.closeOptimizeModal(...e)),class:"w-8 h-8 rounded-lg hover:bg-gray-100 flex items-center justify-center text-gray-500 hover:text-gray-700 transition-colors"}," ✕ ")]),(0,n.Lk)("div",co,[(0,n.Lk)("div",go,[(0,n.Lk)("div",uo,[(0,n.Lk)("button",{onClick:t[45]||(t[45]=e=>l.optimizeMode="auto"),class:(0,a.C4)(["px-4 py-2 rounded-lg font-medium transition-all duration-200","auto"===l.optimizeMode?"bg-blue-500 text-white shadow-md":"bg-gray-100 text-gray-600 hover:bg-gray-200"])}," 自动优化 ",2),(0,n.Lk)("button",{onClick:t[46]||(t[46]=e=>l.optimizeMode="custom"),class:(0,a.C4)(["px-4 py-2 rounded-lg font-medium transition-all duration-200","custom"===l.optimizeMode?"bg-blue-500 text-white shadow-md":"bg-gray-100 text-gray-600 hover:bg-gray-200"])}," 根据调试结果优化 ",2)])]),(0,n.Lk)("div",ho,[t[119]||(t[119]=(0,n.Lk)("div",{class:"flex items-center mb-3"},[(0,n.Lk)("span",{class:"text-lg mr-2"},"✨"),(0,n.Lk)("label",{class:"text-sm font-medium text-gray-700"},"你希望如何编写或优化提示词？")],-1)),(0,n.Lk)("div",mo,[(0,n.bo)((0,n.Lk)("textarea",{"onUpdate:modelValue":t[47]||(t[47]=e=>l.optimizeInput=e),placeholder:"请描述您希望如何优化提示词，例如：让助手更专业、更友善、更具创意...",rows:"4",class:"w-full px-4 py-3 pr-12 border-2 border-blue-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none transition-all duration-200 bg-blue-50/30 focus:bg-white"},null,512),[[o.Jo,l.optimizeInput]]),(0,n.Lk)("button",{onClick:t[48]||(t[48]=(...e)=>i.optimizePrompt&&i.optimizePrompt(...e)),disabled:!l.optimizeInput.trim()||l.isOptimizing,class:"absolute right-3 bottom-3 w-8 h-8 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center justify-center"},[l.isOptimizing?((0,n.uX)(),(0,n.CE)("div",fo)):((0,n.uX)(),(0,n.CE)("span",bo,"→"))],8,po)])]),l.optimizedResult?((0,n.uX)(),(0,n.CE)("div",xo,[t[120]||(t[120]=(0,n.Lk)("h5",{class:"text-md font-semibold text-gray-800 mb-3 flex items-center"},[(0,n.Lk)("span",{class:"mr-2"},"🎯"),(0,n.eW)(" 优化结果 ")],-1)),(0,n.Lk)("div",yo,[(0,n.Lk)("div",ko,(0,a.v_)(l.optimizedResult),1)])])):(0,n.Q3)("",!0),(0,n.Lk)("div",vo,[t[121]||(t[121]=(0,n.Lk)("h5",{class:"text-md font-semibold text-gray-800 mb-3 flex items-center"},[(0,n.Lk)("span",{class:"mr-2"},"📝"),(0,n.eW)(" 当前人设 ")],-1)),(0,n.Lk)("div",wo,[(0,n.Lk)("div",Lo,(0,a.v_)(l.localPersonality||"暂无人设配置"),1)])])]),(0,n.Lk)("div",Co,[(0,n.Lk)("button",{onClick:t[49]||(t[49]=(...e)=>i.closeOptimizeModal&&i.closeOptimizeModal(...e)),class:"px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors"}," 取消 "),(0,n.Lk)("button",{onClick:t[50]||(t[50]=(...e)=>i.applyOptimizedResult&&i.applyOptimizedResult(...e)),disabled:!l.optimizedResult,class:"px-6 py-2 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-lg hover:from-purple-600 hover:to-blue-600 disabled:from-gray-300 disabled:to-gray-400 disabled:cursor-not-allowed transition-all duration-200"}," 应用优化结果 ",8,So)])])])):(0,n.Q3)("",!0),(0,n.bF)(c,{show:l.showKnowledgeBaseManager,knowledgeBase:l.selectedKnowledgeBaseForEdit,onClose:t[52]||(t[52]=e=>l.showKnowledgeBaseManager=!1),onComplete:i.onKnowledgeBaseManagerComplete},null,8,["show","knowledgeBase","onComplete"]),(0,n.bF)(d,{show:l.showKnowledgeBaseEditor,knowledgeBase:l.selectedKnowledgeBaseForEdit,onClose:t[53]||(t[53]=e=>l.showKnowledgeBaseEditor=!1),onSave:i.onKnowledgeBaseEditorSave},null,8,["show","knowledgeBase","onSave"]),l.showKnowledgeBaseMarket?((0,n.uX)(),(0,n.CE)("div",{key:1,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",onClick:t[60]||(t[60]=(0,o.D$)((...e)=>i.closeKnowledgeBaseMarket&&i.closeKnowledgeBaseMarket(...e),["self"]))},[(0,n.Lk)("div",To,[(0,n.Lk)("div",Eo,[t[123]||(t[123]=(0,n.Lk)("h3",{class:"text-xl font-bold text-gray-900"},"知识库资源广场",-1)),(0,n.Lk)("button",{onClick:t[54]||(t[54]=(...e)=>i.closeKnowledgeBaseMarket&&i.closeKnowledgeBaseMarket(...e)),class:"text-gray-400 hover:text-gray-600 transition-colors"},t[122]||(t[122]=[(0,n.Lk)("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[(0,n.Lk)("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),(0,n.Lk)("div",Mo,[(0,n.Lk)("div",Ao,[(0,n.Lk)("div",Bo,[(0,n.bo)((0,n.Lk)("input",{"onUpdate:modelValue":t[55]||(t[55]=e=>l.marketSearchQuery=e),type:"text",placeholder:"搜索知识库资源...",class:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[o.Jo,l.marketSearchQuery]]),t[124]||(t[124]=(0,n.Lk)("span",{class:"absolute left-3 top-3.5 text-gray-400"},"🔍",-1))])]),(0,n.Lk)("div",Ko,[(0,n.Lk)("button",{onClick:t[56]||(t[56]=e=>l.marketSelectedType=""),class:(0,a.C4)(["px-4 py-2 rounded-full text-sm transition-colors",""===l.marketSelectedType?"bg-blue-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"])}," 全部 ",2),(0,n.Lk)("button",{onClick:t[57]||(t[57]=e=>l.marketSelectedType="text"),class:(0,a.C4)(["px-4 py-2 rounded-full text-sm transition-colors","text"===l.marketSelectedType?"bg-blue-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"])}," 文本文档 ",2),(0,n.Lk)("button",{onClick:t[58]||(t[58]=e=>l.marketSelectedType="table"),class:(0,a.C4)(["px-4 py-2 rounded-full text-sm transition-colors","table"===l.marketSelectedType?"bg-blue-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"])}," 表格数据 ",2),(0,n.Lk)("button",{onClick:t[59]||(t[59]=e=>l.marketSelectedType="image"),class:(0,a.C4)(["px-4 py-2 rounded-full text-sm transition-colors","image"===l.marketSelectedType?"bg-blue-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"])}," 图片资源 ",2)]),i.filteredMarketKnowledgeBases.length>0?((0,n.uX)(),(0,n.CE)("div",Io,[((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(i.filteredMarketKnowledgeBases,e=>((0,n.uX)(),(0,n.CE)("div",{key:e.id,class:"bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"},[(0,n.Lk)("div",_o,[(0,n.Lk)("div",{class:(0,a.C4)(["w-10 h-10 rounded-lg flex items-center justify-center text-white text-lg","text"===e.type?"bg-blue-500":"table"===e.type?"bg-green-500":"bg-orange-500"])},(0,a.v_)(e.icon),3),(0,n.Lk)("div",jo,[(0,n.Lk)("h4",Do,(0,a.v_)(e.name),1),(0,n.Lk)("p",Fo,(0,a.v_)(i.getKnowledgeBaseTypeLabel(e.type)),1)])]),(0,n.Lk)("p",$o,(0,a.v_)(e.description),1),(0,n.Lk)("div",Po,[(0,n.Lk)("span",Ro,(0,a.v_)(e.segmentCount||0)+" 个分段",1),(0,n.Lk)("div",Xo,[(0,n.Lk)("button",{onClick:t=>i.selectKnowledgeBaseFromMarket(e),class:"px-3 py-1 bg-green-500 text-white rounded text-xs hover:bg-green-600 transition-colors"}," 选择 ",8,Vo),(0,n.Lk)("button",{onClick:t=>i.importKnowledgeBaseFromMarket(e),class:"px-3 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600 transition-colors"}," 导入 ",8,zo)])])]))),128))])):((0,n.uX)(),(0,n.CE)("div",Oo,t[125]||(t[125]=[(0,n.Lk)("div",{class:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4"},[(0,n.Lk)("span",{class:"text-2xl text-gray-400"},"📚")],-1),(0,n.Lk)("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"暂无知识库资源",-1),(0,n.Lk)("p",{class:"text-gray-500"},"您还没有创建任何知识库，请先创建知识库",-1)])))])])])):(0,n.Q3)("",!0),l.showDeleteConfirm?((0,n.uX)(),(0,n.CE)("div",{key:2,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",onClick:t[63]||(t[63]=(0,o.D$)((...e)=>i.cancelDeleteKnowledgeBase&&i.cancelDeleteKnowledgeBase(...e),["self"]))},[(0,n.Lk)("div",No,[t[130]||(t[130]=(0,n.Fv)('<div class="flex items-center mb-4" data-v-9e32fa80><div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4" data-v-9e32fa80><svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-9e32fa80><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" data-v-9e32fa80></path></svg></div><div data-v-9e32fa80><h3 class="text-lg font-semibold text-gray-900" data-v-9e32fa80>确认删除</h3><p class="text-sm text-gray-600" data-v-9e32fa80>此操作不可撤销</p></div></div>',1)),(0,n.Lk)("p",Uo,[t[126]||(t[126]=(0,n.eW)(" 您确定要删除知识库 ",-1)),(0,n.Lk)("strong",null,'"'+(0,a.v_)(l.knowledgeBaseToDelete?.name)+'"',1),t[127]||(t[127]=(0,n.eW)(" 吗？ ",-1)),t[128]||(t[128]=(0,n.Lk)("br",null,null,-1)),t[129]||(t[129]=(0,n.Lk)("span",{class:"text-sm text-gray-500"},"删除后将无法恢复，包含的所有内容都将丢失。",-1))]),(0,n.Lk)("div",Wo,[(0,n.Lk)("button",{onClick:t[61]||(t[61]=(...e)=>i.cancelDeleteKnowledgeBase&&i.cancelDeleteKnowledgeBase(...e)),class:"flex-1 px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"}," 取消 "),(0,n.Lk)("button",{onClick:t[62]||(t[62]=(...e)=>i.deleteKnowledgeBase&&i.deleteKnowledgeBase(...e)),class:"flex-1 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"}," 确认删除 ")])])])):(0,n.Q3)("",!0)])):(0,n.Q3)("",!0)}s(4603),s(7566),s(8721);const Jo={key:0,class:"fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-70"},Ho={class:"bg-white rounded-2xl shadow-2xl w-[95vw] h-[90vh] overflow-hidden transform transition-all duration-300 scale-100 flex flex-col"},Go={class:"flex items-center justify-between p-6 border-b border-gray-200"},qo={class:"flex items-center"},Zo={class:"text-xl font-bold text-gray-900"},Yo={class:"px-6 py-4 border-b border-gray-200 bg-gray-50"},en={class:"flex items-center justify-center space-x-8"},tn={class:"flex-1 overflow-y-auto"},sn={key:0,class:"p-6"},on={class:"text-center py-16"},nn={key:0,class:"mt-8"},an={class:"space-y-3"},rn={class:"flex items-center space-x-3"},ln={key:0},cn={key:1},dn={key:2},gn={key:3},un={class:"font-semibold text-gray-900"},hn={class:"text-sm text-gray-600"},mn={class:"flex items-center space-x-2 text-xs"},pn={key:0,class:"text-blue-600"},bn={key:1,class:"px-1 py-0.5 bg-green-100 text-green-700 rounded"},fn={key:2,class:"px-1 py-0.5 bg-purple-100 text-purple-700 rounded"},xn={class:"flex items-center space-x-2"},yn={class:"text-right"},kn={key:0,class:"text-sm text-red-600"},vn={key:1,class:"text-sm text-blue-600"},wn={key:2,class:"text-sm text-green-600"},Ln={key:3,class:"text-sm text-gray-600"},Cn={key:4,class:"w-20 h-1 bg-gray-200 rounded-full mt-1"},Sn=["onClick"],Tn={key:1,class:"p-6"},En={class:"max-w-4xl mx-auto space-y-6"},Mn={class:"bg-gray-50 border border-gray-200 rounded-xl p-6"},An={class:"space-y-4"},Bn={class:"space-y-2"},Kn={class:"flex items-center"},In={class:"flex items-center"},_n={key:0,class:"mb-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200"},jn={class:"grid grid-cols-2 gap-4 mb-3"},Dn={class:"flex items-center"},Fn={class:"flex items-center"},$n={class:"bg-gray-50 border border-gray-200 rounded-xl p-6"},Pn={class:"grid grid-cols-2 gap-6"},Rn={class:"bg-gray-50 border border-gray-200 rounded-xl p-6"},Xn={class:"space-y-4"},Vn={class:"flex items-center"},zn={class:"flex items-center"},On={key:2,class:"p-6"},Nn={class:"flex h-full"},Un={class:"w-1/4 border-r border-gray-200 pr-4"},Wn={class:"space-y-2"},Qn=["onClick"],Jn={class:"flex items-center justify-between"},Hn={class:"flex items-center space-x-2"},Gn={class:"font-medium text-gray-900 text-sm"},qn={key:0,class:"text-xs text-blue-600"},Zn={key:1,class:"text-xs text-red-600"},Yn={class:"flex items-center justify-between mt-1"},ea={class:"text-xs text-gray-600"},ta={class:"text-xs text-gray-500"},sa={key:0,class:"text-xs text-red-600 mt-1"},oa={class:"flex-1 pl-6"},na={class:"flex items-center justify-between mb-4"},aa={class:"text-sm text-gray-600"},ra={class:"space-y-4 max-h-96 overflow-y-auto"},la={class:"flex items-center justify-between mb-2"},ia={class:"flex items-center space-x-2"},ca={class:"text-sm font-medium text-gray-700"},da={key:0,class:"px-1 py-0.5 bg-green-100 text-green-700 text-xs rounded"},ga={key:1,class:"px-1 py-0.5 bg-gray-100 text-gray-600 text-xs rounded"},ua={class:"flex items-center space-x-2"},ha={class:"text-xs text-gray-500"},ma={class:"text-xs text-blue-500"},pa={class:"text-sm text-gray-900 bg-gray-50 p-3 rounded mb-3"},ba={key:0,class:"mb-2"},fa={key:1,class:"mb-3"},xa={class:"text-xs text-gray-700 mt-1"},ya={class:"flex items-center justify-between"},ka={class:"text-xs text-gray-500"},va={class:"flex space-x-2"},wa=["onClick"],La=["onClick"],Ca={key:0,class:"text-center py-8"},Sa={key:3,class:"p-6"},Ta={class:"text-center py-16"},Ea={key:0,class:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6 max-w-2xl mx-auto"},Ma={class:"bg-blue-50 p-3 rounded-lg text-center"},Aa={class:"text-xl font-bold text-blue-600"},Ba={class:"bg-green-50 p-3 rounded-lg text-center"},Ka={class:"text-xl font-bold text-green-600"},Ia={class:"bg-purple-50 p-3 rounded-lg text-center"},_a={class:"text-xl font-bold text-purple-600"},ja={class:"bg-orange-50 p-3 rounded-lg text-center"},Da={class:"text-xl font-bold text-orange-600"},Fa={class:"max-w-md mx-auto"},$a={class:"bg-gray-200 rounded-full h-2 mb-4"},Pa={class:"text-sm text-gray-600"},Ra={class:"mt-8 text-left max-w-md mx-auto"},Xa={class:"space-y-2 text-sm"},Va={class:"flex items-center"},za={class:"flex items-center"},Oa={class:"flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50"},Na={key:1},Ua={class:"flex space-x-4"},Wa=["disabled"];function Qa(e,t,s,r,l,i){return s.show?((0,n.uX)(),(0,n.CE)("div",Jo,[(0,n.Lk)("div",Ho,[(0,n.Lk)("div",Go,[(0,n.Lk)("div",qo,[(0,n.Lk)("button",{onClick:t[0]||(t[0]=(...e)=>i.handleBack&&i.handleBack(...e)),class:"mr-3 w-8 h-8 bg-gray-100 rounded-lg hover:bg-gray-200 flex items-center justify-center text-gray-600 transition-colors"}," ← "),t[21]||(t[21]=(0,n.Lk)("div",{class:"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center text-white text-sm mr-3"}," 📚 ",-1)),(0,n.Lk)("h3",Zo,(0,a.v_)(i.getStepTitle()),1)]),(0,n.Lk)("button",{onClick:t[1]||(t[1]=t=>e.$emit("close")),class:"w-8 h-8 rounded-lg hover:bg-gray-100 flex items-center justify-center text-gray-500 hover:text-gray-700 transition-colors"}," ✕ ")]),(0,n.Lk)("div",Yo,[(0,n.Lk)("div",en,[((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(l.steps,(e,t)=>((0,n.uX)(),(0,n.CE)("div",{key:t,class:"flex items-center"},[(0,n.Lk)("div",{class:(0,a.C4)(["w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold",l.currentStep>=t+1?"bg-blue-500 text-white":"bg-gray-200 text-gray-500"])},(0,a.v_)(t+1),3),(0,n.Lk)("span",{class:(0,a.C4)(["ml-2 text-sm font-medium",l.currentStep>=t+1?"text-blue-600":"text-gray-500"])},(0,a.v_)(e),3),t<l.steps.length-1?((0,n.uX)(),(0,n.CE)("div",{key:0,class:(0,a.C4)(["w-16 h-0.5 ml-4",l.currentStep>t+1?"bg-blue-500":"bg-gray-200"])},null,2)):(0,n.Q3)("",!0)]))),128))])]),(0,n.Lk)("div",tn,[1===l.currentStep?((0,n.uX)(),(0,n.CE)("div",sn,[(0,n.Lk)("div",on,[(0,n.Lk)("div",{class:"w-24 h-24 mx-auto mb-6 border-2 border-dashed border-gray-300 rounded-xl flex items-center justify-center hover:border-blue-400 transition-colors cursor-pointer",onClick:t[2]||(t[2]=(...e)=>i.triggerFileUpload&&i.triggerFileUpload(...e)),onDragover:t[3]||(t[3]=(0,o.D$)(()=>{},["prevent"])),onDrop:t[4]||(t[4]=(0,o.D$)((...e)=>i.handleFileDrop&&i.handleFileDrop(...e),["prevent"]))},t[22]||(t[22]=[(0,n.Lk)("div",{class:"text-center"},[(0,n.Lk)("span",{class:"text-4xl text-gray-400 block mb-2"},"📁"),(0,n.Lk)("p",{class:"text-sm text-gray-600"},"点击上传或拖拽文件到此处")],-1)]),32),(0,n.Lk)("input",{ref:"fileInput",type:"file",multiple:"",accept:".docx,.txt,.md",class:"hidden",onChange:t[5]||(t[5]=(...e)=>i.handleFileSelect&&i.handleFileSelect(...e))},null,544),t[24]||(t[24]=(0,n.Lk)("p",{class:"text-gray-600 mb-4"},"支持 Word文档(.docx)、Markdown(.md)、文本文件(.txt)，单个文件不超过 50MB",-1)),l.uploadedFiles.length>0?((0,n.uX)(),(0,n.CE)("div",nn,[t[23]||(t[23]=(0,n.Lk)("h4",{class:"text-lg font-semibold text-gray-900 mb-4"},"已上传文件",-1)),(0,n.Lk)("div",an,[((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(l.uploadedFiles,e=>((0,n.uX)(),(0,n.CE)("div",{key:e.id,class:"flex items-center justify-between p-4 border border-gray-200 rounded-lg"},[(0,n.Lk)("div",rn,[(0,n.Lk)("div",{class:(0,a.C4)(["w-10 h-10 rounded-lg flex items-center justify-center text-white","error"===e.status?"bg-red-500":"completed"===e.status?"bg-green-500":"bg-blue-500"])},["error"===e.status?((0,n.uX)(),(0,n.CE)("span",ln,"❌")):"completed"===e.status?((0,n.uX)(),(0,n.CE)("span",cn,"✅")):"parsing"===e.status?((0,n.uX)(),(0,n.CE)("span",dn,"⚙️")):((0,n.uX)(),(0,n.CE)("span",gn,"📄"))],2),(0,n.Lk)("div",null,[(0,n.Lk)("h5",un,(0,a.v_)(e.name),1),(0,n.Lk)("p",hn,(0,a.v_)(i.formatFileSize(e.size)),1),(0,n.Lk)("div",mn,[e.segments.length>0?((0,n.uX)(),(0,n.CE)("span",pn," 已分段: "+(0,a.v_)(e.segments.length)+" 个 ",1)):(0,n.Q3)("",!0),e.aiEnhanced?((0,n.uX)(),(0,n.CE)("span",bn," AI增强 ")):(0,n.Q3)("",!0),e.analysis?((0,n.uX)(),(0,n.CE)("span",fn,(0,a.v_)(e.analysis.documentType),1)):(0,n.Q3)("",!0)])])]),(0,n.Lk)("div",xn,[(0,n.Lk)("div",yn,["error"===e.status?((0,n.uX)(),(0,n.CE)("div",kn," 解析失败 ")):"parsing"===e.status?((0,n.uX)(),(0,n.CE)("div",vn," 正在解析... ")):"completed"===e.status?((0,n.uX)(),(0,n.CE)("div",wn,(0,a.v_)(e.progress)+"% 完成 ",1)):((0,n.uX)(),(0,n.CE)("div",Ln,(0,a.v_)(e.progress)+"% 上传中 ",1)),"completed"!==e.status&&"error"!==e.status?((0,n.uX)(),(0,n.CE)("div",Cn,[(0,n.Lk)("div",{class:"h-1 bg-blue-500 rounded-full transition-all duration-300",style:(0,a.Tr)({width:e.progress+"%"})},null,4)])):(0,n.Q3)("",!0)]),(0,n.Lk)("button",{onClick:t=>i.removeFile(e.id),class:"text-red-500 hover:text-red-700"}," 🗑️ ",8,Sn)])]))),128))])])):(0,n.Q3)("",!0)])])):(0,n.Q3)("",!0),2===l.currentStep?((0,n.uX)(),(0,n.CE)("div",Tn,[(0,n.Lk)("div",En,[(0,n.Lk)("div",Mn,[t[29]||(t[29]=(0,n.Lk)("h4",{class:"text-lg font-semibold text-gray-900 mb-4"},"文档解析设置",-1)),(0,n.Lk)("div",An,[(0,n.Lk)("div",null,[t[27]||(t[27]=(0,n.Lk)("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"解析模式",-1)),(0,n.Lk)("div",Bn,[(0,n.Lk)("label",Kn,[(0,n.bo)((0,n.Lk)("input",{type:"checkbox","onUpdate:modelValue":t[6]||(t[6]=e=>l.settings.parseText=e),class:"mr-2"},null,512),[[o.lH,l.settings.parseText]]),t[25]||(t[25]=(0,n.Lk)("span",null,"纯文本 (OCR)",-1))]),(0,n.Lk)("label",In,[(0,n.bo)((0,n.Lk)("input",{type:"checkbox","onUpdate:modelValue":t[7]||(t[7]=e=>l.settings.parseTable=e),class:"mr-2"},null,512),[[o.lH,l.settings.parseTable]]),t[26]||(t[26]=(0,n.Lk)("span",null,"表格元素",-1))])])]),(0,n.Lk)("div",null,[t[28]||(t[28]=(0,n.Lk)("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"内容过滤",-1)),(0,n.bo)((0,n.Lk)("textarea",{"onUpdate:modelValue":t[8]||(t[8]=e=>l.settings.contentFilter=e),placeholder:"输入需要过滤的内容关键词，多个关键词用换行分隔",class:"w-full h-24 p-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"},null,512),[[o.Jo,l.settings.contentFilter]])])])]),l.aiEnabled?((0,n.uX)(),(0,n.CE)("div",_n,[t[32]||(t[32]=(0,n.Lk)("h4",{class:"text-lg font-semibold text-gray-900 mb-3 flex items-center"},[(0,n.Lk)("span",{class:"w-2 h-2 bg-green-500 rounded-full mr-2"}),(0,n.eW)(" AI智能处理 ")],-1)),(0,n.Lk)("div",jn,[(0,n.Lk)("div",Dn,[(0,n.bo)((0,n.Lk)("input",{"onUpdate:modelValue":t[9]||(t[9]=e=>l.settings.useAI=e),type:"checkbox",id:"useAI",class:"w-4 h-4 text-blue-600 border-gray-300 rounded"},null,512),[[o.lH,l.settings.useAI]]),t[30]||(t[30]=(0,n.Lk)("label",{for:"useAI",class:"ml-2 text-sm text-gray-700"},"启用AI增强分段",-1))]),(0,n.Lk)("div",Fn,[(0,n.bo)((0,n.Lk)("input",{"onUpdate:modelValue":t[10]||(t[10]=e=>l.settings.fallbackToLocal=e),type:"checkbox",id:"fallback",class:"w-4 h-4 text-blue-600 border-gray-300 rounded"},null,512),[[o.lH,l.settings.fallbackToLocal]]),t[31]||(t[31]=(0,n.Lk)("label",{for:"fallback",class:"ml-2 text-sm text-gray-700"},"AI失败时本地备选",-1))])]),t[33]||(t[33]=(0,n.Lk)("p",{class:"text-xs text-gray-600"}," AI增强功能可提供更准确的语义分段、关键词提取和摘要生成 ",-1))])):(0,n.Q3)("",!0),(0,n.Lk)("div",$n,[t[39]||(t[39]=(0,n.Lk)("h4",{class:"text-lg font-semibold text-gray-900 mb-4"},"分段设置",-1)),(0,n.Lk)("div",Pn,[(0,n.Lk)("div",null,[t[35]||(t[35]=(0,n.Lk)("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"分段策略",-1)),(0,n.bo)((0,n.Lk)("select",{"onUpdate:modelValue":t[11]||(t[11]=e=>l.settings.segmentStrategy=e),class:"w-full p-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"},t[34]||(t[34]=[(0,n.Lk)("option",{value:"auto"},"自动分段",-1),(0,n.Lk)("option",{value:"manual"},"手动分段",-1),(0,n.Lk)("option",{value:"fixed"},"固定长度",-1)]),512),[[o.u1,l.settings.segmentStrategy]])]),(0,n.Lk)("div",null,[t[36]||(t[36]=(0,n.Lk)("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"段落长度",-1)),(0,n.bo)((0,n.Lk)("input",{type:"number","onUpdate:modelValue":t[12]||(t[12]=e=>l.settings.segmentLength=e),placeholder:"建议500-2000字符",class:"w-full p-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"},null,512),[[o.Jo,l.settings.segmentLength]])]),(0,n.Lk)("div",null,[t[37]||(t[37]=(0,n.Lk)("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"段落重叠",-1)),(0,n.bo)((0,n.Lk)("input",{type:"number","onUpdate:modelValue":t[13]||(t[13]=e=>l.settings.segmentOverlap=e),placeholder:"重叠字符数，建议50-200",class:"w-full p-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"},null,512),[[o.Jo,l.settings.segmentOverlap]])]),(0,n.Lk)("div",null,[t[38]||(t[38]=(0,n.Lk)("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"分隔符",-1)),(0,n.bo)((0,n.Lk)("input",{type:"text","onUpdate:modelValue":t[14]||(t[14]=e=>l.settings.separator=e),placeholder:"自定义分段分隔符",class:"w-full p-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"},null,512),[[o.Jo,l.settings.separator]])])])]),(0,n.Lk)("div",Rn,[t[44]||(t[44]=(0,n.Lk)("h4",{class:"text-lg font-semibold text-gray-900 mb-4"},"索引设置",-1)),(0,n.Lk)("div",Xn,[(0,n.Lk)("div",null,[t[41]||(t[41]=(0,n.Lk)("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"关键词提取",-1)),(0,n.Lk)("label",Vn,[(0,n.bo)((0,n.Lk)("input",{type:"checkbox","onUpdate:modelValue":t[15]||(t[15]=e=>l.settings.extractKeywords=e),class:"mr-2"},null,512),[[o.lH,l.settings.extractKeywords]]),t[40]||(t[40]=(0,n.Lk)("span",null,"自动提取关键词用于检索优化",-1))])]),(0,n.Lk)("div",null,[t[43]||(t[43]=(0,n.Lk)("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"摘要生成",-1)),(0,n.Lk)("label",zn,[(0,n.bo)((0,n.Lk)("input",{type:"checkbox","onUpdate:modelValue":t[16]||(t[16]=e=>l.settings.generateSummary=e),class:"mr-2"},null,512),[[o.lH,l.settings.generateSummary]]),t[42]||(t[42]=(0,n.Lk)("span",null,"为每个段落生成摘要",-1))])])])])])])):(0,n.Q3)("",!0),3===l.currentStep?((0,n.uX)(),(0,n.CE)("div",On,[(0,n.Lk)("div",Nn,[(0,n.Lk)("div",Un,[t[45]||(t[45]=(0,n.Lk)("h4",{class:"text-lg font-semibold text-gray-900 mb-4"},"文档列表",-1)),(0,n.Lk)("div",Wn,[((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(l.uploadedFiles,e=>((0,n.uX)(),(0,n.CE)("div",{key:e.id,onClick:t=>l.selectedFileId=e.id,class:(0,a.C4)(["p-3 border rounded-lg cursor-pointer transition-colors",l.selectedFileId===e.id?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"])},[(0,n.Lk)("div",Jn,[(0,n.Lk)("div",Hn,[(0,n.Lk)("div",{class:(0,a.C4)(["w-3 h-3 rounded-full","completed"===e.status?"bg-green-500":"error"===e.status?"bg-red-500":"bg-blue-500"])},null,2),(0,n.Lk)("h5",Gn,(0,a.v_)(e.name),1)]),"parsing"===e.status?((0,n.uX)(),(0,n.CE)("span",qn,"解析中...")):"error"===e.status?((0,n.uX)(),(0,n.CE)("span",Zn,"解析失败")):(0,n.Q3)("",!0)]),(0,n.Lk)("div",Yn,[(0,n.Lk)("p",ea,(0,a.v_)(e.segments?.length||0)+" 个分段",1),(0,n.Lk)("p",ta,(0,a.v_)(i.formatFileSize(e.size)),1)]),e.error?((0,n.uX)(),(0,n.CE)("div",sa," 错误: "+(0,a.v_)(e.error),1)):(0,n.Q3)("",!0)],10,Qn))),128))])]),(0,n.Lk)("div",oa,[(0,n.Lk)("div",na,[t[46]||(t[46]=(0,n.Lk)("h4",{class:"text-lg font-semibold text-gray-900"},"分段预览",-1)),(0,n.Lk)("div",aa," 共 "+(0,a.v_)(i.getCurrentFileSegments().length)+" 个分段 ",1)]),(0,n.Lk)("div",ra,[((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(i.getCurrentFileSegments(),e=>((0,n.uX)(),(0,n.CE)("div",{key:e.id,class:"border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors"},[(0,n.Lk)("div",la,[(0,n.Lk)("div",ia,[(0,n.Lk)("span",ca,"分段 "+(0,a.v_)(e.id),1),e.aiEnhanced?((0,n.uX)(),(0,n.CE)("span",da," AI增强 ")):(0,n.Q3)("",!0),e.source?((0,n.uX)(),(0,n.CE)("span",ga,(0,a.v_)(e.source),1)):(0,n.Q3)("",!0)]),(0,n.Lk)("div",ua,[(0,n.Lk)("span",ha,(0,a.v_)(e.content.length)+" 字符",1),(0,n.Lk)("span",ma,(0,a.v_)(e.keywords.length)+" 关键词",1)])]),(0,n.Lk)("div",pa,(0,a.v_)(e.content.substring(0,300))+(0,a.v_)(e.content.length>300?"...":""),1),e.keywords.length>0?((0,n.uX)(),(0,n.CE)("div",ba,[t[47]||(t[47]=(0,n.Lk)("span",{class:"text-xs text-gray-600 mr-2"},"关键词:",-1)),((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(e.keywords,e=>((0,n.uX)(),(0,n.CE)("span",{key:e,class:"inline-block px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded mr-1"},(0,a.v_)(e),1))),128))])):(0,n.Q3)("",!0),e.summary?((0,n.uX)(),(0,n.CE)("div",fa,[t[48]||(t[48]=(0,n.Lk)("span",{class:"text-xs text-gray-600"},"摘要:",-1)),(0,n.Lk)("p",xa,(0,a.v_)(e.summary),1)])):(0,n.Q3)("",!0),(0,n.Lk)("div",ya,[(0,n.Lk)("div",ka," 创建时间: "+(0,a.v_)(i.formatDate(e.createdAt)),1),(0,n.Lk)("div",va,[(0,n.Lk)("button",{onClick:t=>i.editSegment(e),class:"text-xs text-blue-600 hover:text-blue-800"},"编辑",8,wa),(0,n.Lk)("button",{onClick:t=>i.deleteSegment(e.id),class:"text-xs text-red-600 hover:text-red-800"},"删除",8,La)])])]))),128)),0===i.getCurrentFileSegments().length?((0,n.uX)(),(0,n.CE)("div",Ca,t[49]||(t[49]=[(0,n.Lk)("div",{class:"text-gray-400 mb-2"},"📄",-1),(0,n.Lk)("p",{class:"text-sm text-gray-600"},"该文件暂无分段内容",-1)]))):(0,n.Q3)("",!0)])])])])):(0,n.Q3)("",!0),4===l.currentStep?((0,n.uX)(),(0,n.CE)("div",Sa,[(0,n.Lk)("div",Ta,[t[59]||(t[59]=(0,n.Lk)("div",{class:"w-24 h-24 mx-auto mb-6 bg-blue-100 rounded-full flex items-center justify-center"},[(0,n.Lk)("div",{class:"w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"})],-1)),t[60]||(t[60]=(0,n.Lk)("h4",{class:"text-xl font-semibold text-gray-900 mb-2"},"正在处理知识库数据",-1)),t[61]||(t[61]=(0,n.Lk)("p",{class:"text-gray-600 mb-6"},"请稍候，正在为您的知识库建立索引...",-1)),i.getProcessingStats()?((0,n.uX)(),(0,n.CE)("div",Ea,[(0,n.Lk)("div",Ma,[(0,n.Lk)("div",Aa,(0,a.v_)(i.getProcessingStats().totalFiles),1),t[50]||(t[50]=(0,n.Lk)("div",{class:"text-xs text-gray-600"},"总文件数",-1))]),(0,n.Lk)("div",Ba,[(0,n.Lk)("div",Ka,(0,a.v_)(i.getProcessingStats().totalSegments),1),t[51]||(t[51]=(0,n.Lk)("div",{class:"text-xs text-gray-600"},"总分段数",-1))]),(0,n.Lk)("div",Ia,[(0,n.Lk)("div",_a,(0,a.v_)(i.getProcessingStats().aiEnhancedFiles),1),t[52]||(t[52]=(0,n.Lk)("div",{class:"text-xs text-gray-600"},"AI增强文件",-1))]),(0,n.Lk)("div",ja,[(0,n.Lk)("div",Da,(0,a.v_)(i.getProcessingStats().avgSegmentsPerFile),1),t[53]||(t[53]=(0,n.Lk)("div",{class:"text-xs text-gray-600"},"平均分段数",-1))])])):(0,n.Q3)("",!0),(0,n.Lk)("div",Fa,[(0,n.Lk)("div",$a,[(0,n.Lk)("div",{class:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:(0,a.Tr)({width:l.processingProgress+"%"})},null,4)]),(0,n.Lk)("p",Pa,(0,a.v_)(l.processingProgress)+"% 完成",1)]),(0,n.Lk)("div",Ra,[t[58]||(t[58]=(0,n.Lk)("h5",{class:"font-semibold text-gray-900 mb-2"},"处理进度：",-1)),(0,n.Lk)("div",Xa,[t[56]||(t[56]=(0,n.Lk)("div",{class:"flex items-center"},[(0,n.Lk)("span",{class:"text-green-500 mr-2"},"✓"),(0,n.Lk)("span",null,"文档解析完成")],-1)),t[57]||(t[57]=(0,n.Lk)("div",{class:"flex items-center"},[(0,n.Lk)("span",{class:"text-green-500 mr-2"},"✓"),(0,n.Lk)("span",null,"内容分段完成")],-1)),(0,n.Lk)("div",Va,[(0,n.Lk)("span",{class:(0,a.C4)([l.processingProgress>=70?"text-green-500":"text-gray-400","mr-2"])},(0,a.v_)(l.processingProgress>=70?"✓":"○"),3),t[54]||(t[54]=(0,n.Lk)("span",null,"向量化处理",-1))]),(0,n.Lk)("div",za,[(0,n.Lk)("span",{class:(0,a.C4)([l.processingProgress>=100?"text-green-500":"text-gray-400","mr-2"])},(0,a.v_)(l.processingProgress>=100?"✓":"○"),3),t[55]||(t[55]=(0,n.Lk)("span",null,"索引建立",-1))])])])])])):(0,n.Q3)("",!0)]),(0,n.Lk)("div",Oa,[l.currentStep>1&&l.currentStep<4?((0,n.uX)(),(0,n.CE)("button",{key:0,onClick:t[17]||(t[17]=(...e)=>i.previousStep&&i.previousStep(...e)),class:"px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors"}," 上一步 ")):((0,n.uX)(),(0,n.CE)("div",Na)),(0,n.Lk)("div",Ua,[(0,n.Lk)("button",{onClick:t[18]||(t[18]=t=>e.$emit("close")),class:"px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors"}," 取消 "),l.currentStep<4?((0,n.uX)(),(0,n.CE)("button",{key:0,onClick:t[19]||(t[19]=(...e)=>i.nextStep&&i.nextStep(...e)),disabled:!i.canProceedToNext(),class:"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"},(0,a.v_)(3===l.currentStep?"开始处理":"下一步"),9,Wa)):(0,n.Q3)("",!0),4===l.currentStep&&100===l.processingProgress?((0,n.uX)(),(0,n.CE)("button",{key:1,onClick:t[20]||(t[20]=(...e)=>i.completeCreation&&i.completeCreation(...e)),class:"px-6 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"}," 完成创建 ")):(0,n.Q3)("",!0)])])])])):(0,n.Q3)("",!0)}s(8237),s(7642),s(8004),s(3853),s(5876),s(2475),s(5024),s(1698);var Ja=s(1208);class Ha{constructor(){this.supportedTypes={"text/plain":"txt","text/markdown":"md","application/msword":"doc","application/vnd.openxmlformats-officedocument.wordprocessingml.document":"docx"}}async parseDocument(e){const t=this.getFileType(e);switch(t){case"txt":return await this.parseTextFile(e);case"md":return await this.parseMarkdownFile(e);case"doc":case"docx":return await this.parseWordFile(e);default:throw new Error(`不支持的文件类型: ${e.type}。支持的格式：TXT、Markdown、Word文档(.docx)`)}}getFileType(e){if(this.supportedTypes[e.type])return this.supportedTypes[e.type];const t=e.name.split(".").pop().toLowerCase(),s={txt:"txt",md:"md",markdown:"md",doc:"doc",docx:"docx"};return s[t]||"unknown"}async fileToArrayBuffer(e){return new Promise((t,s)=>{const o=new FileReader;o.onload=e=>t(e.target.result),o.onerror=()=>s(new Error("文件读取失败")),o.readAsArrayBuffer(e)})}async parseTextFile(e){try{let s=await this.readTextWithEncoding(e,"UTF-8");if(this.containsGarbledText(s)){console.warn("检测到可能的编码问题，尝试GBK编码");try{s=await this.readTextWithEncoding(e,"GBK")}catch(t){console.warn("GBK编码读取失败，使用UTF-8结果")}}if(s=this.cleanTextContent(s),!s||s.length<10)throw new Error("文本文件内容过短或为空");return s}catch(s){throw new Error(`文本文件解析失败: ${s.message}`)}}async readTextWithEncoding(e,t){return new Promise((s,o)=>{const n=new FileReader;n.onload=e=>s(e.target.result),n.onerror=()=>o(new Error(`使用${t}编码读取失败`)),n.readAsText(e,t)})}containsGarbledText(e){const t=[/[��]/g,/[\uFFFD]/g,/[^\u0020-\u007F\u4e00-\u9fa5\u3000-\u303F\uFF00-\uFFEF]/g];return t.some(t=>t.test(e))}cleanTextContent(e){return e.replace(/\r\n/g,"\n").replace(/\r/g,"\n").replace(/\n{4,}/g,"\n\n\n").replace(/[ \t]+/g," ").replace(/^\s+|\s+$/gm,"").trim()}async parseMarkdownFile(e){try{let t=await this.parseTextFile(e);return t=this.cleanMarkdownContent(t),t}catch(t){throw new Error(`Markdown文件解析失败: ${t.message}`)}}cleanMarkdownContent(e){return e.replace(/^#{1,6}\s+/gm,e=>e).replace(/\[([^\]]+)\]\([^)]+\)/g,"$1").replace(/!\[([^\]]*)\]\([^)]+\)/g,"[图片: $1]").replace(/\*\*([^*]+)\*\*/g,"$1").replace(/\*([^*]+)\*/g,"$1").replace(/__([^_]+)__/g,"$1").replace(/_([^_]+)_/g,"$1").replace(/```[\s\S]*?```/g,e=>e.replace(/```\w*\n?/g,"").replace(/```/g,"")).replace(/`([^`]+)`/g,"$1").replace(/^>\s*/gm,"").replace(/^[\s]*[-*+]\s+/gm,"• ").replace(/^[\s]*\d+\.\s+/gm,(e,t,s)=>{const o=s.lastIndexOf("\n",t)+1,n=t-o;return" ".repeat(n)+"1. "}).replace(/\n{3,}/g,"\n\n").trim()}async parseWordFile(e){try{const t=await this.fileToArrayBuffer(e),s=this.getFileType(e);if(console.log(`开始解析Word文档: ${e.name}, 类型: ${s}, 大小: ${e.size} bytes`),"doc"===s)throw new Error("不支持旧版.doc格式文档。请将文档转换为.docx格式后重试。");const o=await Ja.extractRawText({arrayBuffer:t});if(console.log("mammoth解析结果:",{hasValue:!!o.value,valueLength:o.value?o.value.length:0,messagesCount:o.messages?o.messages.length:0}),!o.value||!o.value.trim()){console.log("尝试使用HTML提取方式...");const e=await Ja.convertToHtml({arrayBuffer:t});if(e.value){const t=this.extractTextFromHTML(e.value);if(t&&t.trim())return console.log("HTML提取成功，文本长度:",t.length),this.cleanWordText(t)}throw new Error("Word文档中未找到可提取的文本内容。请检查文档是否包含文本或尝试重新保存文档。")}o.messages&&o.messages.length>0&&console.warn("Word文档解析警告:",o.messages.map(e=>e.message));const n=this.cleanWordText(o.value);return console.log("解析完成，最终文本长度:",n.length),n}catch(t){throw console.error("Word文档解析失败:",t),new Error(`Word文档解析失败: ${t.message}`)}}extractTextFromHTML(e){const t=document.createElement("div");t.innerHTML=e;const s=t.querySelectorAll("script, style");s.forEach(e=>e.remove());let o=t.textContent||t.innerText||"";return o.replace(/\s+/g," ").replace(/\n\s*\n/g,"\n\n").trim()}cleanWordText(e){return e?e.replace(/\r\n/g,"\n").replace(/\r/g,"\n").replace(/\n{3,}/g,"\n\n").replace(/\t+/g," ").replace(/[ ]{2,}/g," ").replace(/^\s+|\s+$/gm,"").trim():""}segmentDocument(e,t={}){const{strategy:s="auto",maxLength:o=1e3,overlap:n=100,separator:a="\n\n"}=t;switch(s){case"auto":return this.autoSegment(e,o,n);case"fixed":return this.fixedLengthSegment(e,o,n);case"manual":return this.manualSegment(e,a);default:return this.autoSegment(e,o,n)}}autoSegment(e,t=1e3,s=100){if(console.log("开始自动分段，内容长度:",e.length,"最大分段长度:",t,"重叠长度:",s),!e||0===e.trim().length)return console.warn("内容为空，无法分段"),[];const o=[];try{const n=this.splitByHeaders(e);console.log("按标题分割结果:",n.length,"个章节");let a=1;for(let e=0;e<n.length;e++){const r=n[e];if(console.log(`处理第${e+1}个章节，长度:`,r.length),r.length<=t){const e=this.createSegment(a++,r.trim());o.push(e),console.log(`创建分段 ${e.id}，长度: ${e.length}`)}else{console.log(`章节过长(${r.length}字符)，需要进一步分段`);const e=this.splitLongSection(r,t,s);console.log(`分割为 ${e.length} 个子分段`),e.forEach(e=>{const t=this.createSegment(a++,e.trim());o.push(t),console.log(`创建子分段 ${t.id}，长度: ${t.length}`)})}}return console.log("自动分段完成，共生成",o.length,"个分段"),o}catch(n){return console.error("自动分段失败:",n),console.log("回退到固定长度分段"),this.fixedLengthSegment(e,t,s)}}splitByHeaders(e){console.log("开始按标题分割，内容长度:",e.length);const t=[/^第[一二三四五六七八九十\d]+[章节部分]/m,/^\d+\.\s+/m,/^[一二三四五六七八九十]+[、.]/m,/^#{1,6}\s+/m,/^.{1,50}：\s*$/m],s=e.split("\n"),o=[];let n="";for(const a of s){const e=t.some(e=>e.test(a.trim()));e&&n.trim()?(o.push(n.trim()),n=a):n+=(n?"\n":"")+a}if(n.trim()&&o.push(n.trim()),console.log("标题分割结果:",o.length,"个章节"),o.length<=1){console.log("未识别到标题，按段落分割");const t=e.split(/\n\s*\n/).filter(e=>e.trim());if(console.log("段落分割结果:",t.length,"个段落"),t.length<=2){console.log("段落太少，按句子分割");const t=e.split(/[。！？.!?]\s*/).filter(e=>e.trim().length>20);return console.log("句子分割结果:",t.length,"个句子"),t.length>1?t:[e]}return t}return o}splitLongSection(e,t,s){console.log(`分割长章节，原长度: ${e.length}, 最大长度: ${t}`);const o=e.split(/\n\s*\n/).filter(e=>e.trim());console.log(`段落数量: ${o.length}`);const n=[];let a="";for(const r of o){const e=r.trim();if(a&&a.length+e.length+2>t)if(console.log(`创建子分段，长度: ${a.length}`),n.push(a.trim()),s>0&&a.length>s){const t=this.getLastSentences(a,s);a=t+"\n\n"+e}else a=e;else a+=(a?"\n\n":"")+e}return a.trim()&&(console.log(`添加最后子分段，长度: ${a.length}`),n.push(a.trim())),1===n.length&&n[0].length>t?(console.log("段落分割无效，强制按字符分割"),this.forceCharacterSplit(e,t,s)):(console.log(`分割完成，生成 ${n.length} 个子分段`),n)}forceCharacterSplit(e,t,s){console.log("执行强制字符分割");const o=[];let n=0;while(n<e.length){let a=n+t;if(a<e.length){const s=e.slice(n,a+100).search(/[。！？.!?]/);-1!==s&&s<.8*t&&(a=n+s+1)}const r=e.slice(n,Math.min(a,e.length));o.push(r.trim()),n=Math.max(n+1,a-s)}return console.log(`强制分割完成，生成 ${o.length} 个分段`),o.filter(e=>e.length>0)}getLastSentences(e,t){const s=e.split(/[。！？.!?]/).filter(e=>e.trim());let o="";for(let n=s.length-1;n>=0;n--){const e=s[n].trim();if(!(o.length+e.length<=t))break;o=e+(o?"。"+o:"")}return o||e.slice(-t)}fixedLengthSegment(e,t,s){const o=[];let n=1,a=0;while(a<e.length){let r=Math.min(a+t,e.length);if(r<e.length){const s=e.lastIndexOf(" ",r),o=e.lastIndexOf("\n",r),n=Math.max(s,o);n>a+.8*t&&(r=n)}const l=e.slice(a,r).trim();l&&o.push(this.createSegment(n++,l)),a=Math.max(r-s,a+1)}return o}manualSegment(e,t){const s=[],o=e.split(t).filter(e=>e.trim());return o.forEach((e,t)=>{s.push(this.createSegment(t+1,e.trim()))}),s}createSegment(e,t){return{id:e,content:t,keywords:this.extractKeywords(t),summary:this.generateSummary(t),length:t.length,createdAt:new Date}}extractKeywords(e){const t=new Set(["的","了","在","是","我","有","和","就","不","人","都","一","一个","上","也","很","到","说","要","去","你","会","着","没有","看","好","自己","这","那","它","他","她","们","我们","你们","他们","这个","那个","这些","那些","什么","怎么","为什么","哪里","哪个","如何","因为","所以","但是","然后","可以","应该","需要","能够","可能","或者","以及","而且","并且","虽然","尽管","如果","假如","除非","直到","当","while","the","a","an","and","or","but","in","on","at","to","for","of","with","by","is","are","was","were","be","been","have","has","had","do","does","did","will","would","could","should","may","might","can","this","that","these","those","i","you","he","she","it","we","they","me","him","her","us","them"]),s=e.match(/[\u4e00-\u9fa5]{2,}/g)||[],o=e.match(/[a-zA-Z]{3,}/g)||[],n=[...s,...o],a={};n.forEach(e=>{const s=e.toLowerCase();!t.has(s)&&e.length>=2&&(a[e]=(a[e]||0)+1)});const r=n.length,l=Object.entries(a).map(([t,s])=>({word:t,count:s,tf:s/r,score:s*Math.log(e.length/t.length)})).sort((e,t)=>t.score-e.score).slice(0,8).map(e=>e.word);return l}generateSummary(e){try{if(console.log("开始生成摘要，内容长度:",e.length),!e||0===e.trim().length)return console.warn("内容为空，无法生成摘要"),"内容为空";const t=e.replace(/\s+/g," ").trim();if(t.length<=150)return console.log("内容较短，直接返回原内容"),t;const s=t.split(/[。！？.!?]/).filter(e=>e.trim().length>10);if(console.log("分割句子数量:",s.length),s.length<=2){const e=t.substring(0,150)+"...";return console.log("句子较少，返回截取摘要"),e}const o=s.map(t=>{const o=t.match(/[\u4e00-\u9fa5]+|[a-zA-Z]+/g)||[],n=this.extractKeywords(e);let a=0;o.forEach(e=>{n.includes(e)&&a++});const r={sentence:t.trim(),keywordDensity:a/o.length,position:s.indexOf(t),length:t.length},l=t===s[0]?1.5:t===s[s.length-1]?1.2:1;return r.totalScore=r.keywordDensity*l,r}),n=o.sort((e,t)=>t.totalScore-e.totalScore).slice(0,2).sort((e,t)=>e.position-t.position).map(e=>e.sentence);let a=n.join("。");return a.length>200?a=a.substring(0,200)+"...":a.endsWith("。")||a.endsWith("...")||(a+="。"),console.log("摘要生成完成，长度:",a.length),a}catch(t){console.error("摘要生成失败:",t);const s=e.substring(0,100)+"...";return console.log("使用回退摘要:",s),s}}}var Ga=new Ha;class qa{constructor(e={}){this.accessKeyId=e.accessKeyId||"LTAI5tB38aLEpHToKESq9W3T",this.accessKeySecret=e.accessKeySecret||"******************************",this.workspaceId=e.workspaceId||"llm-xz4u83kacijf8jaj",this.apiKey=e.apiKey||"sk-0baf684faf044823a593986c15617b12",this.baseUrl="https://dashscope.aliyuncs.com/api/v1",this.enabled=!0}isEnabled(){return this.enabled&&this.apiKey&&this.workspaceId}async callAPI(e,t={}){if(!this.isEnabled())throw new Error("百炼服务未启用或配置不完整");try{const s="http://localhost:8080/api/knowledge-base";let o="";if(e.includes("text-generation")&&t.input&&t.input.prompt){const e=t.input.prompt;e.includes("分段")||e.includes("段落")?(o="/segment-document",t={content:this.extractContentFromPrompt(e),maxLength:1e3,strategy:"auto"}):e.includes("关键词")?(o="/extract-keywords",t={content:this.extractContentFromPrompt(e)}):e.includes("摘要")?(o="/generate-summary",t={content:this.extractContentFromPrompt(e)}):e.includes("分析")&&(o="/analyze-document",t={content:this.extractContentFromPrompt(e)})}if(!o)throw new Error("不支持的API调用类型");console.log(`通过后端调用API: ${o}`,t);const n=await fetch(`${s}${o}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!n.ok)throw new Error(`后端API调用失败: ${n.status} ${n.statusText}`);const a=await n.json();return console.log("后端API调用成功:",a),this.convertBackendResponse(a,o)}catch(s){throw console.error("API调用错误:",s),s}}extractContentFromPrompt(e){const t=e.split("\n");for(let s=0;s<t.length;s++){const e=t[s].trim();if(e&&!e.includes("请")&&!e.includes("：")&&e.length>20)return t.slice(s).join("\n")}return e}convertBackendResponse(e,t){switch(t){case"/segment-document":return{output:{text:JSON.stringify({segments:e.segments})}};case"/extract-keywords":return{output:{text:e.keywords.join(",")}};case"/generate-summary":return{output:{text:e.summary}};case"/analyze-document":return{output:{text:JSON.stringify(e)}};default:return e}}async segmentDocument(e,t={}){const{maxLength:s=1e3}=t,o=`请对以下文档进行智能分段。要求：\n1. 按照语义边界进行分段，保持内容的完整性\n2. 每个分段长度控制在${s}字符以内\n3. 为每个分段提取3-5个关键词\n4. 为每个分段生成简洁的摘要（50字以内）\n5. 返回JSON格式，包含segments数组，每个元素包含：id, content, keywords, summary\n\n文档内容：\n${e}\n\n请返回标准的JSON格式结果。`;try{const t=await this.callAPI("/services/aigc/text-generation/generation",{model:"qwen-turbo",input:{prompt:o},parameters:{max_tokens:4e3,temperature:.1}});if(t.output&&t.output.text)try{const e=t.output.text.match(/\{[\s\S]*\}/);if(e){const t=JSON.parse(e[0]);return this.formatSegments(t.segments||[])}}catch(n){return console.warn("解析百炼响应失败，使用备用解析方法"),this.parseTextResponse(t.output.text,e)}throw new Error("百炼服务返回格式异常")}catch(a){throw console.error("百炼分段失败:",a),a}}parseTextResponse(e,t){const s=[],o=e.split("\n").filter(e=>e.trim());let n=null,a=1;for(const r of o)if(r.includes("分段")||r.includes("段落"))n&&s.push(n),n={id:a++,content:"",keywords:[],summary:""};else if(r.includes("关键词")||r.includes("关键字")){if(n){const e=r.replace(/.*[关键词字][:：]?\s*/,"").split(/[,，、\s]+/).filter(e=>e.trim());n.keywords=e.slice(0,5)}}else r.includes("摘要")||r.includes("总结")?n&&(n.summary=r.replace(/.*[摘要总结][:：]?\s*/,"").trim()):!n||!r.trim()||r.includes("JSON")||r.includes("{")||r.includes("}")||(n.content+=(n.content?"\n":"")+r.trim());return n&&s.push(n),s.length>0?s:this.fallbackSegmentation(t)}fallbackSegmentation(e){const t=[],s=e.split(/\n\s*\n/).filter(e=>e.trim());return s.forEach((e,s)=>{t.push({id:s+1,content:e.trim(),keywords:this.extractSimpleKeywords(e),summary:this.generateSimpleSummary(e)})}),t}extractSimpleKeywords(e){const t=e.match(/[\u4e00-\u9fa5]{2,}|[a-zA-Z]{3,}/g)||[],s={};return t.forEach(e=>{s[e]=(s[e]||0)+1}),Object.entries(s).sort(([,e],[,t])=>t-e).slice(0,5).map(([e])=>e)}generateSimpleSummary(e){const t=e.split(/[。！？.!?]/).filter(e=>e.trim());return t[0]?t[0].trim().substring(0,50)+"...":e.substring(0,50)+"..."}formatSegments(e){return e.map((e,t)=>({id:e.id||t+1,content:e.content||"",keywords:Array.isArray(e.keywords)?e.keywords:[],summary:e.summary||"",length:(e.content||"").length,createdAt:new Date,source:"bailian"}))}async extractKeywords(e){const t=`请从以下文本中提取5-8个最重要的关键词，用逗号分隔：\n${e.substring(0,1e3)}`;try{const e=await this.callAPI("/services/aigc/text-generation/generation",{model:"qwen-turbo",input:{prompt:t},parameters:{max_tokens:100,temperature:.1}});return e.output&&e.output.text?e.output.text.split(/[,，、\s]+/).filter(e=>e.trim()).slice(0,8):[]}catch(s){return console.error("关键词提取失败:",s),this.extractSimpleKeywords(e)}}async generateSummary(e){const t=`请为以下文本生成一个简洁的摘要（50字以内）：\n${e.substring(0,1e3)}`;try{const s=await this.callAPI("/services/aigc/text-generation/generation",{model:"qwen-turbo",input:{prompt:t},parameters:{max_tokens:100,temperature:.1}});return s.output&&s.output.text?s.output.text.trim().substring(0,200):this.generateSimpleSummary(e)}catch(s){return console.error("摘要生成失败:",s),this.generateSimpleSummary(e)}}async analyzeDocument(e){const t=`请分析以下文档的结构和质量，包括：\n1. 文档类型（技术文档、学术论文、新闻报道等）\n2. 结构完整性（1-10分）\n3. 内容质量（1-10分）\n4. 建议的分段策略\n5. 主要主题\n\n文档内容：\n${e.substring(0,2e3)}\n\n请返回JSON格式结果。`;try{const e=await this.callAPI("/services/aigc/text-generation/generation",{model:"qwen-turbo",input:{prompt:t},parameters:{max_tokens:500,temperature:.1}});if(e.output&&e.output.text)try{const t=e.output.text.match(/\{[\s\S]*\}/);if(t)return JSON.parse(t[0])}catch(s){}return{documentType:"通用文档",structureScore:7,qualityScore:7,recommendedStrategy:"auto",mainTopics:["通用内容"]}}catch(o){return console.error("文档分析失败:",o),{documentType:"未知",structureScore:5,qualityScore:5,recommendedStrategy:"auto",mainTopics:[]}}}async batchProcess(e,t){const s=[],o=e.length;for(let a=0;a<e.length;a++){const r=e[a];try{t&&t({current:a+1,total:o,status:"processing",document:r.name});const n=await this.segmentDocument(r.content);s.push({...r,segments:n,status:"completed",processedAt:new Date}),a<e.length-1&&await new Promise(e=>setTimeout(e,1e3))}catch(n){console.error(`处理文档 ${r.name} 失败:`,n),s.push({...r,segments:[],status:"error",error:n.message,processedAt:new Date})}}return t&&t({current:o,total:o,status:"completed"}),s}}var Za=qa;class Ya{constructor(e={}){this.localParser=Ga,this.bailianService=new Za(e.bailian),this.config={useAI:!1!==e.useAI,fallbackToLocal:!1!==e.fallbackToLocal,maxFileSize:e.maxFileSize||10485760,aiTimeout:e.aiTimeout||3e4,...e}}async parseDocument(e){const t=Date.now();try{this.validateFile(e);const o=await this.localParser.parseDocument(e);let n=null;if(this.config.useAI&&this.bailianService.isEnabled())try{n=await Promise.race([this.bailianService.analyzeDocument(o),new Promise((e,t)=>setTimeout(()=>t(new Error("AI分析超时")),this.config.aiTimeout))])}catch(s){console.warn("AI文档分析失败，使用本地分析:",s.message),n=this.getLocalAnalysis(o)}else n=this.getLocalAnalysis(o);const a=Date.now()-t;return{fileName:e.name,fileSize:e.size,fileType:this.localParser.getFileType(e),content:o,analysis:n,parseTime:a,source:"enhanced",timestamp:new Date}}catch(s){throw console.error("文档解析失败:",s),new Error(`文档解析失败: ${s.message}`)}}async segmentDocument(e,t={},s=null){const{strategy:o="auto",maxLength:n=1e3,useAI:a=this.config.useAI}=t;try{if(a&&this.bailianService.isEnabled())try{const a=await Promise.race([this.bailianService.segmentDocument(e,{maxLength:n,strategy:this.getAIStrategy(o,s)}),new Promise((e,t)=>setTimeout(()=>t(new Error("AI分段超时")),this.config.aiTimeout))]);return this.postProcessSegments(a,t)}catch(r){if(console.warn("AI分段失败，回退到本地处理:",r.message),!this.config.fallbackToLocal)throw r}const i=this.localParser.segmentDocument(e,t);if(a&&this.bailianService.isEnabled()&&i.length>0)try{return await this.enhanceLocalSegments(i)}catch(l){return console.warn("AI增强失败，返回本地分段结果:",l.message),i}return i}catch(r){console.error("分段处理失败:",r),console.log("使用最简单的固定长度分段作为最后回退");try{return this.localParser.fixedLengthSegment(e,n,100)}catch(i){throw console.error("固定长度分段也失败:",i),r}}}async enhanceLocalSegments(e){const t=[];for(const o of e)try{const[e,s]=await Promise.all([this.bailianService.extractKeywords(o.content).catch(e=>(console.log("关键词提取失败，使用默认值:",e.message),o.keywords||[])),this.bailianService.generateSummary(o.content).catch(e=>(console.log("摘要生成失败，使用默认值:",e.message),o.summary||""))]);t.push({...o,keywords:e.length>0?e:o.keywords,summary:s||o.summary,enhanced:!0,source:"hybrid"}),await new Promise(e=>setTimeout(e,200))}catch(s){console.warn(`增强分段 ${o.id} 失败:`,s.message),t.push({...o,enhanced:!1,source:"local"})}return t}postProcessSegments(e,t){return e.map((e,s)=>({...e,id:e.id||s+1,length:e.content?e.content.length:0,createdAt:e.createdAt||new Date,settings:{strategy:t.strategy,maxLength:t.maxLength,overlap:t.overlap}}))}getAIStrategy(e,t){if("auto"!==e)return e;if(t&&t.recommendedStrategy)return t.recommendedStrategy;if(t&&t.documentType){const e=t.documentType.toLowerCase();if(e.includes("技术")||e.includes("manual"))return"semantic";if(e.includes("学术")||e.includes("论文"))return"structured";if(e.includes("新闻")||e.includes("报道"))return"paragraph"}return"semantic"}getLocalAnalysis(e){const t=e.split("\n"),s=e.match(/[\u4e00-\u9fa5]+|[a-zA-Z]+/g)||[];let o="通用文档";e.includes("摘要")||e.includes("Abstract")?o="学术论文":e.includes("API")||e.includes("接口")||e.includes("函数")?o="技术文档":(e.includes("新闻")||e.includes("报道"))&&(o="新闻报道");const n=/^#{1,6}\s+|^第[一二三四五六七八九十\d]+[章节]/.test(e),a=t.filter(e=>e.trim()).length>5,r=(n?5:0)+(a?3:0)+2,l=s.length/t.length,i=Math.min(10,Math.max(1,Math.round(l/2+3)));return{documentType:o,structureScore:Math.min(10,r),qualityScore:i,recommendedStrategy:n?"semantic":"auto",mainTopics:this.extractMainTopics(e),statistics:{totalLines:t.length,totalWords:s.length,avgWordsPerLine:Math.round(l)}}}extractMainTopics(e){const t=[],s=e.match(/^#{1,6}\s+(.+)$/gm)||[];if(s.forEach(e=>{const s=e.replace(/^#{1,6}\s+/,"").trim();s&&s.length<50&&t.push(s)}),0===t.length){const s=e.match(/[\u4e00-\u9fa5]{2,}/g)||[],o={};s.forEach(e=>{e.length>=2&&e.length<=6&&(o[e]=(o[e]||0)+1)});const n=Object.entries(o).sort(([,e],[,t])=>t-e).slice(0,5).map(([e])=>e);t.push(...n)}return t.slice(0,8)}validateFile(e){if(!e)throw new Error("文件不能为空");if(e.size>this.config.maxFileSize)throw new Error(`文件大小超过限制 (${this.config.maxFileSize/1024/1024}MB)`);const t=["text/plain","text/markdown","application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],s=this.localParser.getFileType(e);if(!t.some(e=>this.localParser.supportedTypes[e]===s))throw new Error(`不支持的文件类型: ${e.type}`)}async batchProcess(e,t={},s){const o=[],n=e.length;for(let r=0;r<e.length;r++){const l=e[r];try{s&&s({current:r+1,total:n,status:"parsing",fileName:l.name});const e=await this.parseDocument(l,t);s&&s({current:r+1,total:n,status:"segmenting",fileName:l.name});const a=await this.segmentDocument(e.content,t,e.analysis);o.push({...e,segments:a,status:"completed"})}catch(a){console.error(`处理文件 ${l.name} 失败:`,a),o.push({fileName:l.name,fileSize:l.size,status:"error",error:a.message,segments:[]})}}return s&&s({current:n,total:n,status:"completed"}),o}getProcessingStats(e){const t={total:e.length,completed:0,failed:0,totalSegments:0,totalWords:0,avgSegmentsPerDoc:0,processingTime:0};return e.forEach(e=>{"completed"===e.status?(t.completed++,t.totalSegments+=e.segments?e.segments.length:0,t.totalWords+=e.content?e.content.length:0,t.processingTime+=e.parseTime||0):t.failed++}),t.avgSegmentsPerDoc=t.completed>0?Math.round(t.totalSegments/t.completed):0,t}}var er=Ya;class tr{constructor(){this.config=this.loadConfig()}getDefaultConfig(){return{bailian:{accessKeyId:"LTAI5tB38aLEpHToKESq9W3T",accessKeySecret:"******************************",workspaceId:"llm-xz4u83kacijf8jaj",apiKey:"sk-0baf684faf044823a593986c15617b12",enabled:!0,timeout:3e4,retryCount:2},processing:{useAI:!0,fallbackToLocal:!0,maxFileSize:10485760,batchSize:5,concurrency:2},segmentation:{defaultStrategy:"auto",maxLength:1e3,overlap:100,minSegmentLength:50},ui:{showAIOptions:!0,showProcessingDetails:!0,autoSave:!0,theme:"light"}}}loadConfig(){try{const e=localStorage.getItem("ai_config");if(e){const t=JSON.parse(e);return this.mergeConfig(this.getDefaultConfig(),t)}}catch(e){console.warn("加载AI配置失败:",e)}return this.getDefaultConfig()}saveConfig(){try{return localStorage.setItem("ai_config",JSON.stringify(this.config)),!0}catch(e){return console.error("保存AI配置失败:",e),!1}}mergeConfig(e,t){const s={...e};return Object.keys(t).forEach(o=>{"object"!==typeof t[o]||Array.isArray(t[o])?s[o]=t[o]:s[o]={...e[o],...t[o]}}),s}get(e){const t=e.split(".");let s=this.config;for(const o of t){if(!s||"object"!==typeof s)return;s=s[o]}return s}set(e,t){const s=e.split(".");let o=this.config;for(let n=0;n<s.length-1;n++){const e=s[n];o[e]&&"object"===typeof o[e]||(o[e]={}),o=o[e]}o[s[s.length-1]]=t,this.saveConfig()}getBailianConfig(){return this.get("bailian")}setBailianConfig(e){this.set("bailian",{...this.get("bailian"),...e})}isBailianConfigured(){const e=this.getBailianConfig();return!!(e.apiKey&&e.workspaceId&&e.enabled)}getProcessingConfig(){return this.get("processing")}setProcessingConfig(e){this.set("processing",{...this.get("processing"),...e})}getSegmentationConfig(){return this.get("segmentation")}setSegmentationConfig(e){this.set("segmentation",{...this.get("segmentation"),...e})}reset(){this.config=this.getDefaultConfig(),this.saveConfig()}validate(){const e=[],t=this.getBailianConfig();t.enabled&&(t.apiKey||e.push("百炼API Key未配置"),t.workspaceId||e.push("百炼工作空间ID未配置"),t.timeout<5e3&&e.push("百炼服务超时时间过短"));const s=this.getProcessingConfig();s.maxFileSize<1024&&e.push("最大文件大小设置过小"),(s.batchSize<1||s.batchSize>20)&&e.push("批处理大小应在1-20之间");const o=this.getSegmentationConfig();return o.maxLength<100&&e.push("最大分段长度过小"),o.overlap>=o.maxLength&&e.push("重叠长度不能大于等于最大分段长度"),{valid:0===e.length,errors:e}}export(){return{config:this.config,timestamp:(new Date).toISOString(),version:"1.0"}}import(e){try{return!!e.config&&(this.config=this.mergeConfig(this.getDefaultConfig(),e.config),this.saveConfig(),!0)}catch(t){return console.error("导入配置失败:",t),!1}}getSummary(){const e=this.getBailianConfig(),t=this.getProcessingConfig();return{bailianEnabled:e.enabled,useAI:t.useAI,fallbackEnabled:t.fallbackToLocal,maxFileSize:`${Math.round(t.maxFileSize/1024/1024)}MB`,defaultStrategy:this.get("segmentation.defaultStrategy"),configured:this.isBailianConfigured()}}}const sr=new tr;var or=sr,nr={name:"KnowledgeBaseManager",props:{show:{type:Boolean,default:!1},knowledgeBase:{type:Object,default:()=>({})}},emits:["close","complete"],data(){return{currentStep:1,steps:["上传","创建设置","分段预览","数据处理"],uploadedFiles:[],settings:{parseText:!0,parseTable:!1,contentFilter:"",segmentStrategy:"auto",segmentLength:1e3,segmentOverlap:100,separator:"",extractKeywords:!0,generateSummary:!1,useAI:!0,fallbackToLocal:!0},parser:null,aiProcessing:!1,aiEnabled:!1,processingStats:null,selectedFileId:null,processingProgress:0}},mounted(){this.initializeParser(),this.loadAIConfig()},methods:{initializeParser(){try{this.parser=new er({bailian:or.getBailianConfig(),useAI:this.settings.useAI,fallbackToLocal:this.settings.fallbackToLocal}),this.aiEnabled=or.isBailianConfigured(),console.log("增强版解析器初始化成功"),this.testSegmentation()}catch(e){console.error("初始化解析器失败:",e),this.aiEnabled=!1}},async testSegmentation(){try{console.log("测试分段功能...");const e="这是第一段内容。这里有一些测试文字，用来验证分段功能是否正常工作。\n\n这是第二段内容。这里也有一些测试文字，用来验证分段功能的效果。\n\n这是第三段内容。最后一段用来确保分段算法能够正确处理多段文本。",t=await this.parser.segmentDocument(e,{strategy:"auto",maxLength:100,overlap:20,useAI:!1});console.log("测试分段结果:",t),console.log("测试分段数量:",t.length)}catch(e){console.error("测试分段失败:",e)}},loadAIConfig(){const e=or.getProcessingConfig();this.settings.useAI=e.useAI,this.settings.fallbackToLocal=e.fallbackToLocal;const t=or.getSegmentationConfig();this.settings.segmentStrategy=t.defaultStrategy,this.settings.segmentLength=t.maxLength,this.settings.segmentOverlap=t.overlap},getStepTitle(){const e={1:"新增知识库 - 上传文件",2:"新增知识库 - 创建设置",3:"新增知识库 - 分段预览",4:"新增知识库 - 数据处理"};return e[this.currentStep]||"新增知识库"},handleBack(){this.currentStep>1?this.previousStep():this.$emit("close")},triggerFileUpload(){this.$refs.fileInput.click()},handleFileSelect(e){const t=Array.from(e.target.files);this.addFiles(t)},handleFileDrop(e){const t=Array.from(e.dataTransfer.files);this.addFiles(t)},async addFiles(e){for(const o of e){const e={id:Date.now()+Math.random(),name:o.name,size:o.size,file:o,progress:0,status:"uploading",content:"",segments:[],analysis:null,error:null,aiEnhanced:!1};this.uploadedFiles.push(e);try{await this.simulateUploadProgress(e),e.status="parsing";const s=await this.parser.parseDocument(o,{useAI:this.settings.useAI&&this.aiEnabled});Object.assign(e,s),console.log("开始分段处理，文件:",e.name,"内容长度:",e.content.length),e.status="segmenting";try{const t={strategy:this.settings.segmentStrategy,maxLength:parseInt(this.settings.segmentLength),overlap:parseInt(this.settings.segmentOverlap),useAI:this.settings.useAI&&this.aiEnabled};console.log("分段设置:",t),e.segments=await this.parser.segmentDocument(e.content,t,e.analysis),console.log("分段完成，分段数量:",e.segments.length),console.log("分段结果:",e.segments),console.log("文件对象完整信息:",JSON.stringify(e,null,2)),e.status="completed",e.progress=100,e.aiEnhanced=this.settings.useAI&&this.aiEnabled,this.$forceUpdate(),console.log("强制更新Vue组件")}catch(t){console.error("分段处理失败:",t),e.segments=[],e.status="completed",e.progress=100,e.error=`分段失败: ${t.message}`}}catch(s){console.error("文件处理失败:",s),e.status="error",e.error=s.message}}},async simulateUploadProgress(e){return new Promise(t=>{const s=setInterval(()=>{e.progress+=20*Math.random(),e.progress>=90&&(e.progress=90,clearInterval(s),t())},200)})},removeFile(e){this.uploadedFiles=this.uploadedFiles.filter(t=>t.id!==e)},formatFileSize(e){if(0===e)return"0 Bytes";const t=1024,s=["Bytes","KB","MB","GB"],o=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,o)).toFixed(2))+" "+s[o]},getCurrentFileSegments(){if(console.log("getCurrentFileSegments 被调用"),console.log("selectedFileId:",this.selectedFileId),console.log("uploadedFiles:",this.uploadedFiles),!this.selectedFileId){const e=this.uploadedFiles[0],t=e?.segments||[];return console.log("获取第一个文件的分段:"),console.log("- 文件名:",e?.name),console.log("- 文件ID:",e?.id),console.log("- 文件状态:",e?.status),console.log("- 分段数量:",t.length),console.log("- 分段内容:",t),t}console.log("查找文件，selectedFileId:",this.selectedFileId),console.log("所有文件ID:",this.uploadedFiles.map(e=>e.id));const e=this.uploadedFiles.find(e=>e.id===this.selectedFileId);if(console.log("找到的文件:",e),!e){console.log("未找到匹配的文件，使用第一个文件");const e=this.uploadedFiles[0],t=e?.segments||[];return console.log("- 第一个文件名:",e?.name),console.log("- 第一个文件ID:",e?.id),console.log("- 分段数量:",t.length),t}const t=e?.segments||[];return console.log("获取选中文件的分段:"),console.log("- 文件名:",e?.name),console.log("- 文件ID:",e?.id),console.log("- 文件状态:",e?.status),console.log("- 分段数量:",t.length),console.log("- 分段内容:",t),t},canProceedToNext(){switch(this.currentStep){case 1:return this.uploadedFiles.length>0;case 2:return!0;case 3:return!0;default:return!1}},async nextStep(){this.canProceedToNext()&&this.currentStep<4&&(this.currentStep++,3===this.currentStep&&(await this.regenerateSegments(),this.uploadedFiles.length>0&&(this.selectedFileId=this.uploadedFiles[0].id)),4===this.currentStep&&this.startProcessing())},async regenerateSegments(){this.aiProcessing=!0;try{for(const t of this.uploadedFiles)if(t.content&&"completed"===t.status)try{console.log("重新分段文件:",t.name,"内容长度:",t.content.length),t.status="segmenting";const e={strategy:this.settings.segmentStrategy,maxLength:parseInt(this.settings.segmentLength)||1e3,overlap:parseInt(this.settings.segmentOverlap)||100,separator:this.settings.separator||"\n\n",useAI:this.settings.useAI&&this.aiEnabled};console.log("重新分段设置:",e),t.segments=await this.parser.segmentDocument(t.content,e,t.analysis),console.log("重新分段完成，分段数量:",t.segments.length),t.status="completed",t.aiEnhanced=this.settings.useAI&&this.aiEnabled}catch(e){console.error("重新分段失败:",e),t.status="error",t.error=e.message,t.segments=[]}}finally{this.aiProcessing=!1}},previousStep(){this.currentStep>1&&this.currentStep--},startProcessing(){this.processingProgress=0;const e=setInterval(()=>{this.processingProgress+=15*Math.random(),this.processingProgress>=100&&(this.processingProgress=100,clearInterval(e))},500)},async editSegment(e){const t=prompt("编辑分段内容:",e.content);if(null!==t&&t.trim()){const n=e.content;e.content=t.trim(),e.length=e.content.length;try{if(this.settings.useAI&&this.aiEnabled)try{const[t,s]=await Promise.all([this.parser.bailianService.extractKeywords(e.content).catch(t=>(console.log("AI关键词提取失败，使用本地方法:",t.message),this.parser.localParser.extractKeywords(e.content))),this.parser.bailianService.generateSummary(e.content).catch(t=>(console.log("AI摘要生成失败，使用本地方法:",t.message),this.parser.localParser.generateSummary(e.content)))]);e.keywords=t,e.summary=s,e.aiEnhanced=!0}catch(s){console.warn("AI处理失败，回退到本地方法:",s.message),e.keywords=this.parser.localParser.extractKeywords(e.content),e.summary=this.parser.localParser.generateSummary(e.content),e.aiEnhanced=!1}else e.keywords=this.parser.localParser.extractKeywords(e.content),e.summary=this.parser.localParser.generateSummary(e.content),e.aiEnhanced=!1}catch(o){console.error("更新分段信息失败:",o),e.content=n,e.length=n.length}}},deleteSegment(e){if(confirm("确定要删除这个分段吗？")){const t=this.uploadedFiles.find(e=>e.id===this.selectedFileId);t&&(t.segments=t.segments.filter(t=>t.id!==e))}},completeCreation(){const e={...this.knowledgeBase,files:this.uploadedFiles,settings:this.settings,status:"completed",createdAt:new Date};this.$emit("complete",e),this.$emit("close")},formatDate(e){return new Date(e).toLocaleString("zh-CN")},getStrategyDescription(e){const t={auto:"根据文档结构自动选择最佳分段方式",semantic:"基于语义边界进行智能分段",fixed:"按固定字符长度分段",manual:"使用自定义分隔符手动分段"};return t[e]||"未知策略"},getProcessingStats(){if(!this.parser)return null;const e=this.uploadedFiles.filter(e=>"completed"===e.status),t=e.reduce((e,t)=>e+(t.segments?.length||0),0),s=e.filter(e=>e.aiEnhanced).length;return{totalFiles:this.uploadedFiles.length,completedFiles:e.length,totalSegments:t,aiEnhancedFiles:s,avgSegmentsPerFile:e.length>0?Math.round(t/e.length):0}}},watch:{show(e){e&&(this.currentStep=1,this.uploadedFiles=[],this.selectedFileId=null,this.processingProgress=0)}}};const ar=(0,_.A)(nr,[["render",Qa]]);var rr=ar;const lr={key:0,class:"fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-70"},ir={class:"bg-white rounded-2xl shadow-2xl w-[95vw] h-[90vh] overflow-hidden transform transition-all duration-300 scale-100 flex flex-col"},cr={class:"flex items-center justify-between p-6 border-b border-gray-200"},dr={class:"flex items-center"},gr={class:"text-xl font-bold text-gray-900"},ur={class:"flex items-center space-x-4"},hr={class:"flex flex-1 overflow-hidden"},mr={class:"w-1/3 border-r border-gray-200 flex flex-col"},pr={class:"p-4 border-b border-gray-200"},br={class:"relative mb-3"},fr={class:"flex space-x-2"},xr=["value"],yr={class:"flex-1 overflow-y-auto"},kr={class:"p-2 space-y-2"},vr=["onClick"],wr={class:"flex items-center justify-between mb-2"},Lr={class:"text-sm font-medium text-gray-700"},Cr={class:"flex space-x-1"},Sr=["onClick"],Tr=["onClick"],Er={class:"text-xs text-gray-600 mb-1"},Mr={class:"text-sm text-gray-900 line-clamp-3"},Ar={class:"flex items-center justify-between mt-2"},Br={class:"text-xs text-gray-500"},Kr={class:"flex space-x-1"},Ir={class:"flex-1 flex flex-col"},_r={key:0,class:"flex-1 flex flex-col"},jr={class:"p-4 border-b border-gray-200 bg-gray-50"},Dr={class:"flex items-center justify-between"},Fr={class:"text-lg font-semibold text-gray-900"},$r={class:"text-sm text-gray-600"},Pr={class:"flex space-x-2"},Rr={class:"flex-1 p-4 overflow-y-auto"},Xr={class:"space-y-6"},Vr={key:1,class:"p-3 border border-gray-200 rounded-lg bg-gray-50 min-h-64"},zr={class:"text-gray-900 whitespace-pre-wrap"},Or={key:0,class:"space-y-2"},Nr={class:"flex flex-wrap gap-2 mb-2"},Ur=["onClick"],Wr={class:"flex"},Qr={key:1,class:"flex flex-wrap gap-2"},Jr={key:1,class:"p-3 border border-gray-200 rounded-lg bg-gray-50"},Hr={class:"text-gray-900"},Gr={class:"grid grid-cols-2 gap-4"},qr={class:"p-3 border border-gray-200 rounded-lg bg-gray-50"},Zr={class:"text-gray-900"},Yr={class:"p-3 border border-gray-200 rounded-lg bg-gray-50"},el={class:"text-gray-900"},tl={key:1,class:"flex-1 flex items-center justify-center"},sl={key:0,class:"fixed inset-0 bg-black/50 flex items-center justify-center z-80"},ol={class:"bg-white rounded-xl shadow-2xl w-[600px] max-h-[80vh] overflow-hidden"},nl={class:"flex items-center justify-between p-6 border-b border-gray-200"},al={class:"p-6 space-y-4"},rl=["value"],ll={class:"flex justify-end space-x-4 p-6 border-t border-gray-200"};function il(e,t,s,r,l,i){return s.show?((0,n.uX)(),(0,n.CE)("div",lr,[(0,n.Lk)("div",ir,[(0,n.Lk)("div",cr,[(0,n.Lk)("div",dr,[(0,n.Lk)("button",{onClick:t[0]||(t[0]=t=>e.$emit("close")),class:"mr-3 w-8 h-8 bg-gray-100 rounded-lg hover:bg-gray-200 flex items-center justify-center text-gray-600 transition-colors"}," ← "),t[20]||(t[20]=(0,n.Lk)("div",{class:"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center text-white text-sm mr-3"}," 📚 ",-1)),(0,n.Lk)("div",null,[(0,n.Lk)("h3",gr,(0,a.v_)(s.knowledgeBase.name),1),t[19]||(t[19]=(0,n.Lk)("p",{class:"text-sm text-gray-600"},"知识库内容管理",-1))])]),(0,n.Lk)("div",ur,[(0,n.Lk)("button",{onClick:t[1]||(t[1]=(...e)=>i.saveChanges&&i.saveChanges(...e)),class:"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"}," 保存更改 "),(0,n.Lk)("button",{onClick:t[2]||(t[2]=t=>e.$emit("close")),class:"w-8 h-8 rounded-lg hover:bg-gray-100 flex items-center justify-center text-gray-500 hover:text-gray-700 transition-colors"}," ✕ ")])]),(0,n.Lk)("div",hr,[(0,n.Lk)("div",mr,[(0,n.Lk)("div",pr,[(0,n.Lk)("div",br,[(0,n.bo)((0,n.Lk)("input",{"onUpdate:modelValue":t[3]||(t[3]=e=>l.searchQuery=e),placeholder:"搜索内容...",class:"w-full pl-8 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"},null,512),[[o.Jo,l.searchQuery]]),t[21]||(t[21]=(0,n.Lk)("span",{class:"absolute left-2 top-2.5 text-gray-400"},"🔍",-1))]),(0,n.Lk)("div",fr,[(0,n.bo)((0,n.Lk)("select",{"onUpdate:modelValue":t[4]||(t[4]=e=>l.selectedFileFilter=e),class:"flex-1 px-3 py-2 border border-gray-200 rounded-lg text-sm"},[t[22]||(t[22]=(0,n.Lk)("option",{value:""},"全部文件",-1)),((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(l.files,e=>((0,n.uX)(),(0,n.CE)("option",{key:e.id,value:e.id},(0,a.v_)(e.name),9,xr))),128))],512),[[o.u1,l.selectedFileFilter]]),(0,n.Lk)("button",{onClick:t[5]||(t[5]=e=>l.showAddSegment=!0),class:"px-3 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors text-sm"}," + 添加 ")])]),(0,n.Lk)("div",yr,[(0,n.Lk)("div",kr,[((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(i.filteredSegments,e=>((0,n.uX)(),(0,n.CE)("div",{key:e.id,onClick:t=>i.selectSegment(e),class:(0,a.C4)(["p-3 border rounded-lg cursor-pointer transition-all duration-200",l.selectedSegment?.id===e.id?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"])},[(0,n.Lk)("div",wr,[(0,n.Lk)("span",Lr,"分段 "+(0,a.v_)(e.id),1),(0,n.Lk)("div",Cr,[(0,n.Lk)("button",{onClick:(0,o.D$)(t=>i.editSegment(e),["stop"]),class:"text-xs text-blue-600 hover:text-blue-800"}," 编辑 ",8,Sr),(0,n.Lk)("button",{onClick:(0,o.D$)(t=>i.deleteSegment(e.id),["stop"]),class:"text-xs text-red-600 hover:text-red-800"}," 删除 ",8,Tr)])]),(0,n.Lk)("p",Er,(0,a.v_)(e.fileName),1),(0,n.Lk)("p",Mr,(0,a.v_)(e.content.substring(0,100))+(0,a.v_)(e.content.length>100?"...":""),1),(0,n.Lk)("div",Ar,[(0,n.Lk)("span",Br,(0,a.v_)(e.content.length)+" 字符",1),(0,n.Lk)("div",Kr,[((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(e.keywords.slice(0,2),e=>((0,n.uX)(),(0,n.CE)("span",{key:e,class:"px-1 py-0.5 bg-blue-100 text-blue-700 text-xs rounded"},(0,a.v_)(e),1))),128))])])],10,vr))),128))])])]),(0,n.Lk)("div",Ir,[l.selectedSegment?((0,n.uX)(),(0,n.CE)("div",_r,[(0,n.Lk)("div",jr,[(0,n.Lk)("div",Dr,[(0,n.Lk)("div",null,[(0,n.Lk)("h4",Fr,"分段 "+(0,a.v_)(l.selectedSegment.id),1),(0,n.Lk)("p",$r,"来源文件: "+(0,a.v_)(l.selectedSegment.fileName),1)]),(0,n.Lk)("div",Pr,[(0,n.Lk)("button",{onClick:t[6]||(t[6]=(...e)=>i.toggleEditMode&&i.toggleEditMode(...e)),class:(0,a.C4)(["px-3 py-1 rounded text-sm transition-colors",l.isEditing?"bg-green-500 text-white hover:bg-green-600":"bg-blue-500 text-white hover:bg-blue-600"])},(0,a.v_)(l.isEditing?"保存":"编辑"),3),l.isEditing?((0,n.uX)(),(0,n.CE)("button",{key:0,onClick:t[7]||(t[7]=(...e)=>i.cancelEdit&&i.cancelEdit(...e)),class:"px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600 transition-colors"}," 取消 ")):(0,n.Q3)("",!0)])])]),(0,n.Lk)("div",Rr,[(0,n.Lk)("div",Xr,[(0,n.Lk)("div",null,[t[23]||(t[23]=(0,n.Lk)("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"内容",-1)),l.isEditing?(0,n.bo)(((0,n.uX)(),(0,n.CE)("textarea",{key:0,"onUpdate:modelValue":t[8]||(t[8]=e=>l.editingContent=e),class:"w-full h-64 p-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none",placeholder:"输入分段内容..."},null,512)),[[o.Jo,l.editingContent]]):((0,n.uX)(),(0,n.CE)("div",Vr,[(0,n.Lk)("p",zr,(0,a.v_)(l.selectedSegment.content),1)]))]),(0,n.Lk)("div",null,[t[24]||(t[24]=(0,n.Lk)("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"关键词",-1)),l.isEditing?((0,n.uX)(),(0,n.CE)("div",Or,[(0,n.Lk)("div",Nr,[((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(l.editingKeywords,(e,t)=>((0,n.uX)(),(0,n.CE)("span",{key:t,class:"inline-flex items-center px-2 py-1 bg-blue-100 text-blue-700 text-sm rounded"},[(0,n.eW)((0,a.v_)(e)+" ",1),(0,n.Lk)("button",{onClick:e=>i.removeKeyword(t),class:"ml-1 text-blue-500 hover:text-blue-700"}," × ",8,Ur)]))),128))]),(0,n.Lk)("div",Wr,[(0,n.bo)((0,n.Lk)("input",{"onUpdate:modelValue":t[9]||(t[9]=e=>l.newKeyword=e),onKeyup:t[10]||(t[10]=(0,o.jR)((...e)=>i.addKeyword&&i.addKeyword(...e),["enter"])),placeholder:"输入关键词后按回车添加",class:"flex-1 px-3 py-2 border border-gray-200 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500"},null,544),[[o.Jo,l.newKeyword]]),(0,n.Lk)("button",{onClick:t[11]||(t[11]=(...e)=>i.addKeyword&&i.addKeyword(...e)),class:"px-4 py-2 bg-blue-500 text-white rounded-r-lg hover:bg-blue-600 transition-colors"}," 添加 ")])])):((0,n.uX)(),(0,n.CE)("div",Qr,[((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(l.selectedSegment.keywords,e=>((0,n.uX)(),(0,n.CE)("span",{key:e,class:"px-2 py-1 bg-blue-100 text-blue-700 text-sm rounded"},(0,a.v_)(e),1))),128))]))]),(0,n.Lk)("div",null,[t[25]||(t[25]=(0,n.Lk)("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"摘要",-1)),l.isEditing?(0,n.bo)(((0,n.uX)(),(0,n.CE)("textarea",{key:0,"onUpdate:modelValue":t[12]||(t[12]=e=>l.editingSummary=e),class:"w-full h-24 p-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none",placeholder:"输入分段摘要..."},null,512)),[[o.Jo,l.editingSummary]]):((0,n.uX)(),(0,n.CE)("div",Jr,[(0,n.Lk)("p",Hr,(0,a.v_)(l.selectedSegment.summary||"暂无摘要"),1)]))]),(0,n.Lk)("div",Gr,[(0,n.Lk)("div",null,[t[26]||(t[26]=(0,n.Lk)("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"字符数",-1)),(0,n.Lk)("div",qr,[(0,n.Lk)("p",Zr,(0,a.v_)(l.selectedSegment.content.length),1)])]),(0,n.Lk)("div",null,[t[27]||(t[27]=(0,n.Lk)("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"最后更新",-1)),(0,n.Lk)("div",Yr,[(0,n.Lk)("p",el,(0,a.v_)(i.formatDate(l.selectedSegment.updatedAt)),1)])])])])])])):((0,n.uX)(),(0,n.CE)("div",tl,t[28]||(t[28]=[(0,n.Fv)('<div class="text-center" data-v-0f62befc><div class="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center" data-v-0f62befc><span class="text-4xl text-gray-400" data-v-0f62befc>📄</span></div><h4 class="text-lg font-semibold text-gray-900 mb-2" data-v-0f62befc>选择一个分段进行编辑</h4><p class="text-gray-600" data-v-0f62befc>从左侧列表中选择要编辑的内容分段</p></div>',1)])))])]),l.showAddSegment?((0,n.uX)(),(0,n.CE)("div",sl,[(0,n.Lk)("div",ol,[(0,n.Lk)("div",nl,[t[29]||(t[29]=(0,n.Lk)("h3",{class:"text-lg font-bold text-gray-900"},"添加新分段",-1)),(0,n.Lk)("button",{onClick:t[13]||(t[13]=e=>l.showAddSegment=!1),class:"text-gray-500 hover:text-gray-700"},"✕")]),(0,n.Lk)("div",al,[(0,n.Lk)("div",null,[t[31]||(t[31]=(0,n.Lk)("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"所属文件",-1)),(0,n.bo)((0,n.Lk)("select",{"onUpdate:modelValue":t[14]||(t[14]=e=>l.newSegment.fileId=e),class:"w-full px-3 py-2 border border-gray-200 rounded-lg"},[t[30]||(t[30]=(0,n.Lk)("option",{value:""},"选择文件",-1)),((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(l.files,e=>((0,n.uX)(),(0,n.CE)("option",{key:e.id,value:e.id},(0,a.v_)(e.name),9,rl))),128))],512),[[o.u1,l.newSegment.fileId]])]),(0,n.Lk)("div",null,[t[32]||(t[32]=(0,n.Lk)("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"内容",-1)),(0,n.bo)((0,n.Lk)("textarea",{"onUpdate:modelValue":t[15]||(t[15]=e=>l.newSegment.content=e),class:"w-full h-32 p-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none",placeholder:"输入分段内容..."},null,512),[[o.Jo,l.newSegment.content]])]),(0,n.Lk)("div",null,[t[33]||(t[33]=(0,n.Lk)("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"关键词 (用逗号分隔)",-1)),(0,n.bo)((0,n.Lk)("input",{"onUpdate:modelValue":t[16]||(t[16]=e=>l.newSegment.keywordsText=e),class:"w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"关键词1, 关键词2, 关键词3"},null,512),[[o.Jo,l.newSegment.keywordsText]])])]),(0,n.Lk)("div",ll,[(0,n.Lk)("button",{onClick:t[17]||(t[17]=e=>l.showAddSegment=!1),class:"px-4 py-2 text-gray-600 hover:text-gray-800"}," 取消 "),(0,n.Lk)("button",{onClick:t[18]||(t[18]=(...e)=>i.addNewSegment&&i.addNewSegment(...e)),class:"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"}," 添加 ")])])])):(0,n.Q3)("",!0)])])):(0,n.Q3)("",!0)}var cl={name:"KnowledgeBaseEditor",props:{show:{type:Boolean,default:!1},knowledgeBase:{type:Object,default:()=>({})}},emits:["close","save"],data(){return{searchQuery:"",selectedFileFilter:"",selectedSegment:null,isEditing:!1,editingContent:"",editingKeywords:[],editingSummary:"",newKeyword:"",showAddSegment:!1,newSegment:{fileId:"",content:"",keywordsText:""},files:[{id:1,name:"go.docx"},{id:2,name:"product_manual.pdf"},{id:3,name:"api_docs.md"}],segments:[{id:1,fileId:1,fileName:"go.docx",content:"Go 知识库\n\nGo 简介入门\n\n1.1 Go 介绍\nGo 是 Google 开发的一种静态强类型、编译型语言。Go 语言语法与 C 相近，但功能上有：内存安全，GC（垃圾回收），结构形态及 CSP-style 并发计算。",keywords:["Go语言","Google","编程语言","静态类型"],summary:"Go语言的基本介绍，包括其特性和优势",updatedAt:new Date("2024-01-15")},{id:2,fileId:1,fileName:"go.docx",content:"1.2 开发环境搭建\n下载安装\n从 Go 官方网站下载对应操作系统的 Go 安装包。Windows、macOS、Linux 都有对应的安装包。安装完成后，可以通过命令行验证安装是否成功。",keywords:["环境搭建","安装","下载"],summary:"Go语言开发环境的安装和配置步骤",updatedAt:new Date("2024-01-15")},{id:3,fileId:2,fileName:"product_manual.pdf",content:"产品功能介绍\n\n本产品是一个智能知识库管理系统，支持多种文档格式的导入、解析和检索。主要功能包括：\n1. 文档上传和解析\n2. 智能分段\n3. 关键词提取\n4. 语义检索",keywords:["产品介绍","知识库","文档管理"],summary:"产品的主要功能和特性介绍",updatedAt:new Date("2024-01-10")}]}},computed:{filteredSegments(){let e=this.segments;if(this.selectedFileFilter&&(e=e.filter(e=>e.fileId==this.selectedFileFilter)),this.searchQuery){const t=this.searchQuery.toLowerCase();e=e.filter(e=>e.content.toLowerCase().includes(t)||e.keywords.some(e=>e.toLowerCase().includes(t)))}return e}},methods:{selectSegment(e){this.selectedSegment=e,this.isEditing=!1},editSegment(e){this.selectSegment(e),this.startEdit()},startEdit(){this.isEditing=!0,this.editingContent=this.selectedSegment.content,this.editingKeywords=[...this.selectedSegment.keywords],this.editingSummary=this.selectedSegment.summary||""},toggleEditMode(){this.isEditing?this.saveSegmentChanges():this.startEdit()},cancelEdit(){this.isEditing=!1,this.editingContent="",this.editingKeywords=[],this.editingSummary=""},saveSegmentChanges(){this.selectedSegment&&(this.selectedSegment.content=this.editingContent,this.selectedSegment.keywords=[...this.editingKeywords],this.selectedSegment.summary=this.editingSummary,this.selectedSegment.updatedAt=new Date,this.isEditing=!1)},addKeyword(){this.newKeyword.trim()&&!this.editingKeywords.includes(this.newKeyword.trim())&&(this.editingKeywords.push(this.newKeyword.trim()),this.newKeyword="")},removeKeyword(e){this.editingKeywords.splice(e,1)},deleteSegment(e){confirm("确定要删除这个分段吗？")&&(this.segments=this.segments.filter(t=>t.id!==e),this.selectedSegment?.id===e&&(this.selectedSegment=null))},addNewSegment(){if(!this.newSegment.fileId||!this.newSegment.content.trim())return void alert("请填写完整信息");const e=this.files.find(e=>e.id==this.newSegment.fileId),t=this.newSegment.keywordsText.split(",").map(e=>e.trim()).filter(e=>e),s={id:Math.max(...this.segments.map(e=>e.id))+1,fileId:this.newSegment.fileId,fileName:e.name,content:this.newSegment.content,keywords:t,summary:"",updatedAt:new Date};this.segments.push(s),this.showAddSegment=!1,this.newSegment={fileId:"",content:"",keywordsText:""}},saveChanges(){const e={knowledgeBase:this.knowledgeBase,segments:this.segments};this.$emit("save",e),alert("保存成功！")},formatDate(e){return new Date(e).toLocaleDateString("zh-CN")}}};const dl=(0,_.A)(cl,[["render",il],["__scopeId","data-v-0f62befc"]]);var gl=dl,ul={name:"RobotSettingsModal",components:{KnowledgeBaseManager:rr,KnowledgeBaseEditor:gl},props:{show:Boolean,robot:Object},emits:["close","save"],data(){return{activeTab:"personality",localPersonality:"",testInput:"",showOptimizeModal:!1,optimizeMode:"auto",optimizeInput:"",optimizedResult:"",isOptimizing:!1,showKnowledgeBaseSelection:!1,showCreateKnowledgeBase:!1,showKnowledgeBaseManager:!1,showKnowledgeBaseEditor:!1,currentKnowledgeBaseType:"",searchQuery:"",createMethod:"local",newKbType:"text",newKbName:"",newKbDescription:"",selectedKnowledgeBaseForEdit:null,activeKnowledgeBaseMenu:null,showKnowledgeBaseMarket:!1,showDeleteConfirm:!1,knowledgeBaseToDelete:null,saveSuccessMessage:!1,knowledgeBases:[],marketSearchQuery:"",marketSelectedType:"",testMessages:[{content:"你好，我想了解一下你的功能",isUser:!0},{content:"您好！我是您的AI助手，我可以帮助您解答问题、提供信息和进行对话。请告诉我您需要什么帮助？",isUser:!1}],tabs:[{id:"personality",name:"人设与回复逻辑",icon:"👤"},{id:"orchestration",name:"编排",icon:"📚"},{id:"preview",name:"预览与调试",icon:"🔍"}],replyLogic:{systemRole:"你是一个专业的对话大模型工程师，专注于帮助用户解决技术相关的问题。",dialogCommand:"请以简洁、专业的方式回答用户的问题，尽量控制在合理的长度内",responseMode:"simple",temperature:.6,maxTokens:50,language:"zh-cn",speaker:"601002",speechSpeed:1},knowledgeConfig:{callMethod:"auto",searchStrategy:"mixed",maxRecall:5,minScore:.5,queryRewrite:!0,resultRerank:!0}}},computed:{filteredKnowledgeBases(){let e=this.knowledgeBases;if(this.currentKnowledgeBaseType&&(e=e.filter(e=>e.type===this.currentKnowledgeBaseType)),this.searchQuery.trim()){const t=this.searchQuery.toLowerCase();e=e.filter(e=>e.name.toLowerCase().includes(t)||e.description.toLowerCase().includes(t))}return e},filteredMarketKnowledgeBases(){let e=this.knowledgeBases;if(this.marketSelectedType&&(e=e.filter(e=>e.type===this.marketSelectedType)),this.marketSearchQuery.trim()){const t=this.marketSearchQuery.toLowerCase();e=e.filter(e=>e.name.toLowerCase().includes(t)||e.description.toLowerCase().includes(t))}return e}},watch:{robot:{handler(e){e&&this.loadRobotSettings()},immediate:!0},show(e){e&&this.robot&&this.loadRobotSettings()}},methods:{loadRobotSettings(){if(this.robot){if(this.localPersonality=this.robot.personnel_design||"",this.robot.reply_logic)try{const e=JSON.parse(this.robot.reply_logic);this.replyLogic={...this.replyLogic,...e}}catch(e){console.warn("解析回复逻辑配置失败:",e)}if(this.robot.knowledge_config)try{const e=JSON.parse(this.robot.knowledge_config);this.knowledgeConfig={...this.knowledgeConfig,...e}}catch(e){console.warn("解析知识库配置失败:",e)}}},async saveSettings(){if(this.robot)try{const e=await fetch("http://localhost:8080/api/robot/update",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({id:this.robot.id,name:this.robot.name,description:this.robot.description,personnel_design:this.localPersonality,reply_logic:JSON.stringify(this.replyLogic),knowledge_config:JSON.stringify(this.knowledgeConfig)})}),t=await e.json();if(200!==t.code)throw new Error(t.msg||"保存失败");this.$emit("save",{...this.robot,personnel_design:this.localPersonality,reply_logic:JSON.stringify(this.replyLogic),knowledge_config:JSON.stringify(this.knowledgeConfig)}),this.showSuccessMessage(),console.log("机器人设置保存成功:",this.robot.name)}catch(e){console.error("保存设置失败:",e),alert("保存失败: "+e.message)}},showSuccessMessage(){this.saveSuccessMessage=!0,setTimeout(()=>{this.saveSuccessMessage=!1},3e3)},async loadKnowledgeBases(){try{const e=await fetch("http://localhost:8080/api/knowledge-base/list",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({})}),t=await e.json();200===t.code?(this.knowledgeBases=t.data.map(e=>({...e,type:this.mapKnowledgeBaseType(e.type),icon:this.getKnowledgeBaseIcon(e.type),updateTime:new Date(e.updatedAt)})),console.log("知识库列表加载成功:",this.knowledgeBases)):console.error("获取知识库列表失败:",t.msg)}catch(e){console.error("获取知识库列表失败:",e),this.knowledgeBases=[]}},getKnowledgeBaseIcon(e){const t={text:"📄",table:"📊",image:"🖼️",document:"📄"};return t[e]||"📄"},mapKnowledgeBaseType(e){const t={document:"text",text:"text",table:"table",image:"image"};return t[e]||"text"},async sendTestMessage(){if(!this.testInput.trim())return;const e=this.testInput.trim();this.testMessages.push({content:e,isUser:!0}),this.testInput="";try{const t=await fetch("http://localhost:8080/api/llm/communication",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({user_id:this.robot.id?this.robot.id.toString():"test_user",message:{role:"user",content:e},streaming:!1})});if(!t.ok)throw new Error(`HTTP error! status: ${t.status}`);const s=await t.json();if(200!==s.code)throw new Error(s.msg||"服务器返回错误");this.testMessages.push({content:s.data,isUser:!1})}catch(t){console.error("发送测试消息失败:",t),this.testMessages.push({content:"抱歉，测试服务暂时不可用，请稍后重试。错误信息："+t.message,isUser:!1})}},generateTestResponse(){const e=["根据您的问题，我来为您详细解答...","这是一个很好的问题，让我来帮您分析一下...","基于当前的配置，我的回复是...","感谢您的提问，我会根据设定的人设来回答..."];return e[Math.floor(Math.random()*e.length)]+`（温度: ${this.replyLogic.temperature}, 模式: ${this.replyLogic.responseMode}）`},closeOptimizeModal(){this.showOptimizeModal=!1,this.optimizeInput="",this.optimizedResult="",this.optimizeMode="auto",this.isOptimizing=!1},async optimizePrompt(){if(this.optimizeInput.trim()){this.isOptimizing=!0;try{const e={mode:this.optimizeMode,input:this.optimizeInput.trim(),currentPersonality:this.localPersonality,replyLogic:this.replyLogic},t=await fetch("http://localhost:8080/api/prompt/optimize",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),s=await t.json();200===s.code?this.optimizedResult=s.data:this.optimizedResult=this.generateMockOptimizedResult()}catch(e){console.error("优化提示词失败:",e),this.optimizedResult=this.generateMockOptimizedResult()}finally{this.isOptimizing=!1}}},generateMockOptimizedResult(){const e={professional:"你是一位经验丰富的专业顾问，具备深厚的行业知识和丰富的实践经验。你善于分析问题的本质，能够提供准确、实用的解决方案。在与用户交流时，你保持专业而友善的态度，用清晰简洁的语言解释复杂概念，确保用户能够理解并应用你的建议。",friendly:"你是一位温暖友善的AI助手，总是以积极乐观的态度帮助用户。你善于倾听，能够理解用户的需求和情感，并给予贴心的回应。你的语言风格亲切自然，让用户感到舒适和被理解。你会耐心地解答问题，并在适当的时候给予鼓励和支持。",creative:"你是一位富有创意和想象力的AI助手，擅长从多个角度思考问题，提供新颖独特的解决方案。你善于运用比喻、故事和生动的例子来解释概念，让交流变得有趣而富有启发性。你鼓励用户跳出常规思维，探索更多可能性。",technical:"你是一位技术专家，精通各种技术领域的知识。你能够深入浅出地解释复杂的技术概念，提供准确的技术指导和最佳实践建议。你关注技术的实用性和可操作性，能够根据用户的技术水平调整解释的深度和方式。"},t=this.optimizeInput.toLowerCase();let s=e.professional;return t.includes("友善")||t.includes("温和")||t.includes("亲切")?s=e.friendly:t.includes("创意")||t.includes("创新")||t.includes("想象")?s=e.creative:(t.includes("技术")||t.includes("专业")||t.includes("技能"))&&(s=e.technical),`${s}\n\n根据您的要求"${this.optimizeInput}"，我已经为您优化了人设配置，使其更加符合您的期望。这个配置将帮助AI助手更好地理解其角色定位，并以相应的方式与用户互动。`},applyOptimizedResult(){this.optimizedResult&&(this.localPersonality=this.optimizedResult,this.closeOptimizeModal())},async openKnowledgeBaseSelection(e){this.currentKnowledgeBaseType=e,this.showKnowledgeBaseSelection=!0,this.showCreateKnowledgeBase=!1,this.searchQuery="",await this.loadKnowledgeBases()},closeKnowledgeBaseSelection(){this.showKnowledgeBaseSelection=!1,this.currentKnowledgeBaseType="",this.searchQuery=""},openCreateKnowledgeBase(){this.showCreateKnowledgeBase=!0,this.showKnowledgeBaseSelection=!1,this.currentKnowledgeBaseType=""},closeCreateKnowledgeBase(){this.showCreateKnowledgeBase=!1,this.currentKnowledgeBaseType&&(this.showKnowledgeBaseSelection=!0)},selectKnowledgeBase(e){console.log("选择知识库:",e),alert(`已选择知识库: ${e.name}`),this.closeKnowledgeBaseSelection()},createKnowledgeBase(){if(!this.newKbName.trim())return;const e={id:Date.now(),name:this.newKbName,description:this.newKbDescription,type:this.newKbType,method:this.createMethod,itemCount:0,updateTime:new Date,icon:"text"===this.newKbType?"📄":"table"===this.newKbType?"📊":"🖼️"};this.selectedKnowledgeBaseForEdit=e,this.showKnowledgeBaseManager=!0,this.showCreateKnowledgeBase=!1},editKnowledgeBase(e){this.selectedKnowledgeBaseForEdit=e,this.showKnowledgeBaseEditor=!0},onKnowledgeBaseManagerComplete(e){this.knowledgeBases.push(e),console.log("创建知识库完成:",e),this.resetKnowledgeBaseForm(),this.showKnowledgeBaseManager=!1,this.closeKnowledgeBaseSelection()},onKnowledgeBaseEditorSave(e){const t=this.knowledgeBases.findIndex(t=>t.id===e.knowledgeBase.id);-1!==t&&(this.knowledgeBases[t]={...this.knowledgeBases[t],...e.knowledgeBase}),console.log("知识库编辑保存:",e),this.showKnowledgeBaseEditor=!1},resetKnowledgeBaseForm(){this.newKbName="",this.newKbDescription="",this.newKbType="text",this.createMethod="local"},formatDate(e){const t=new Date,s=t-e,o=Math.floor(s/864e5);return 0===o?"今天":1===o?"昨天":o<7?`${o}天前`:o<30?`${Math.floor(o/7)}周前`:e.toLocaleDateString()},toggleKnowledgeBaseMenu(e){this.activeKnowledgeBaseMenu=this.activeKnowledgeBaseMenu===e?null:e},viewKnowledgeBaseDetails(e){console.log("查看知识库详情:",e),alert(`知识库详情：\n名称：${e.name}\n描述：${e.description}\n类型：${e.type}\n内容数量：${e.itemCount}\n更新时间：${this.formatDate(e.updateTime)}`),this.activeKnowledgeBaseMenu=null},async openKnowledgeBaseMarket(){this.showKnowledgeBaseMarket=!0,this.activeKnowledgeBaseMenu=null,await this.loadKnowledgeBases(),this.marketSearchQuery="",this.marketSelectedType=""},closeKnowledgeBaseMarket(){this.showKnowledgeBaseMarket=!1,this.marketSearchQuery="",this.marketSelectedType=""},getKnowledgeBaseTypeLabel(e){const t={text:"文本文档",table:"表格数据",image:"图片资源"};return t[e]||"未知类型"},selectKnowledgeBaseFromMarket(e){console.log("选择知识库:",e),this.currentKnowledgeBaseType?this.addKnowledgeBaseToConfig(e,this.currentKnowledgeBaseType):this.addKnowledgeBaseToConfig(e,e.type),this.closeKnowledgeBaseMarket(),alert(`已选择知识库：${e.name}`)},importKnowledgeBaseFromMarket(e){console.log("导入知识库:",e),alert(`导入功能开发中，知识库：${e.name}`)},addKnowledgeBaseToConfig(e,t){this.knowledgeConfig[t]||(this.knowledgeConfig[t]=[]);const s=this.knowledgeConfig[t].some(t=>t.id===e.id);s?alert("该知识库已经添加过了"):(this.knowledgeConfig[t].push({id:e.id,name:e.name,description:e.description,segmentCount:e.segmentCount||0}),console.log(`知识库 ${e.name} 已添加到 ${t} 配置中`))},exportConfiguration(){try{const e={robotName:this.robotName,personality:this.localPersonality,replyLogic:this.replyLogic,knowledgeConfig:this.knowledgeConfig,exportTime:(new Date).toISOString(),version:"1.0"},t=JSON.stringify(e,null,2),s=new Blob([t],{type:"application/json"}),o=URL.createObjectURL(s),n=document.createElement("a");n.href=o,n.download=`robot_config_${this.robotName||"unnamed"}_${(new Date).toISOString().split("T")[0]}.json`,document.body.appendChild(n),n.click(),document.body.removeChild(n),URL.revokeObjectURL(o),alert("配置已导出成功！")}catch(e){console.error("导出配置失败:",e),alert("导出失败: "+e.message)}},confirmDeleteKnowledgeBase(e){this.knowledgeBaseToDelete=e,this.showDeleteConfirm=!0,this.activeKnowledgeBaseMenu=null},deleteKnowledgeBase(){if(this.knowledgeBaseToDelete){const e=this.knowledgeBases.findIndex(e=>e.id===this.knowledgeBaseToDelete.id);-1!==e&&(this.knowledgeBases.splice(e,1),console.log("删除知识库:",this.knowledgeBaseToDelete.name))}this.showDeleteConfirm=!1,this.knowledgeBaseToDelete=null},cancelDeleteKnowledgeBase(){this.showDeleteConfirm=!1,this.knowledgeBaseToDelete=null},handleGlobalClick(e){e.target.closest(".relative")||(this.activeKnowledgeBaseMenu=null)}},async mounted(){document.addEventListener("click",this.handleGlobalClick),await this.loadKnowledgeBases()},beforeUnmount(){document.removeEventListener("click",this.handleGlobalClick)}};const hl=(0,_.A)(ul,[["render",Qo],["__scopeId","data-v-9e32fa80"]]);var ml=hl;const pl={class:"min-h-screen bg-gray-50 py-8"},bl={class:"max-w-6xl mx-auto px-4"},fl={class:"flex justify-center space-x-4 mb-8"},xl={class:"bg-white rounded-xl shadow-lg p-6"},yl={class:"flex items-center justify-between mb-6"},kl={class:"text-sm text-gray-600"},vl={key:0,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},wl={class:"flex items-center justify-between mb-4"},Ll={class:"flex items-center space-x-3"},Cl={class:"font-semibold text-gray-900"},Sl={class:"text-sm text-gray-600"},Tl={class:"flex space-x-2"},El=["onClick"],Ml=["onClick"],Al={class:"text-sm text-gray-600 mb-4"},Bl={class:"flex items-center justify-between text-sm text-gray-500"},Kl={class:"mt-4 pt-4 border-t border-gray-100"},Il={class:"flex space-x-2"},_l=["onClick"],jl=["onClick"],Dl={key:1,class:"text-center py-12"};function Fl(e,t,s,o,r,l){const i=(0,n.g2)("KnowledgeBaseManager"),c=(0,n.g2)("KnowledgeBaseEditor");return(0,n.uX)(),(0,n.CE)("div",pl,[(0,n.Lk)("div",bl,[t[9]||(t[9]=(0,n.Lk)("div",{class:"text-center mb-8"},[(0,n.Lk)("h1",{class:"text-3xl font-bold text-gray-900 mb-2"},"知识库管理系统"),(0,n.Lk)("p",{class:"text-gray-600"},"完整的知识库创建、管理和编辑流程演示")],-1)),(0,n.Lk)("div",fl,[(0,n.Lk)("button",{onClick:t[0]||(t[0]=(...e)=>l.showCreateKnowledgeBase&&l.showCreateKnowledgeBase(...e)),class:"px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-semibold"}," 创建新知识库 "),(0,n.Lk)("button",{onClick:t[1]||(t[1]=(...e)=>l.showExistingKnowledgeBases&&l.showExistingKnowledgeBases(...e)),class:"px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors font-semibold"}," 管理现有知识库 ")]),(0,n.Lk)("div",xl,[(0,n.Lk)("div",yl,[t[5]||(t[5]=(0,n.Lk)("h2",{class:"text-xl font-bold text-gray-900"},"我的知识库",-1)),(0,n.Lk)("span",kl,"共 "+(0,a.v_)(r.knowledgeBases.length)+" 个知识库",1)]),r.knowledgeBases.length>0?((0,n.uX)(),(0,n.CE)("div",vl,[((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(r.knowledgeBases,e=>((0,n.uX)(),(0,n.CE)("div",{key:e.id,class:"border border-gray-200 rounded-lg p-6 hover:border-blue-300 hover:shadow-md transition-all duration-200"},[(0,n.Lk)("div",wl,[(0,n.Lk)("div",Ll,[(0,n.Lk)("div",{class:(0,a.C4)(["w-12 h-12 rounded-lg flex items-center justify-center text-white text-xl","text"===e.type?"bg-blue-500":"table"===e.type?"bg-green-500":"bg-orange-500"])},(0,a.v_)("text"===e.type?"📄":"table"===e.type?"📊":"🖼️"),3),(0,n.Lk)("div",null,[(0,n.Lk)("h3",Cl,(0,a.v_)(e.name),1),(0,n.Lk)("p",Sl,(0,a.v_)(l.getTypeLabel(e.type)),1)])]),(0,n.Lk)("div",Tl,[(0,n.Lk)("button",{onClick:t=>l.editKnowledgeBase(e),class:"p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",title:"编辑内容"}," ✏️ ",8,El),(0,n.Lk)("button",{onClick:t=>l.deleteKnowledgeBase(e.id),class:"p-2 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors",title:"删除"}," 🗑️ ",8,Ml)])]),(0,n.Lk)("p",Al,(0,a.v_)(e.description),1),(0,n.Lk)("div",Bl,[(0,n.Lk)("span",null,(0,a.v_)(e.itemCount)+" 项内容",1),(0,n.Lk)("span",null,(0,a.v_)(l.formatDate(e.updateTime)),1)]),(0,n.Lk)("div",Kl,[(0,n.Lk)("div",Il,[(0,n.Lk)("button",{onClick:t=>l.viewKnowledgeBase(e),class:"flex-1 px-3 py-2 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 transition-colors"}," 查看详情 ",8,_l),(0,n.Lk)("button",{onClick:t=>l.editKnowledgeBase(e),class:"flex-1 px-3 py-2 bg-gray-500 text-white rounded text-sm hover:bg-gray-600 transition-colors"}," 编辑内容 ",8,jl)])])]))),128))])):((0,n.uX)(),(0,n.CE)("div",Dl,[t[6]||(t[6]=(0,n.Lk)("div",{class:"w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center"},[(0,n.Lk)("span",{class:"text-4xl text-gray-400"},"📚")],-1)),t[7]||(t[7]=(0,n.Lk)("h3",{class:"text-lg font-semibold text-gray-900 mb-2"},"还没有知识库",-1)),t[8]||(t[8]=(0,n.Lk)("p",{class:"text-gray-600 mb-6"},"创建您的第一个知识库，开始管理文档和数据",-1)),(0,n.Lk)("button",{onClick:t[2]||(t[2]=(...e)=>l.showCreateKnowledgeBase&&l.showCreateKnowledgeBase(...e)),class:"px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"}," 创建知识库 ")]))]),t[10]||(t[10]=(0,n.Fv)('<div class="mt-8 bg-white rounded-xl shadow-lg p-6" data-v-5dff746d><h2 class="text-xl font-bold text-gray-900 mb-4" data-v-5dff746d>知识库管理流程</h2><div class="grid grid-cols-1 md:grid-cols-4 gap-6" data-v-5dff746d><div class="text-center" data-v-5dff746d><div class="w-16 h-16 mx-auto mb-3 bg-blue-100 rounded-full flex items-center justify-center" data-v-5dff746d><span class="text-2xl" data-v-5dff746d>📁</span></div><h3 class="font-semibold text-gray-900 mb-2" data-v-5dff746d>1. 上传文件</h3><p class="text-sm text-gray-600" data-v-5dff746d>支持 PDF、DOC、DOCX、MD、TXT 等多种格式</p></div><div class="text-center" data-v-5dff746d><div class="w-16 h-16 mx-auto mb-3 bg-green-100 rounded-full flex items-center justify-center" data-v-5dff746d><span class="text-2xl" data-v-5dff746d>⚙️</span></div><h3 class="font-semibold text-gray-900 mb-2" data-v-5dff746d>2. 创建设置</h3><p class="text-sm text-gray-600" data-v-5dff746d>配置解析模式、分段策略和索引设置</p></div><div class="text-center" data-v-5dff746d><div class="w-16 h-16 mx-auto mb-3 bg-yellow-100 rounded-full flex items-center justify-center" data-v-5dff746d><span class="text-2xl" data-v-5dff746d>👁️</span></div><h3 class="font-semibold text-gray-900 mb-2" data-v-5dff746d>3. 分段预览</h3><p class="text-sm text-gray-600" data-v-5dff746d>预览文档分段结果，支持手动调整</p></div><div class="text-center" data-v-5dff746d><div class="w-16 h-16 mx-auto mb-3 bg-purple-100 rounded-full flex items-center justify-center" data-v-5dff746d><span class="text-2xl" data-v-5dff746d>🚀</span></div><h3 class="font-semibold text-gray-900 mb-2" data-v-5dff746d>4. 数据处理</h3><p class="text-sm text-gray-600" data-v-5dff746d>自动建立索引，完成知识库创建</p></div></div></div>',1))]),(0,n.bF)(i,{show:r.showManager,knowledgeBase:r.currentKnowledgeBase,onClose:t[3]||(t[3]=e=>r.showManager=!1),onComplete:l.onKnowledgeBaseComplete},null,8,["show","knowledgeBase","onComplete"]),(0,n.bF)(c,{show:r.showEditor,knowledgeBase:r.currentKnowledgeBase,onClose:t[4]||(t[4]=e=>r.showEditor=!1),onSave:l.onKnowledgeBaseSave},null,8,["show","knowledgeBase","onSave"])])}var $l={name:"KnowledgeBaseDemo",components:{KnowledgeBaseManager:rr,KnowledgeBaseEditor:gl},data(){return{showManager:!1,showEditor:!1,currentKnowledgeBase:null,knowledgeBases:[{id:1,name:"Go语言文档",description:"Go语言学习资料和API文档",type:"text",itemCount:156,updateTime:new Date("2024-01-15"),status:"completed"},{id:2,name:"产品需求表格",description:"产品功能需求和用户反馈数据",type:"table",itemCount:89,updateTime:new Date("2024-01-10"),status:"completed"},{id:3,name:"设计素材库",description:"UI设计稿和产品截图",type:"image",itemCount:234,updateTime:new Date("2024-01-08"),status:"completed"}]}},methods:{showCreateKnowledgeBase(){this.currentKnowledgeBase={name:"",description:"",type:"text"},this.showManager=!0},showExistingKnowledgeBases(){alert("显示现有知识库管理界面")},editKnowledgeBase(e){this.currentKnowledgeBase=e,this.showEditor=!0},viewKnowledgeBase(e){alert(`查看知识库: ${e.name}\n类型: ${this.getTypeLabel(e.type)}\n内容数量: ${e.itemCount}`)},deleteKnowledgeBase(e){confirm("确定要删除这个知识库吗？此操作不可恢复。")&&(this.knowledgeBases=this.knowledgeBases.filter(t=>t.id!==e))},onKnowledgeBaseComplete(e){this.knowledgeBases.push({...e,id:Date.now(),itemCount:e.files?.length||0,updateTime:new Date,status:"completed"}),this.showManager=!1,alert("知识库创建成功！")},onKnowledgeBaseSave(e){const t=this.knowledgeBases.findIndex(t=>t.id===e.knowledgeBase.id);-1!==t&&(this.knowledgeBases[t]={...this.knowledgeBases[t],...e.knowledgeBase,updateTime:new Date}),this.showEditor=!1,alert("知识库保存成功！")},getTypeLabel(e){const t={text:"文本格式",table:"表格格式",image:"图片格式"};return t[e]||"未知格式"},formatDate(e){return new Date(e).toLocaleDateString("zh-CN")}}};const Pl=(0,_.A)($l,[["render",Fl],["__scopeId","data-v-5dff746d"]]);var Rl=Pl;const Xl={class:"min-h-screen bg-gray-50 py-8"},Vl={class:"max-w-4xl mx-auto px-4"},zl={class:"bg-white rounded-xl shadow-lg p-6 mb-8"},Ol={key:0,class:"bg-white rounded-xl shadow-lg p-6"},Nl={class:"mb-6 p-4 bg-gray-50 rounded-lg"},Ul={class:"grid grid-cols-2 gap-4 text-sm"},Wl={key:0,class:"mb-6 p-4 bg-purple-50 rounded-lg"},Ql={class:"grid grid-cols-2 gap-4 text-sm"},Jl={key:0,class:"mt-3"},Hl={class:"flex flex-wrap gap-1 mt-1"},Gl={key:1,class:"mb-6 p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200"},ql={class:"flex items-center"},Zl={class:"mb-6 p-4 bg-blue-50 rounded-lg"},Yl={class:"grid grid-cols-3 gap-4"},ei={class:"mb-6"},ti={class:"font-semibold text-gray-900 mb-3"},si={class:"space-y-4 max-h-96 overflow-y-auto"},oi={class:"flex items-center justify-between mb-3"},ni={class:"font-medium text-gray-900"},ai={class:"flex items-center space-x-3 text-sm text-gray-500"},ri={class:"mb-3 p-3 bg-gray-50 rounded text-sm"},li={class:"max-h-32 overflow-y-auto"},ii={key:0,class:"mb-2"},ci={key:1,class:"text-xs text-gray-700"},di={class:"p-4 bg-green-50 rounded-lg"},gi={class:"grid grid-cols-4 gap-4 text-sm"},ui={key:1,class:"bg-white rounded-xl shadow-lg p-8 text-center"},hi={key:2,class:"bg-red-50 border border-red-200 rounded-xl p-6 text-center"},mi={class:"text-red-700"};function pi(e,t,s,r,l,i){return(0,n.uX)(),(0,n.CE)("div",Xl,[(0,n.Lk)("div",Vl,[t[33]||(t[33]=(0,n.Lk)("h1",{class:"text-3xl font-bold text-gray-900 mb-8 text-center"},"文档解析测试",-1)),(0,n.Lk)("div",zl,[t[14]||(t[14]=(0,n.Lk)("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"上传测试文件",-1)),(0,n.Lk)("div",{class:"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors cursor-pointer",onClick:t[0]||(t[0]=(...e)=>i.triggerFileUpload&&i.triggerFileUpload(...e)),onDragover:t[1]||(t[1]=(0,o.D$)(()=>{},["prevent"])),onDrop:t[2]||(t[2]=(0,o.D$)((...e)=>i.handleFileDrop&&i.handleFileDrop(...e),["prevent"]))},t[13]||(t[13]=[(0,n.Lk)("div",{class:"text-4xl text-gray-400 mb-4"},"📁",-1),(0,n.Lk)("p",{class:"text-gray-600 mb-2"},"点击选择文件或拖拽文件到此处",-1),(0,n.Lk)("p",{class:"text-sm text-gray-500"},"支持 TXT、Markdown、Word文档(.docx) 格式",-1)]),32),(0,n.Lk)("input",{ref:"fileInput",type:"file",class:"hidden",accept:".txt,.md,.docx",onChange:t[3]||(t[3]=(...e)=>i.handleFileSelect&&i.handleFileSelect(...e))},null,544)]),l.parseResult?((0,n.uX)(),(0,n.CE)("div",Ol,[t[29]||(t[29]=(0,n.Lk)("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"解析结果",-1)),(0,n.Lk)("div",Nl,[t[15]||(t[15]=(0,n.Lk)("h3",{class:"font-semibold text-gray-900 mb-2"},"文件信息",-1)),(0,n.Lk)("div",Ul,[(0,n.Lk)("div",null,"文件名: "+(0,a.v_)(l.parseResult.fileName),1),(0,n.Lk)("div",null,"文件大小: "+(0,a.v_)(i.formatFileSize(l.parseResult.fileSize)),1),(0,n.Lk)("div",null,"文件类型: "+(0,a.v_)(l.parseResult.fileType),1),(0,n.Lk)("div",null,"解析时间: "+(0,a.v_)(l.parseResult.parseTime)+"ms",1)])]),l.parseResult.analysis?((0,n.uX)(),(0,n.CE)("div",Wl,[t[17]||(t[17]=(0,n.Lk)("h3",{class:"font-semibold text-gray-900 mb-2"},"AI文档分析",-1)),(0,n.Lk)("div",Ql,[(0,n.Lk)("div",null,"文档类型: "+(0,a.v_)(l.parseResult.analysis.documentType),1),(0,n.Lk)("div",null,"结构评分: "+(0,a.v_)(l.parseResult.analysis.structureScore)+"/10",1),(0,n.Lk)("div",null,"质量评分: "+(0,a.v_)(l.parseResult.analysis.qualityScore)+"/10",1),(0,n.Lk)("div",null,"推荐策略: "+(0,a.v_)(l.parseResult.analysis.recommendedStrategy),1)]),l.parseResult.analysis.mainTopics&&l.parseResult.analysis.mainTopics.length>0?((0,n.uX)(),(0,n.CE)("div",Jl,[t[16]||(t[16]=(0,n.Lk)("span",{class:"text-sm font-medium text-gray-700"},"主要主题:",-1)),(0,n.Lk)("div",Hl,[((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(l.parseResult.analysis.mainTopics,e=>((0,n.uX)(),(0,n.CE)("span",{key:e,class:"px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded"},(0,a.v_)(e),1))),128))])])):(0,n.Q3)("",!0)])):(0,n.Q3)("",!0),l.aiEnabled?((0,n.uX)(),(0,n.CE)("div",Gl,[t[19]||(t[19]=(0,n.Lk)("h3",{class:"font-semibold text-gray-900 mb-3 flex items-center"},[(0,n.Lk)("span",{class:"w-2 h-2 bg-green-500 rounded-full mr-2"}),(0,n.eW)(" AI增强功能 ")],-1)),(0,n.Lk)("div",ql,[(0,n.bo)((0,n.Lk)("input",{"onUpdate:modelValue":t[4]||(t[4]=e=>l.segmentSettings.useAI=e),onChange:t[5]||(t[5]=(...e)=>i.reSegment&&i.reSegment(...e)),type:"checkbox",id:"useAI",class:"w-4 h-4 text-blue-600 border-gray-300 rounded"},null,544),[[o.lH,l.segmentSettings.useAI]]),t[18]||(t[18]=(0,n.Lk)("label",{for:"useAI",class:"ml-2 text-sm text-gray-700"},"启用AI智能分段和内容分析",-1))]),t[20]||(t[20]=(0,n.Lk)("p",{class:"text-xs text-gray-600 mt-2"}," AI功能将提供更准确的语义分段、关键词提取和摘要生成 ",-1))])):(0,n.Q3)("",!0),(0,n.Lk)("div",Zl,[t[25]||(t[25]=(0,n.Lk)("h3",{class:"font-semibold text-gray-900 mb-3"},"分段设置",-1)),(0,n.Lk)("div",Yl,[(0,n.Lk)("div",null,[t[22]||(t[22]=(0,n.Lk)("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"分段策略",-1)),(0,n.bo)((0,n.Lk)("select",{"onUpdate:modelValue":t[6]||(t[6]=e=>l.segmentSettings.strategy=e),onChange:t[7]||(t[7]=(...e)=>i.reSegment&&i.reSegment(...e)),class:"w-full px-3 py-2 border border-gray-200 rounded text-sm"},t[21]||(t[21]=[(0,n.Lk)("option",{value:"auto"},"自动分段",-1),(0,n.Lk)("option",{value:"semantic"},"语义分段",-1),(0,n.Lk)("option",{value:"fixed"},"固定长度",-1),(0,n.Lk)("option",{value:"manual"},"手动分段",-1)]),544),[[o.u1,l.segmentSettings.strategy]])]),(0,n.Lk)("div",null,[t[23]||(t[23]=(0,n.Lk)("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"最大长度",-1)),(0,n.bo)((0,n.Lk)("input",{"onUpdate:modelValue":t[8]||(t[8]=e=>l.segmentSettings.maxLength=e),onChange:t[9]||(t[9]=(...e)=>i.reSegment&&i.reSegment(...e)),type:"number",min:"100",max:"5000",class:"w-full px-3 py-2 border border-gray-200 rounded text-sm"},null,544),[[o.Jo,l.segmentSettings.maxLength,void 0,{number:!0}]])]),(0,n.Lk)("div",null,[t[24]||(t[24]=(0,n.Lk)("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"重叠长度",-1)),(0,n.bo)((0,n.Lk)("input",{"onUpdate:modelValue":t[10]||(t[10]=e=>l.segmentSettings.overlap=e),onChange:t[11]||(t[11]=(...e)=>i.reSegment&&i.reSegment(...e)),type:"number",min:"0",max:"500",class:"w-full px-3 py-2 border border-gray-200 rounded text-sm"},null,544),[[o.Jo,l.segmentSettings.overlap,void 0,{number:!0}]])])])]),(0,n.Lk)("div",ei,[(0,n.Lk)("h3",ti," 分段结果 (共 "+(0,a.v_)(l.segments.length)+" 个分段) ",1),(0,n.Lk)("div",si,[((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(l.segments,e=>((0,n.uX)(),(0,n.CE)("div",{key:e.id,class:"border border-gray-200 rounded-lg p-4"},[(0,n.Lk)("div",oi,[(0,n.Lk)("h4",ni,"分段 "+(0,a.v_)(e.id),1),(0,n.Lk)("div",ai,[(0,n.Lk)("span",null,(0,a.v_)(e.content.length)+" 字符",1),(0,n.Lk)("span",null,(0,a.v_)(e.keywords.length)+" 关键词",1)])]),(0,n.Lk)("div",ri,[(0,n.Lk)("div",li,(0,a.v_)(e.content),1)]),e.keywords.length>0?((0,n.uX)(),(0,n.CE)("div",ii,[t[26]||(t[26]=(0,n.Lk)("span",{class:"text-xs text-gray-600 mr-2"},"关键词:",-1)),((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(e.keywords,e=>((0,n.uX)(),(0,n.CE)("span",{key:e,class:"inline-block px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded mr-1"},(0,a.v_)(e),1))),128))])):(0,n.Q3)("",!0),e.summary?((0,n.uX)(),(0,n.CE)("div",ci,[t[27]||(t[27]=(0,n.Lk)("span",{class:"text-gray-600"},"摘要:",-1)),(0,n.eW)(" "+(0,a.v_)(e.summary),1)])):(0,n.Q3)("",!0)]))),128))])]),(0,n.Lk)("div",di,[t[28]||(t[28]=(0,n.Lk)("h3",{class:"font-semibold text-gray-900 mb-2"},"统计信息",-1)),(0,n.Lk)("div",gi,[(0,n.Lk)("div",null,"总字符数: "+(0,a.v_)(l.parseResult.content.length),1),(0,n.Lk)("div",null,"分段数量: "+(0,a.v_)(l.segments.length),1),(0,n.Lk)("div",null,"平均分段长度: "+(0,a.v_)(Math.round(l.parseResult.content.length/l.segments.length)),1),(0,n.Lk)("div",null,"总关键词数: "+(0,a.v_)(i.totalKeywords),1)])])])):(0,n.Q3)("",!0),l.isLoading?((0,n.uX)(),(0,n.CE)("div",ui,t[30]||(t[30]=[(0,n.Lk)("div",{class:"w-16 h-16 mx-auto mb-4 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"},null,-1),(0,n.Lk)("p",{class:"text-gray-600"},"正在解析文档...",-1)]))):(0,n.Q3)("",!0),l.error?((0,n.uX)(),(0,n.CE)("div",hi,[t[31]||(t[31]=(0,n.Lk)("div",{class:"text-red-500 text-4xl mb-4"},"❌",-1)),t[32]||(t[32]=(0,n.Lk)("h3",{class:"text-lg font-semibold text-red-900 mb-2"},"解析失败",-1)),(0,n.Lk)("p",mi,(0,a.v_)(l.error),1),(0,n.Lk)("button",{onClick:t[12]||(t[12]=(...e)=>i.clearError&&i.clearError(...e)),class:"mt-4 px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"}," 重新尝试 ")])):(0,n.Q3)("",!0)])])}var bi={name:"DocumentParserTest",data(){return{isLoading:!1,error:null,parseResult:null,segments:[],parser:null,aiEnabled:!1,segmentSettings:{strategy:"auto",maxLength:1e3,overlap:100,useAI:!0}}},mounted(){this.initializeParser()},computed:{totalKeywords(){return this.segments.reduce((e,t)=>e+t.keywords.length,0)}},methods:{initializeParser(){try{this.parser=new er({bailian:or.getBailianConfig(),useAI:this.segmentSettings.useAI,fallbackToLocal:!0}),this.aiEnabled=or.isBailianConfigured()}catch(e){console.error("初始化解析器失败:",e),this.aiEnabled=!1}},triggerFileUpload(){this.$refs.fileInput.click()},handleFileSelect(e){const t=e.target.files[0];t&&this.parseFile(t)},handleFileDrop(e){const t=e.dataTransfer.files[0];t&&this.parseFile(t)},async parseFile(e){this.isLoading=!0,this.error=null,this.parseResult=null,this.segments=[];try{const t=Date.now(),s=await this.parser.parseDocument(e,{useAI:this.segmentSettings.useAI&&this.aiEnabled}),o=Date.now()-t;this.parseResult={...s,parseTime:o},this.reSegment()}catch(t){console.error("文档解析失败:",t),this.error=t.message}finally{this.isLoading=!1}},async reSegment(){if(this.parseResult)try{this.isLoading=!0,this.segments=await this.parser.segmentDocument(this.parseResult.content,{...this.segmentSettings,useAI:this.segmentSettings.useAI&&this.aiEnabled},this.parseResult.analysis)}catch(e){console.error("分段失败:",e),this.error="分段处理失败: "+e.message}finally{this.isLoading=!1}},clearError(){this.error=null},formatFileSize(e){if(0===e)return"0 Bytes";const t=1024,s=["Bytes","KB","MB","GB"],o=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,o)).toFixed(2))+" "+s[o]}}};const fi=(0,_.A)(bi,[["render",pi],["__scopeId","data-v-46bf4e48"]]);var xi=fi;const yi={key:0,class:"fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 animate-in fade-in duration-300"},ki={key:0,class:"fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 animate-in slide-in-from-top max-w-xs"},vi={key:0,class:"text-xs mt-1 text-red-100 whitespace-pre-wrap"},wi={key:1,class:"fixed top-4 right-4 bg-amber-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 animate-in slide-in-from-top max-w-xs"},Li={key:0,class:"text-xs mt-1 text-amber-100 whitespace-pre-wrap"},Ci={class:"bg-white rounded-3xl shadow-2xl w-[90vw] max-w-2xl mx-4 overflow-hidden transform transition-all duration-500 hover:shadow-3xl"},Si={class:"relative bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 text-white p-6 overflow-hidden"},Ti={class:"flex items-center justify-between relative z-10"},Ei={class:"p-8"},Mi={class:"mb-8 relative"},Ai={key:0,class:"absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full"},Bi={key:0,class:"absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full"},Ki={key:0,class:"grid grid-cols-1 md:grid-cols-2 gap-8 animate-in fade-in-50 duration-300"},Ii={class:"group"},_i={class:"relative"},ji=["value"],Di={class:"group"},Fi={class:"relative"},$i=["value"],Pi={key:1,class:"space-y-8 animate-in fade-in-50 duration-300"},Ri={class:"group"},Xi={class:"flex items-center justify-center w-full"},Vi={class:"flex flex-col w-full h-40 border-2 border-dashed border-gray-200 rounded-xl hover:border-indigo-300 hover:bg-indigo-50/50 cursor-pointer transition-all group p-6"},zi={key:0,class:"text-center py-10 text-gray-500 bg-gray-50 rounded-xl"},Oi={class:"space-y-3"},Ni={class:"flex items-center"},Ui={class:"font-medium group-hover:text-indigo-600 transition-colors"},Wi={class:"flex space-x-2"},Qi=["onClick"],Ji=["onClick"],Hi={class:"pt-8 mt-8 border-t border-gray-100 grid grid-cols-1 md:grid-cols-2 gap-8"},Gi={class:"group"},qi={class:"relative"},Zi=["value"],Yi={class:"flex justify-between items-center mb-2.5"},ec={class:"text-sm font-medium text-indigo-600"},tc={class:"flex justify-between items-center mb-2.5"},sc={class:"text-sm font-medium text-indigo-600"},oc={class:"group"},nc={class:"relative"},ac={class:"flex justify-end space-x-4 pt-8"};function rc(e,t,s,r,l,i){return s.show?((0,n.uX)(),(0,n.CE)("div",yi,[l.errorMessage?((0,n.uX)(),(0,n.CE)("div",ki,[(0,n.eW)((0,a.v_)(l.errorMessage)+" ",1),l.errorDetails?((0,n.uX)(),(0,n.CE)("div",vi,(0,a.v_)(l.errorDetails),1)):(0,n.Q3)("",!0)])):(0,n.Q3)("",!0),l.warningMessage?((0,n.uX)(),(0,n.CE)("div",wi,[(0,n.eW)(" ⚠️ "+(0,a.v_)(l.warningMessage)+" ",1),l.warningDetails?((0,n.uX)(),(0,n.CE)("div",Li,(0,a.v_)(l.warningDetails),1)):(0,n.Q3)("",!0)])):(0,n.Q3)("",!0),(0,n.Lk)("div",Ci,[(0,n.Lk)("div",Si,[t[20]||(t[20]=(0,n.Lk)("div",{class:"absolute -right-20 -top-20 w-64 h-64 bg-white/10 rounded-full blur-3xl"},null,-1)),t[21]||(t[21]=(0,n.Lk)("div",{class:"absolute left-20 bottom-0 w-32 h-32 bg-white/5 rounded-full blur-2xl"},null,-1)),(0,n.Lk)("div",Ti,[t[19]||(t[19]=(0,n.Lk)("h3",{class:"text-2xl font-serif font-semibold flex items-center"},[(0,n.Lk)("span",{class:"mr-3 text-2xl"},"🎵"),(0,n.eW)(" 声音配置中心 ")],-1)),(0,n.Lk)("button",{onClick:t[0]||(t[0]=t=>e.$emit("close")),class:"text-white/80 hover:text-white transition-colors p-2 rounded-full hover:bg-white/10 hover:scale-110 transition-transform","aria-label":"关闭"},t[18]||(t[18]=[(0,n.Lk)("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[(0,n.Lk)("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2.5",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]),(0,n.Lk)("div",Ei,[(0,n.Lk)("div",Mi,[t[24]||(t[24]=(0,n.Lk)("div",{class:"absolute bottom-0 left-0 right-0 h-0.5 bg-gray-100"},null,-1)),(0,n.Lk)("button",{onClick:t[1]||(t[1]=e=>l.activeTab="default"),class:(0,a.C4)(["default"===l.activeTab?"text-indigo-600":"text-gray-500 hover:text-gray-700","py-3 px-6 font-medium relative transition-all text-lg"])},[t[22]||(t[22]=(0,n.eW)(" 系统语音 ",-1)),"default"===l.activeTab?((0,n.uX)(),(0,n.CE)("span",Ai)):(0,n.Q3)("",!0)],2),(0,n.Lk)("button",{onClick:t[2]||(t[2]=e=>l.activeTab="packages"),class:(0,a.C4)(["packages"===l.activeTab?"text-indigo-600":"text-gray-500 hover:text-gray-700","py-3 px-6 font-medium relative transition-all text-lg"])},[t[23]||(t[23]=(0,n.eW)(" 语音包 ",-1)),"packages"===l.activeTab?((0,n.uX)(),(0,n.CE)("span",Bi)):(0,n.Q3)("",!0)],2)]),"default"===l.activeTab?((0,n.uX)(),(0,n.CE)("div",Ki,[(0,n.Lk)("div",Ii,[t[26]||(t[26]=(0,n.Lk)("label",{class:"block text-sm font-medium text-gray-700 mb-2.5 group-hover:text-indigo-600 transition-colors"}," 语言选择 ",-1)),(0,n.Lk)("div",_i,[(0,n.bo)((0,n.Lk)("select",{"onUpdate:modelValue":t[3]||(t[3]=e=>l.selectedLanguage=e),onChange:t[4]||(t[4]=(...e)=>i.onLanguageChange&&i.onLanguageChange(...e)),class:"w-full px-4 py-3.5 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500/50 focus:border-indigo-500 transition-all appearance-none text-base"},[((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(l.languages,e=>((0,n.uX)(),(0,n.CE)("option",{key:e.code,value:e.code},(0,a.v_)(e.name),9,ji))),128))],544),[[o.u1,l.selectedLanguage]]),t[25]||(t[25]=(0,n.Lk)("div",{class:"absolute right-4 top-1/2 transform -translate-y-1/2 pointer-events-none text-gray-400"},[(0,n.Lk)("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[(0,n.Lk)("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})])],-1))])]),(0,n.Lk)("div",Di,[t[28]||(t[28]=(0,n.Lk)("label",{class:"block text-sm font-medium text-gray-700 mb-2.5 group-hover:text-indigo-600 transition-colors"}," 声音选择 ",-1)),(0,n.Lk)("div",Fi,[(0,n.bo)((0,n.Lk)("select",{"onUpdate:modelValue":t[5]||(t[5]=e=>l.selectedVoice=e),onChange:t[6]||(t[6]=(...e)=>i.onVoiceChange&&i.onVoiceChange(...e)),class:"w-full px-4 py-3.5 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500/50 focus:border-indigo-500 transition-all appearance-none text-base"},[((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(i.currentVoices,e=>((0,n.uX)(),(0,n.CE)("option",{key:e.code,value:e.code},(0,a.v_)(e.name),9,$i))),128))],544),[[o.u1,l.selectedVoice]]),t[27]||(t[27]=(0,n.Lk)("div",{class:"absolute right-4 top-1/2 transform -translate-y-1/2 pointer-events-none text-gray-400"},[(0,n.Lk)("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[(0,n.Lk)("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})])],-1))])])])):(0,n.Q3)("",!0),"packages"===l.activeTab?((0,n.uX)(),(0,n.CE)("div",Pi,[(0,n.Lk)("div",Ri,[t[30]||(t[30]=(0,n.Lk)("label",{class:"block text-sm font-medium text-gray-700 mb-2.5 group-hover:text-indigo-600 transition-colors"}," 上传语音包 ",-1)),(0,n.Lk)("div",Xi,[(0,n.Lk)("label",Vi,[t[29]||(t[29]=(0,n.Fv)('<div class="flex flex-col items-center justify-center" data-v-2f65cf20><div class="w-14 h-14 rounded-full bg-gradient-to-br from-indigo-100 to-purple-100 flex items-center justify-center text-indigo-500 group-hover:scale-110 transition-transform mb-4" data-v-2f65cf20><svg class="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-2f65cf20><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" data-v-2f65cf20></path></svg></div><p class="text-base text-gray-600" data-v-2f65cf20>点击上传或拖放语音包文件</p><p class="text-xs text-gray-400 mt-1" data-v-2f65cf20>支持 .zip, .mp3, .wav 格式</p></div>',1)),(0,n.Lk)("input",{type:"file",class:"opacity-0",onChange:t[7]||(t[7]=(...e)=>i.handleVoicePackageUpload&&i.handleVoicePackageUpload(...e)),accept:".zip,.mp3,.wav"},null,32)])])]),(0,n.Lk)("div",null,[t[34]||(t[34]=(0,n.Lk)("label",{class:"block text-sm font-medium text-gray-700 mb-2.5"}," 已安装语音包 ",-1)),0===l.voicePackages.length?((0,n.uX)(),(0,n.CE)("div",zi,t[31]||(t[31]=[(0,n.Lk)("div",{class:"w-20 h-20 mx-auto mb-4 rounded-full bg-gray-100 flex items-center justify-center text-gray-400"},[(0,n.Lk)("svg",{class:"w-10 h-10",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[(0,n.Lk)("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M12 19l9 2-9-18-9 18 9-2zm0 0v-8"})])],-1),(0,n.Lk)("p",null,"暂无语音包，请上传",-1)]))):(0,n.Q3)("",!0),(0,n.Lk)("div",Oi,[((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(l.voicePackages,e=>((0,n.uX)(),(0,n.CE)("div",{key:e.name,class:"flex items-center justify-between p-4 bg-white border border-gray-100 rounded-xl hover:border-indigo-200 hover:shadow-sm transition-all group"},[(0,n.Lk)("div",Ni,[t[32]||(t[32]=(0,n.Lk)("div",{class:"w-10 h-10 rounded-lg bg-gradient-to-br from-purple-100 to-pink-100 flex items-center justify-center text-purple-600 mr-3"}," 🎙️ ",-1)),(0,n.Lk)("span",Ui,(0,a.v_)(e.name),1)]),(0,n.Lk)("div",Wi,[(0,n.Lk)("button",{onClick:t=>i.selectVoicePackage(e),class:(0,a.C4)([l.selectedVoicePackage===e.name?"bg-indigo-100 text-indigo-800 hover:bg-indigo-200":"bg-gray-100 text-gray-800 hover:bg-gray-200","px-3 py-1.5 rounded-lg text-sm transition-colors"])},(0,a.v_)(l.selectedVoicePackage===e.name?"已选中":"选择"),11,Qi),(0,n.Lk)("button",{onClick:t=>i.downloadVoicePackage(e.name),class:"p-1.5 rounded-lg text-gray-500 hover:bg-gray-100 transition-colors","aria-label":"下载"},t[33]||(t[33]=[(0,n.Lk)("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[(0,n.Lk)("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"})],-1)]),8,Ji)])]))),128))])])])):(0,n.Q3)("",!0),(0,n.Lk)("div",Hi,[(0,n.Lk)("div",Gi,[t[36]||(t[36]=(0,n.Lk)("label",{class:"block text-sm font-medium text-gray-700 mb-2.5 group-hover:text-indigo-600 transition-colors"}," 情感调节 ",-1)),(0,n.Lk)("div",qi,[(0,n.bo)((0,n.Lk)("select",{"onUpdate:modelValue":t[8]||(t[8]=e=>l.selectedEmotion=e),onChange:t[9]||(t[9]=(...e)=>i.onEmotionChange&&i.onEmotionChange(...e)),class:"w-full px-4 py-3.5 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500/50 focus:border-indigo-500 transition-all appearance-none text-base"},[((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(l.emotions,e=>((0,n.uX)(),(0,n.CE)("option",{key:e.code,value:e.code},(0,a.v_)(e.name),9,Zi))),128))],544),[[o.u1,l.selectedEmotion]]),t[35]||(t[35]=(0,n.Lk)("div",{class:"absolute right-4 top-1/2 transform -translate-y-1/2 pointer-events-none text-gray-400"},[(0,n.Lk)("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[(0,n.Lk)("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})])],-1))])]),(0,n.Lk)("div",null,[(0,n.Lk)("div",Yi,[t[37]||(t[37]=(0,n.Lk)("label",{class:"block text-sm font-medium text-gray-700"}," 语速 ",-1)),(0,n.Lk)("span",ec,(0,a.v_)(Number(l.speechRate).toFixed(1)),1)]),(0,n.bo)((0,n.Lk)("input",{"onUpdate:modelValue":t[10]||(t[10]=e=>l.speechRate=e),onInput:t[11]||(t[11]=(...e)=>i.onRateChange&&i.onRateChange(...e)),type:"range",min:"0.5",max:"2",step:"0.1",class:"w-full h-2.5 bg-gray-100 rounded-full appearance-none cursor-pointer accent-indigo-500"},null,544),[[o.Jo,l.speechRate,void 0,{number:!0}]]),t[38]||(t[38]=(0,n.Lk)("div",{class:"flex justify-between text-xs text-gray-500 mt-1.5"},[(0,n.Lk)("span",null,"缓慢"),(0,n.Lk)("span",null,"自然"),(0,n.Lk)("span",null,"快速")],-1))]),(0,n.Lk)("div",null,[(0,n.Lk)("div",tc,[t[39]||(t[39]=(0,n.Lk)("label",{class:"block text-sm font-medium text-gray-700"}," 音量 ",-1)),(0,n.Lk)("span",sc,(0,a.v_)(l.volume),1)]),(0,n.bo)((0,n.Lk)("input",{"onUpdate:modelValue":t[12]||(t[12]=e=>l.volume=e),onInput:t[13]||(t[13]=(...e)=>i.onVolumeChange&&i.onVolumeChange(...e)),type:"range",min:"0",max:"10",step:"1",class:"w-full h-2.5 bg-gray-100 rounded-full appearance-none cursor-pointer accent-indigo-500"},null,544),[[o.Jo,l.volume,void 0,{number:!0}]])]),(0,n.Lk)("div",oc,[t[41]||(t[41]=(0,n.Lk)("label",{class:"block text-sm font-medium text-gray-700 mb-2.5 group-hover:text-indigo-600 transition-colors"}," 语音服务 ",-1)),(0,n.Lk)("div",nc,[(0,n.bo)((0,n.Lk)("select",{"onUpdate:modelValue":t[14]||(t[14]=e=>l.localTtsService=e),onChange:t[15]||(t[15]=(...e)=>i.onTtsServiceChange&&i.onTtsServiceChange(...e)),class:"w-full px-4 py-3.5 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500/50 focus:border-indigo-500 transition-all appearance-none text-base"},null,544),[[o.u1,l.localTtsService]]),t[40]||(t[40]=(0,n.Lk)("div",{class:"absolute right-4 top-1/2 transform -translate-y-1/2 pointer-events-none text-gray-400"},[(0,n.Lk)("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[(0,n.Lk)("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})])],-1))])])]),(0,n.Lk)("div",ac,[(0,n.Lk)("button",{onClick:t[16]||(t[16]=(...e)=>i.saveSettings&&i.saveSettings(...e)),class:"bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-8 py-3.5 rounded-xl transition-all duration-300 font-medium shadow-md shadow-indigo-100/50 hover:shadow-indigo-200/50 transform hover:-translate-y-0.5 active:translate-y-0"}," 保存设置 "),(0,n.Lk)("button",{onClick:t[17]||(t[17]=t=>e.$emit("close")),class:"bg-gray-100 hover:bg-gray-200 text-gray-800 px-8 py-3.5 rounded-xl transition-all duration-300 font-medium"}," 取消 ")])])])])):(0,n.Q3)("",!0)}var lc={name:"VoiceSettingsModal",props:{show:{type:Boolean,default:!1},currentSettings:{type:Object,default:()=>({language:"zh-cn",voice:"601002",emotion:"neutral",rate:1,volume:5,voicePackage:"",ttsService:"baidu"})},apiBaseUrl:{type:String,default:"http://localhost:8080"},debugMode:{type:Boolean,default:!0},ttsService:{type:String,default:"baidu",validator:e=>["google","baidu","custom"].includes(e)}},emits:["close","settings-changed","error","update:ttsService"],data(){return{activeTab:"default",selectedLanguage:"zh-cn",selectedVoice:"601002",selectedEmotion:"neutral",speechRate:1,volume:5,voicePackages:[],selectedVoicePackage:"",errorMessage:"",errorDetails:"",warningMessage:"",warningDetails:"",localTtsService:this.ttsService,languages:[{code:"zh-cn",name:"中文(普通话)"},{code:"en-us",name:"英语(美国)"},{code:"ja-jp",name:"日语"},{code:"ko-kr",name:"韩语"},{code:"fr-fr",name:"法语"},{code:"de-de",name:"德语"}],emotions:[{code:"neutral",name:"中性"},{code:"happy",name:"开心"},{code:"sad",name:"悲伤"},{code:"angry",name:"愤怒"},{code:"surprised",name:"惊讶"}],voiceOptions:{"zh-cn":[{code:"601002",name:"智瑜 - 温暖女声"},{code:"601003",name:"智聆 - 通用女声"},{code:"601004",name:"智美 - 甜美女声"},{code:"601005",name:"智云 - 通用男声"},{code:"601006",name:"智莉 - 通用女声"},{code:"601007",name:"智言 - 助手女声"}],"en-us":[{code:"601101",name:"Emma - 英文女声"},{code:"601102",name:"Brian - 英文男声"},{code:"601103",name:"Amy - 英文女声"},{code:"601104",name:"Russell - 英文男声"}],"ja-jp":[{code:"601201",name:"さくら - 日文女声"},{code:"601202",name:"たかし - 日文男声"}],"ko-kr":[{code:"601301",name:"지은 - 韩文女声"},{code:"601302",name:"민수 - 韩文男声"}],"fr-fr":[{code:"601401",name:"Céline - 法文女声"},{code:"601402",name:"Pierre - 法文男声"}],"de-de":[{code:"601501",name:"Anna - 德文女声"},{code:"601502",name:"Hans - 德文男声"}]}}},computed:{currentVoices(){return this.voiceOptions[this.selectedLanguage]||[]},voicePackagesListApi(){return`${this.apiBaseUrl}/api/voice/packages/list`},voicePackagesUploadApi(){return`${this.apiBaseUrl}/api/voice/packages/upload`},voiceSettingsApi(){return`${this.apiBaseUrl}/api/voice/settings`}},methods:{showError(e,t=""){this.errorMessage=e,this.errorDetails=t,this.warningMessage="",this.warningDetails="",this.$emit("error",{message:e,details:t}),setTimeout(()=>{this.errorMessage="",this.errorDetails=""},8e3),this.debugMode&&console.error("语音设置错误:",e,"\n详情:",t)},showWarning(e,t=""){this.warningMessage=e,this.warningDetails=t,this.errorMessage="",this.errorDetails="",setTimeout(()=>{this.warningMessage="",this.warningDetails=""},5e3),this.debugMode&&console.warn("语音设置警告:",e,"\n详情:",t)},isValidUrl(e){try{return new URL(e),!0}catch(t){return!1}},onLanguageChange(){const e=this.currentVoices;e.length>0&&(this.selectedVoice=e[0].code)},onVoiceChange(){this.debugMode&&console.log("选择了新语音:",this.selectedVoice)},onEmotionChange(){this.debugMode&&console.log("选择了新情感:",this.selectedEmotion)},onRateChange(){this.debugMode&&console.log("语速调整为:",this.speechRate)},onVolumeChange(){this.debugMode&&console.log("音量调整为:",this.volume)},onTtsServiceChange(){this.debugMode&&console.log("切换到TTS服务:",this.localTtsService),this.$emit("update:ttsService",this.localTtsService)},async saveSettings(){const e={language:this.selectedLanguage,emotion:this.selectedEmotion,rate:Number(this.speechRate),volume:Number(this.volume),voicePackage:"packages"===this.activeTab?this.selectedVoicePackage:"",ttsService:this.localTtsService};"default"===this.activeTab&&(e.voice=this.selectedVoice);try{const t=await fetch(this.voiceSettingsApi,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok){const e=await t.text();throw new Error(`保存设置失败，状态码: ${t.status}, 内容: ${e}`)}localStorage.setItem("voiceSettings",JSON.stringify(e)),this.$emit("settings-changed",e),this.$emit("update:ttsService",this.localTtsService),this.$emit("close")}catch(t){console.error("保存设置失败:",t),this.showError("保存设置失败: "+(t.message||"未知错误"))}},async loadSettings(){try{const e=await fetch(this.voiceSettingsApi);if(e.ok){const t=await e.json();if(200===t.code&&t.data){const e=t.data;return this.selectedLanguage=e.language||"zh-cn",this.selectedVoice=e.voice||"601002",this.selectedEmotion=e.emotion||"neutral",this.speechRate=Number(e.rate)||1,this.volume=Number(e.volume)||5,this.selectedVoicePackage=e.voicePackage||"",this.localTtsService=e.ttsService||"baidu",void(this.selectedVoicePackage&&(this.activeTab="packages"))}}}catch(t){console.warn("从后端获取设置失败，将使用本地存储:",t)}const e=localStorage.getItem("voiceSettings");if(e)try{const t=JSON.parse(e);this.selectedLanguage=t.language||"zh-cn",this.selectedVoice=t.voice||"601002",this.selectedEmotion=t.emotion||"neutral",this.speechRate=Number(t.rate)||1,this.volume=Number(t.volume)||5,this.selectedVoicePackage=t.voicePackage||"",this.localTtsService=t.ttsService||"baidu",this.selectedVoicePackage&&(this.activeTab="packages")}catch(t){console.error("加载本地设置失败:",t),this.showError("加载保存的设置失败")}},async fetchVoicePackages(){try{const e=await fetch(this.voicePackagesListApi);if(e.ok){const t=await e.json();200===t.code?this.voicePackages=t.data||[]:this.showError("获取语音包列表失败: "+(t.msg||"未知错误"))}else{const t=await e.text();this.showError("获取语音包列表请求失败",`状态码: ${e.status}, 内容: ${t}`)}}catch(e){console.error("获取语音包列表失败:",e),this.showError("无法连接到服务器获取语音包列表",e.message)}},async handleVoicePackageUpload(e){const t=e.target.files[0];if(!t)return;if(t.size>104857600)return void this.showError("文件过大","请上传小于100MB的文件");const s=new FormData;s.append("file",t);try{const t=await fetch(this.voicePackagesUploadApi,{method:"POST",body:s});if(t.ok){const s=await t.json();200===s.code?(this.showError("语音包上传成功",s.msg||""),this.fetchVoicePackages(),e.target.value=""):this.showError("上传失败: "+(s.msg||"未知错误"))}else{const e=await t.text();this.showError("上传请求失败",`状态码: ${t.status}, 内容: ${e}`)}}catch(o){console.error("上传语音包失败:",o),this.showError("网络错误，上传失败",o.message)}},selectVoicePackage(e){this.selectedVoicePackage=e.name},downloadVoicePackage(e){try{const t=`${this.apiBaseUrl}/api/voice/packages/download?filename=${encodeURIComponent(e)}`;window.open(t,"_blank")}catch(t){console.error("下载语音包失败:",t),this.showError("下载失败: "+(t.message||"未知错误"))}}},watch:{show(e){e&&(this.fetchVoicePackages(),this.loadSettings())},ttsService(e){this.localTtsService=e},currentSettings:{handler(e){e&&(this.selectedLanguage=e.language||"zh-cn",this.selectedVoice=e.voice||"601002",this.selectedEmotion=e.emotion||"neutral",this.speechRate=Number(e.rate)||1,this.volume=Number(e.volume)||5,this.selectedVoicePackage=e.voicePackage||"",this.localTtsService=e.ttsService||"baidu")},deep:!0,immediate:!0}}};const ic=(0,_.A)(lc,[["render",rc],["__scopeId","data-v-2f65cf20"]]);var cc=ic,dc={name:"App",components:{VoiceSettingsModal:cc,AssistantList:P,ChatArea:de,ModalComponents:Qe,HistoryModal:ft,RobotSettingsModal:ml,KnowledgeBaseDemo:Rl,DocumentParserTest:xi},data(){return{currentView:"chat",showAddModal:!1,showDialModal:!1,showSettingsModal:!1,showDeleteModal:!1,showHistoryModal:!1,showChatSettingsModal:!1,showRobotSettingsModal:!1,currentConversationId:null,dialNumber:"",messageInput:"",searchQuery:"",isEditing:!1,currentSelectedAssistant:null,currentSettingsAssistant:null,currentPersonality:"",deleteAssistantId:null,showVoiceSettingsModal:!1,deleteAssistantName:"",currentAssistant:{id:null,name:"",description:""},assistantMessages:{},assistantPersonalities:{},assistants:[{id:1,name:"默认机器人",description:"这是一个可以用于简单测试对话的语音机器人。"}],chatSettings:{language:"zh-cn",speaker:"601002",systemRole:"你是一个专业的对话大模型工程师，专注于帮助用户解决技术相关的问题。",dialogCommand:"请以简洁、专业的方式回答用户的问题，尽量控制在合理的长度内",responseMode:"simple",temperature:.6,maxTokens:50,speechSpeed:1}}},computed:{currentMessages(){return this.currentSelectedAssistant&&this.assistantMessages[this.currentSelectedAssistant.id]||[]}},methods:{getViewName(){const e={chat:"对话助手",knowledge:"知识库管理","parser-test":"文档解析测试"};return e[this.currentView]||"未知视图"},openAddModal(){this.isEditing=!1,this.currentAssistant={id:null,name:"",description:""},this.showAddModal=!0},openEditModal(e){this.isEditing=!0,this.currentAssistant={id:e.id,name:e.name,description:e.description},this.showAddModal=!0},closeModal(){this.showAddModal=!1,this.currentAssistant={id:null,name:"",description:""}},async saveAssistant(){if(this.currentAssistant.name.trim()&&this.currentAssistant.description.trim()){if(this.isEditing)try{const e=await fetch("http://localhost:8080/api/robot/update",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({id:this.currentAssistant.id,name:this.currentAssistant.name.trim(),description:this.currentAssistant.description.trim(),personnel_design:this.assistantPersonalities[this.currentAssistant.id]||"你是一个友善、专业的AI助手。"})}),t=await e.json();if(200===t.code){const e=this.assistants.findIndex(e=>e.id===this.currentAssistant.id);-1!==e&&(this.assistants[e]={...this.currentAssistant},this.currentSelectedAssistant?.id===this.currentAssistant.id&&(this.currentSelectedAssistant={...this.currentAssistant}))}else console.error("更新失败:",t.msg)}catch(e){console.error("网络错误:",e)}else{const t={name:this.currentAssistant.name.trim(),description:this.currentAssistant.description.trim(),personnel_design:this.assistantPersonalities[this.currentAssistant.id]||"你是一个友善、专业的AI助手。"};try{const e=await fetch("http://localhost:8080/api/robot/create",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)}),s=await e.json();if(200===s.code){await this.fetchRobots();const e=s.data?.id,t=this.assistants.find(t=>t.id===e);t?(this.currentConversationId=null,this.selectAssistant(t)):(this.currentConversationId=null,this.selectAssistant(this.assistants[this.assistants.length-1]))}else console.error("创建失败:",s.msg)}catch(e){console.error("网络错误:",e)}}this.closeModal()}},openSettingsModal(e){this.currentSettingsAssistant=e,this.currentPersonality=this.assistantPersonalities[e.id]||"你是一个友善、专业的AI助手。",this.showRobotSettingsModal=!0},closeSettingsModal(){this.showSettingsModal=!1,this.currentSettingsAssistant=null,this.currentPersonality=""},async savePersonality(){if(this.currentSettingsAssistant){try{const e=await fetch("http://localhost:8080/api/robot/update",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({id:this.currentSettingsAssistant.id,name:this.currentSettingsAssistant.name,description:this.currentSettingsAssistant.description,personnel_design:this.currentPersonality})}),t=await e.json();200===t.code?(this.assistantPersonalities[this.currentSettingsAssistant.id]=this.currentPersonality,console.log("人设配置保存成功")):(console.error("人设配置保存失败:",t.msg),alert("保存失败: "+t.msg))}catch(e){console.error("网络错误:",e),alert("网络错误，保存失败")}this.closeSettingsModal()}},openDeleteModal(e){this.deleteAssistantId=e.id,this.deleteAssistantName=e.name,this.showDeleteModal=!0},closeDeleteModal(){this.showDeleteModal=!1,this.deleteAssistantId=null,this.deleteAssistantName=""},async confirmDelete(){if(this.deleteAssistantId){try{const e=await fetch("http://localhost:8080/api/robot/delete",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({id:this.deleteAssistantId})}),t=await e.json();200===t.code?(this.assistants=this.assistants.filter(e=>e.id!==this.deleteAssistantId),this.currentSelectedAssistant?.id===this.deleteAssistantId&&(this.currentSelectedAssistant=null),delete this.assistantMessages[this.deleteAssistantId],delete this.assistantPersonalities[this.deleteAssistantId]):console.error("删除失败:",t.msg)}catch(e){console.error("网络错误:",e)}this.closeDeleteModal()}},deleteAssistant(e){const t=this.assistants.find(t=>t.id===e);t&&this.openDeleteModal(t)},addNumber(e){this.dialNumber+=e},clearNumber(){this.dialNumber=""},async makeCall(){if(this.dialNumber)try{const e=parseInt(this.dialNumber,10);if(isNaN(e))return void alert("请输入有效的数字编号");const t=this.assistants.some(t=>t.id===e);if(!t)return void alert("没有该机器人，请重新输入编号");const s=encodeURIComponent(this.dialNumber),o=await fetch(`http://localhost:8080/api/robot/find/by_id?id=${s}`);if(!o.ok)throw new Error(`服务器错误: ${o.status}`);const n=await o.json();if(200===n.code){this.currentCallRobot=n.data,this.isCalling=!0;const t=this.assistants.find(t=>t.id===e);t&&this.selectAssistant(t),await fetch("http://localhost:8080/api/call/status",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({robot_id:this.currentCallRobot.id,status:"calling",id:this.dialNumber})}),this.startVoiceCall(this.currentCallRobot)}else alert("未找到对应的机器人")}catch(e){console.error("拨号失败",e),alert("拨号失败，请重试")}else alert("请输入机器人编号")},async endCall(){this.currentCallRobot&&(this.isCalling=!1,this.stopVoiceCall(),await fetch("http://localhost:8080/api/call/status",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({robot_id:this.currentCallRobot.id,status:"ended"})}),this.currentCallRobot=null,this.dialNumber="",this.showDialModal=!1)},startVoiceCall(e){this.$refs.chatAreaRef&&this.$refs.chatAreaRef.callVoiceRecorderToggle(e)},stopVoiceCall(){this.$refs.chatAreaRef&&this.$refs.chatAreaRef.isWebRTCConnected&&this.$refs.chatAreaRef.callVoiceRecorderDisconnect()},handleVoiceMessage(e){this.$refs.chatAreaRef&&this.$refs.chatAreaRef.handleVoiceMessage(e)},selectAssistant(e){this.currentSelectedAssistant=e,this.currentConversationId=null,this.assistantMessages[e.id]||(this.assistantMessages[e.id]=[{id:Date.now(),content:`您好！我是${e.name}，源于通义千问，阿里巴巴集团旗下的超大规模语言模型助手，有什么可以帮助您的吗？`,isUser:!1,timestamp:new Date}]),this.$nextTick(()=>{this.scrollToBottom()})},async sendMessage(){if(this.messageInput.trim()&&this.currentSelectedAssistant){this.assistantMessages[this.currentSelectedAssistant.id].push({id:Date.now(),content:this.messageInput.trim(),isUser:!0,messageType:"text",timestamp:new Date});const t=this.messageInput.trim();this.messageInput="";try{const e=this.assistantPersonalities[this.currentSelectedAssistant.id];e&&e.trim()&&await fetch("http://localhost:8080/api/text/send",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({user_id:this.currentSelectedAssistant.id.toString(),message:{role:"system",content:e},streaming:!1})});const s=await fetch("http://localhost:8080/api/text/send",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({user_id:this.currentSelectedAssistant.id.toString(),message:{role:"user",content:t},streaming:!1})});if(!s.ok){const e=await s.text();throw console.error("服务器响应:",e),new Error(`HTTP error! status: ${s.status}`)}const o=await s.json();if(200!==o.code)throw new Error(o.msg||"服务器返回错误");this.assistantMessages[this.currentSelectedAssistant.id].push({id:Date.now()+1,content:o.data,isUser:!1,messageType:"text",timestamp:new Date}),await this.saveConversation()}catch(e){console.error("发送消息失败:",e),this.assistantMessages[this.currentSelectedAssistant.id].push({id:Date.now()+1,content:"抱歉，AI服务暂时不可用，请稍后重试",isUser:!1,timestamp:new Date})}this.scrollToBottom()}},scrollToBottom(){this.$nextTick(()=>{const e=this.$refs.chatAreaRef;if(e&&e.$refs.chatContainer){const t=e.$refs.chatContainer;t.scrollTop=t.scrollHeight,console.log("滚动到底部")}else console.warn("找不到聊天容器元素")})},resetChat(){this.currentSelectedAssistant&&(this.currentConversationId=null,this.assistantMessages[this.currentSelectedAssistant.id]=[{id:Date.now(),content:`您好！我是${this.currentSelectedAssistant.name}，我是通义千问，阿里巴巴集团旗下的超大规模语言模型助手，有什么可以帮助您的吗？`,isUser:!1,timestamp:new Date}],this.messageInput="",this.scrollToBottom())},async fetchRobots(){try{const e=await fetch("http://localhost:8080/api/robot/list",{method:"POST",headers:{"Content-Type":"application/json"}}),t=await e.json();console.log("获取到的机器人数据:",t),200===t.code&&t.data&&t.data.length>0?(this.assistants=t.data.map(e=>({id:e.id,name:e.name,description:e.description})),t.data.forEach(e=>{e.personnel_design&&(this.assistantPersonalities[e.id]=e.personnel_design)})):console.log("后端无数据，使用默认机器人")}catch(e){console.error("获取机器人列表失败:",e)}},handleVoiceInput(){alert("语音功能开发中...")},async saveConversation(){if(!this.currentSelectedAssistant||!this.currentMessages.length)return;const e=this.currentMessages.filter(e=>!(e.content.includes("您好！我是")&&!e.isUser));if(0!==e.length)try{const t=e.map(e=>({content:e.content,isUser:e.isUser,messageType:e.messageType||"text"}));console.log("保存对话数据:",{assistant_id:this.currentSelectedAssistant.id,assistant_name:this.currentSelectedAssistant.name,messages:t,conversation_id:this.currentConversationId});const s=await fetch("http://localhost:8080/api/history/save",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({assistant_id:this.currentSelectedAssistant.id,assistant_name:this.currentSelectedAssistant.name,messages:t,conversation_id:this.currentConversationId})}),o=await s.json();200===o.code?(this.currentConversationId=o.data.conversation_id,console.log("对话保存成功:",o)):console.error("保存对话失败:",o.msg)}catch(t){console.error("保存对话失败:",t)}},async restoreConversation(e){console.log("接收到恢复对话请求:",e);let t=this.assistants.find(t=>t.id==e.assistant.id);if(!t){console.log("助手不存在，尝试从数据库恢复");try{const o=await fetch(`http://localhost:8080/api/robot/findById?id=${e.assistant.id}`),n=await o.json();if(200===n.code&&n.data)t={id:n.data.id,name:n.data.name,description:n.data.description},this.assistants.push(t),n.data.personnel_design&&(this.assistantPersonalities[t.id]=n.data.personnel_design),console.log("从数据库恢复助手成功:",t);else{console.log("数据库中没有找到助手，重新创建到数据库");try{const s=await fetch("http://localhost:8080/api/robot/create",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e.assistant.name,description:e.assistant.description||`恢复的助手: ${e.assistant.name}`,personnel_design:"你是一个友善、专业的AI助手。"})}),o=await s.json();if(200!==o.code)throw new Error("创建助手失败");t={id:o.data.id,name:o.data.name,description:o.data.description},this.assistants.push(t),console.log("重新创建助手到数据库成功:",t)}catch(s){console.error("重新创建助手失败:",s),t={id:e.assistant.id,name:e.assistant.name,description:`已删除的助手: ${e.assistant.name}`},this.assistants.push(t)}}}catch(o){console.error("从数据库恢复助手失败:",o),t={id:e.assistant.id,name:e.assistant.name,description:`已删除的助手: ${e.assistant.name}`},this.assistants.push(t)}}this.currentSelectedAssistant=t,this.currentConversationId=null,this.assistantMessages[t.id]=e.messages.map(e=>({id:e.id||Date.now()+Math.random(),content:e.content,isUser:e.is_user,messageType:e.message_type||"text",timestamp:new Date(e.created_at)})),console.log("恢复的消息:",this.assistantMessages[t.id]),this.$nextTick(()=>{this.scrollToBottom()})},addMessage(e){if(this.currentSelectedAssistant){this.assistantMessages[this.currentSelectedAssistant.id]||(this.assistantMessages[this.currentSelectedAssistant.id]=[]);const t={...e,messageType:e.messageType||"text"};this.assistantMessages[this.currentSelectedAssistant.id].push(t),this.scrollToBottom()}},saveChatSettings(){console.log("保存对话设置:",this.chatSettings),this.showChatSettingsModal=!1},updateChatSetting(e,t){this.chatSettings[e]=t},closeRobotSettingsModal(){this.showRobotSettingsModal=!1,this.currentSettingsAssistant=null},async handleRobotSettingsSave(e){const t=this.assistants.findIndex(t=>t.id===e.id);-1!==t&&(this.assistants[t]={...this.assistants[t],...e},e.personnel_design&&(this.assistantPersonalities[e.id]=e.personnel_design),this.currentSelectedAssistant?.id===e.id&&(this.currentSelectedAssistant={...this.currentSelectedAssistant,...e})),await this.fetchRobots()}},async mounted(){await this.fetchRobots(),this.assistants.length>0&&this.selectAssistant(this.assistants[0])}};const gc=(0,_.A)(dc,[["render",b]]);var uc=gc;const hc=(0,o.Ef)(uc);hc.mount("#app")}},t={};function s(o){var n=t[o];if(void 0!==n)return n.exports;var a=t[o]={exports:{}};return e[o].call(a.exports,a,a.exports,s),a.exports}s.m=e,function(){var e=[];s.O=function(t,o,n,a){if(!o){var r=1/0;for(d=0;d<e.length;d++){o=e[d][0],n=e[d][1],a=e[d][2];for(var l=!0,i=0;i<o.length;i++)(!1&a||r>=a)&&Object.keys(s.O).every(function(e){return s.O[e](o[i])})?o.splice(i--,1):(l=!1,a<r&&(r=a));if(l){e.splice(d--,1);var c=n();void 0!==c&&(t=c)}}return t}a=a||0;for(var d=e.length;d>0&&e[d-1][2]>a;d--)e[d]=e[d-1];e[d]=[o,n,a]}}(),function(){s.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return s.d(t,{a:t}),t}}(),function(){s.d=function(e,t){for(var o in t)s.o(t,o)&&!s.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})}}(),function(){s.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){s.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){s.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){var e={524:0};s.O.j=function(t){return 0===e[t]};var t=function(t,o){var n,a,r=o[0],l=o[1],i=o[2],c=0;if(r.some(function(t){return 0!==e[t]})){for(n in l)s.o(l,n)&&(s.m[n]=l[n]);if(i)var d=i(s)}for(t&&t(o);c<r.length;c++)a=r[c],s.o(e,a)&&e[a]&&e[a][0](),e[a]=0;return s.O(d)},o=self["webpackChunkcall"]=self["webpackChunkcall"]||[];o.forEach(t.bind(null,0)),o.push=t.bind(null,o.push.bind(o))}();var o=s.O(void 0,[504],function(){return s(2065)});o=s.O(o)})();
//# sourceMappingURL=app.2e3c4399.js.map
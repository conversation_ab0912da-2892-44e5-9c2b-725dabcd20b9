<template>
  <div>
    <!-- ... existing code ... -->
    <div class="knowledge-base-section">
      <h3>选择文本知识库</h3>
      <button @click="createKnowledgeBase">创建知识库</button>
      <input type="text" placeholder="搜索知识库..." v-model="searchQuery">
      <div v-for="kb in knowledgeBases" :key="kb.id">
        <span>{{ kb.name }}</span>
        <button @click="selectKnowledgeBase(kb)">选择</button>
        <button @click="editKnowledgeBase(kb)">编辑</button>
        <!-- 添加删除知识库按钮 -->
        <button @click="deleteKnowledgeBase(kb)">删除</button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  methods: {
    deleteKnowledgeBase(kb) {
      // 调用删除知识库的API
      this.$axios.delete(`/api/knowledge_bases/${kb.id}`)
          .then(response => {
            // 处理删除成功的情况
            console.log('知识库删除成功', response);
            // 更新知识库列表
            this.knowledgeBases = this.knowledgeBases.filter(item => item.id !== kb.id);
          })
          .catch(error => {
            // 处理删除失败的情况
            console.error('知识库删除失败', error);
          });
    }
  }
}
</script>
// 知识库系统类型定义
// 确保前后端数据结构一致性

// 知识库类型枚举
export type KnowledgeBaseType = 'text' | 'table' | 'image' | 'document';

// 知识库状态枚举
export type KnowledgeBaseStatus = 'processing' | 'completed' | 'failed';

// 文件状态枚举
export type FileStatus = 'processing' | 'completed' | 'failed';

// 分段策略枚举
export type SegmentStrategy = 'auto' | 'semantic' | 'fixed' | 'manual';

// 知识库主表接口
export interface KnowledgeBase {
    id: string;
    name: string;
    description?: string;
    type: KnowledgeBaseType;
    status: KnowledgeBaseStatus;
    segmentCount: number;
    createdAt: string | Date;
    updatedAt: string | Date;

    // 关联数据（可选，根据API返回决定）
    files?: KnowledgeBaseFile[];
    settings?: KnowledgeBaseSettings;
}

// 知识库文件接口
export interface KnowledgeBaseFile {
    id: string;
    knowledgeBaseId: string;
    name: string;
    size: number;
    content?: string;
    status: FileStatus;
    progress: number;
    error?: string;
    aiEnhanced: boolean;
    createdAt: string | Date;
    updatedAt: string | Date;

    // 关联数据（可选）
    segments?: KnowledgeSegment[];
}

// 知识库分段接口
export interface KnowledgeSegment {
    id: string;
    fileId: string;
    knowledgeBaseId: string;
    content: string;
    keywords?: string[] | string; // 支持数组或JSON字符串
    summary?: string;
    length: number;
    position: number;
    embedding?: number[] | string; // 支持数组或JSON字符串
    createdAt: string | Date;
    updatedAt: string | Date;

    // 前端显示用字段
    fileName?: string;
}

// 知识库设置接口
export interface KnowledgeBaseSettings {
    id?: number;
    knowledgeBaseId: string;
    parseText: boolean;
    parseTable: boolean;
    contentFilter?: string;
    segmentStrategy: SegmentStrategy;
    segmentLength: number;
    segmentOverlap: number;
    separator?: string;
    extractKeywords: boolean;
    generateSummary: boolean;
    useAI: boolean;
    fallbackToLocal: boolean;
    createdAt?: string | Date;
    updatedAt?: string | Date;
}

// 知识库搜索结果接口
export interface KnowledgeSearchResult {
    id: string;
    content: string;
    score: number;
    source: string;
    knowledgeBaseId: string;
    keywords: string[];
    summary?: string;
}

// 前端显示用的知识库列表项接口
export interface KnowledgeBaseListItem {
    id: string;
    name: string;
    description?: string;
    type: KnowledgeBaseType;
    itemCount: number; // 对应 segmentCount
    updateTime: Date;
    status: KnowledgeBaseStatus;
    icon?: string;
}

// 创建知识库请求接口
export interface CreateKnowledgeBaseRequest {
    name: string;
    description?: string;
    type: KnowledgeBaseType;
    files?: KnowledgeBaseFile[];
    settings: KnowledgeBaseSettings;
}

// API响应基础接口
export interface ApiResponse<T = any> {
    code: number;
    msg: string;
    data: T;
}

// 知识库列表响应接口
export interface KnowledgeBaseListResponse extends ApiResponse<KnowledgeBase[]> {}

// 知识库详情响应接口
export interface KnowledgeBaseDetailResponse extends ApiResponse<KnowledgeBase> {}

// 类型映射工具函数
export class KnowledgeBaseTypeMapper {
    // 后端类型到前端类型的映射
    static backendToFrontend(backendType: string): KnowledgeBaseType {
        const mapping: Record<string, KnowledgeBaseType> = {
            'document': 'text',
            'text': 'text',
            'table': 'table',
            'image': 'image'
        };
        return mapping[backendType] || 'text';
    }

    // 前端类型到后端类型的映射
    static frontendToBackend(frontendType: KnowledgeBaseType): string {
        const mapping: Record<KnowledgeBaseType, string> = {
            'text': 'text',
            'table': 'table',
            'image': 'image',
            'document': 'text'
        };
        return mapping[frontendType] || 'text';
    }

    // 获取类型图标
    static getTypeIcon(type: KnowledgeBaseType): string {
        const icons: Record<KnowledgeBaseType, string> = {
            'text': '📄',
            'table': '📊',
            'image': '🖼️',
            'document': '📄'
        };
        return icons[type] || '📄';
    }

    // 获取类型标签
    static getTypeLabel(type: KnowledgeBaseType): string {
        const labels: Record<KnowledgeBaseType, string> = {
            'text': '文本格式',
            'table': '表格格式',
            'image': '图片格式',
            'document': '文档格式'
        };
        return labels[type] || '未知格式';
    }
}

// 数据转换工具函数
export class KnowledgeBaseDataConverter {
    // 将后端知识库数据转换为前端列表项
    static toListItem(kb: KnowledgeBase): KnowledgeBaseListItem {
        return {
            id: kb.id,
            name: kb.name,
            description: kb.description,
            type: KnowledgeBaseTypeMapper.backendToFrontend(kb.type),
            itemCount: kb.segmentCount || 0,
            updateTime: new Date(kb.updatedAt),
            status: kb.status,
            icon: KnowledgeBaseTypeMapper.getTypeIcon(kb.type as KnowledgeBaseType)
        };
    }

    // 解析关键词（支持字符串和数组）
    static parseKeywords(keywords: string[] | string | undefined): string[] {
        if (!keywords) return [];
        if (Array.isArray(keywords)) return keywords;
        try {
            return JSON.parse(keywords);
        } catch {
            return [];
        }
    }

    // 序列化关键词为JSON字符串
    static serializeKeywords(keywords: string[]): string {
        return JSON.stringify(keywords || []);
    }

    // 解析向量数据（支持数组和字符串）
    static parseEmbedding(embedding: number[] | string | undefined): number[] {
        if (!embedding) return [];
        if (Array.isArray(embedding)) return embedding;
        try {
            return JSON.parse(embedding);
        } catch {
            return [];
        }
    }

    // 序列化向量数据为JSON字符串
    static serializeEmbedding(embedding: number[]): string {
        return JSON.stringify(embedding || []);
    }
}

export default {
    KnowledgeBaseTypeMapper,
    KnowledgeBaseDataConverter
};

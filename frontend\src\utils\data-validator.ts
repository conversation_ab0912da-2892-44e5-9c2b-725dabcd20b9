// 数据验证工具
// 确保前后端数据的完整性和一致性

import {
    KnowledgeBase,
    KnowledgeBaseFile,
    KnowledgeSegment,
    KnowledgeBaseSettings,
    KnowledgeBaseType,
    KnowledgeBaseStatus,
    FileStatus,
    SegmentStrategy
} from '../types/knowledge-base';

// 验证结果接口
export interface ValidationResult {
    isValid: boolean;
    errors: string[];
    warnings: string[];
}

// 数据验证器类
export class DataValidator {

    // 验证知识库数据
    static validateKnowledgeBase(kb: any): ValidationResult {
        const result: ValidationResult = {
            isValid: true,
            errors: [],
            warnings: []
        };

        // 必填字段验证
        if (!kb.id || typeof kb.id !== 'string') {
            result.errors.push('知识库ID不能为空且必须为字符串');
        }

        if (!kb.name || typeof kb.name !== 'string') {
            result.errors.push('知识库名称不能为空且必须为字符串');
        }

        if (!kb.type || !this.isValidKnowledgeBaseType(kb.type)) {
            result.errors.push('知识库类型无效，必须为: text, table, image, document');
        }

        if (!kb.status || !this.isValidKnowledgeBaseStatus(kb.status)) {
            result.errors.push('知识库状态无效，必须为: processing, completed, failed');
        }

        // 数值字段验证
        if (kb.segmentCount !== undefined && (typeof kb.segmentCount !== 'number' || kb.segmentCount < 0)) {
            result.errors.push('分段数量必须为非负数');
        }

        // 时间字段验证
        if (kb.createdAt && !this.isValidDate(kb.createdAt)) {
            result.warnings.push('创建时间格式可能不正确');
        }

        if (kb.updatedAt && !this.isValidDate(kb.updatedAt)) {
            result.warnings.push('更新时间格式可能不正确');
        }

        // 关联数据验证
        if (kb.files && Array.isArray(kb.files)) {
            kb.files.forEach((file: any, index: number) => {
                const fileResult = this.validateKnowledgeBaseFile(file);
                if (!fileResult.isValid) {
                    result.errors.push(`文件[${index}]: ${fileResult.errors.join(', ')}`);
                }
                result.warnings.push(...fileResult.warnings.map(w => `文件[${index}]: ${w}`));
            });
        }

        if (kb.settings) {
            const settingsResult = this.validateKnowledgeBaseSettings(kb.settings);
            if (!settingsResult.isValid) {
                result.errors.push(`设置: ${settingsResult.errors.join(', ')}`);
            }
            result.warnings.push(...settingsResult.warnings.map(w => `设置: ${w}`));
        }

        result.isValid = result.errors.length === 0;
        return result;
    }

    // 验证知识库文件数据
    static validateKnowledgeBaseFile(file: any): ValidationResult {
        const result: ValidationResult = {
            isValid: true,
            errors: [],
            warnings: []
        };

        // 必填字段验证
        if (!file.id || typeof file.id !== 'string') {
            result.errors.push('文件ID不能为空且必须为字符串');
        }

        if (!file.knowledgeBaseId || typeof file.knowledgeBaseId !== 'string') {
            result.errors.push('知识库ID不能为空且必须为字符串');
        }

        if (!file.name || typeof file.name !== 'string') {
            result.errors.push('文件名不能为空且必须为字符串');
        }

        if (file.size === undefined || typeof file.size !== 'number' || file.size < 0) {
            result.errors.push('文件大小必须为非负数');
        }

        if (!file.status || !this.isValidFileStatus(file.status)) {
            result.errors.push('文件状态无效，必须为: processing, completed, failed');
        }

        // 进度验证
        if (file.progress !== undefined && (typeof file.progress !== 'number' || file.progress < 0 || file.progress > 100)) {
            result.errors.push('处理进度必须为0-100之间的数字');
        }

        // AI增强字段验证
        if (file.aiEnhanced !== undefined && typeof file.aiEnhanced !== 'boolean') {
            result.errors.push('AI增强标志必须为布尔值');
        }

        // 分段数据验证
        if (file.segments && Array.isArray(file.segments)) {
            file.segments.forEach((segment: any, index: number) => {
                const segmentResult = this.validateKnowledgeSegment(segment);
                if (!segmentResult.isValid) {
                    result.errors.push(`分段[${index}]: ${segmentResult.errors.join(', ')}`);
                }
                result.warnings.push(...segmentResult.warnings.map(w => `分段[${index}]: ${w}`));
            });
        }

        result.isValid = result.errors.length === 0;
        return result;
    }

    // 验证知识库分段数据
    static validateKnowledgeSegment(segment: any): ValidationResult {
        const result: ValidationResult = {
            isValid: true,
            errors: [],
            warnings: []
        };

        // 必填字段验证
        if (!segment.id || typeof segment.id !== 'string') {
            result.errors.push('分段ID不能为空且必须为字符串');
        }

        if (!segment.fileId || typeof segment.fileId !== 'string') {
            result.errors.push('文件ID不能为空且必须为字符串');
        }

        if (!segment.knowledgeBaseId || typeof segment.knowledgeBaseId !== 'string') {
            result.errors.push('知识库ID不能为空且必须为字符串');
        }

        if (!segment.content || typeof segment.content !== 'string') {
            result.errors.push('分段内容不能为空且必须为字符串');
        }

        // 数值字段验证
        if (segment.length === undefined || typeof segment.length !== 'number' || segment.length < 0) {
            result.errors.push('内容长度必须为非负数');
        }

        if (segment.position === undefined || typeof segment.position !== 'number' || segment.position < 0) {
            result.errors.push('位置必须为非负数');
        }

        // 关键词验证
        if (segment.keywords !== undefined) {
            if (!this.isValidKeywords(segment.keywords)) {
                result.warnings.push('关键词格式可能不正确，应为字符串数组或JSON字符串');
            }
        }

        // 向量数据验证
        if (segment.embedding !== undefined) {
            if (!this.isValidEmbedding(segment.embedding)) {
                result.warnings.push('向量数据格式可能不正确，应为数字数组或JSON字符串');
            }
        }

        result.isValid = result.errors.length === 0;
        return result;
    }

    // 验证知识库设置数据
    static validateKnowledgeBaseSettings(settings: any): ValidationResult {
        const result: ValidationResult = {
            isValid: true,
            errors: [],
            warnings: []
        };

        // 必填字段验证
        if (!settings.knowledgeBaseId || typeof settings.knowledgeBaseId !== 'string') {
            result.errors.push('知识库ID不能为空且必须为字符串');
        }

        // 布尔字段验证
        const booleanFields = ['parseText', 'parseTable', 'extractKeywords', 'generateSummary', 'useAI', 'fallbackToLocal'];
        booleanFields.forEach(field => {
            if (settings[field] !== undefined && typeof settings[field] !== 'boolean') {
                result.errors.push(`${field}必须为布尔值`);
            }
        });

        // 分段策略验证
        if (settings.segmentStrategy && !this.isValidSegmentStrategy(settings.segmentStrategy)) {
            result.errors.push('分段策略无效，必须为: auto, semantic, fixed, manual');
        }

        // 数值字段验证
        if (settings.segmentLength !== undefined && (typeof settings.segmentLength !== 'number' || settings.segmentLength <= 0)) {
            result.errors.push('分段长度必须为正数');
        }

        if (settings.segmentOverlap !== undefined && (typeof settings.segmentOverlap !== 'number' || settings.segmentOverlap < 0)) {
            result.errors.push('分段重叠长度必须为非负数');
        }

        // 逻辑验证
        if (settings.segmentLength && settings.segmentOverlap && settings.segmentOverlap >= settings.segmentLength) {
            result.warnings.push('分段重叠长度不应大于等于分段长度');
        }

        result.isValid = result.errors.length === 0;
        return result;
    }

    // 辅助验证方法
    private static isValidKnowledgeBaseType(type: any): type is KnowledgeBaseType {
        return ['text', 'table', 'image', 'document'].includes(type);
    }

    private static isValidKnowledgeBaseStatus(status: any): status is KnowledgeBaseStatus {
        return ['processing', 'completed', 'failed'].includes(status);
    }

    private static isValidFileStatus(status: any): status is FileStatus {
        return ['processing', 'completed', 'failed'].includes(status);
    }

    private static isValidSegmentStrategy(strategy: any): strategy is SegmentStrategy {
        return ['auto', 'semantic', 'fixed', 'manual'].includes(strategy);
    }

    private static isValidDate(date: any): boolean {
        if (typeof date === 'string') {
            return !isNaN(Date.parse(date));
        }
        if (date instanceof Date) {
            return !isNaN(date.getTime());
        }
        return false;
    }

    private static isValidKeywords(keywords: any): boolean {
        if (Array.isArray(keywords)) {
            return keywords.every(k => typeof k === 'string');
        }
        if (typeof keywords === 'string') {
            try {
                const parsed = JSON.parse(keywords);
                return Array.isArray(parsed) && parsed.every(k => typeof k === 'string');
            } catch {
                return false;
            }
        }
        return false;
    }

    private static isValidEmbedding(embedding: any): boolean {
        if (Array.isArray(embedding)) {
            return embedding.every(e => typeof e === 'number');
        }
        if (typeof embedding === 'string') {
            try {
                const parsed = JSON.parse(embedding);
                return Array.isArray(parsed) && parsed.every(e => typeof e === 'number');
            } catch {
                return false;
            }
        }
        return false;
    }
}

// 批量验证工具
export class BatchValidator {

    // 批量验证知识库列表
    static validateKnowledgeBaseList(kbList: any[]): ValidationResult {
        const result: ValidationResult = {
            isValid: true,
            errors: [],
            warnings: []
        };

        if (!Array.isArray(kbList)) {
            result.errors.push('知识库列表必须为数组');
            result.isValid = false;
            return result;
        }

        kbList.forEach((kb, index) => {
            const kbResult = DataValidator.validateKnowledgeBase(kb);
            if (!kbResult.isValid) {
                result.errors.push(`知识库[${index}]: ${kbResult.errors.join(', ')}`);
            }
            result.warnings.push(...kbResult.warnings.map(w => `知识库[${index}]: ${w}`));
        });

        result.isValid = result.errors.length === 0;
        return result;
    }

    // 验证API响应数据
    static validateApiResponse(response: any): ValidationResult {
        const result: ValidationResult = {
            isValid: true,
            errors: [],
            warnings: []
        };

        if (typeof response !== 'object' || response === null) {
            result.errors.push('API响应必须为对象');
            result.isValid = false;
            return result;
        }

        if (typeof response.code !== 'number') {
            result.errors.push('响应代码必须为数字');
        }

        if (typeof response.msg !== 'string') {
            result.errors.push('响应消息必须为字符串');
        }

        if (response.data === undefined) {
            result.warnings.push('响应数据为空');
        }

        result.isValid = result.errors.length === 0;
        return result;
    }
}

export default {
    DataValidator,
    BatchValidator
};

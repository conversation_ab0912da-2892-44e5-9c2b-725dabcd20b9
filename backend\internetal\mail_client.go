package internetal

import (
	"crypto/tls"
	"fmt"
	"net/smtp"
	"strings"
)

// MailClient 邮件客户端结构
type MailClient struct {
	SMTPServer string
	SMTPPort   string
	FromEmail  string
	AuthCode   string
}

// NewMailClient 创建新的邮件客户端
func NewMailClient(smtpServer, smtpPort, fromEmail, authCode string) *MailClient {
	return &MailClient{
		SMTPServer: smtpServer,
		SMTPPort:   smtpPort,
		FromEmail:  fromEmail,
		AuthCode:   authCode,
	}
}

// SendEmail 发送邮件
func (mc *MailClient) SendEmail(to []string, subject, body string) error {
	// 构建邮件消息
	message := buildEmailMessage(mc.FromEmail, to, subject, body)

	// 配置认证信息
	auth := smtp.PlainAuth("", mc.FromEmail, mc.AuthCode, mc.SMTPServer)

	// 连接到SMTP服务器并发送邮件
	address := fmt.Sprintf("%s:%s", mc.SMTPServer, mc.SMTPPort)

	// 使用TLS加密连接
	tlsConfig := &tls.Config{
		InsecureSkipVerify: true,
		ServerName:         mc.SMTPServer,
	}

	conn, err := tls.Dial("tcp", address, tlsConfig)
	if err != nil {
		return fmt.Errorf("连接服务器失败: %v", err)
	}
	defer conn.Close()

	client, err := smtp.NewClient(conn, mc.SMTPServer)
	if err != nil {
		return fmt.Errorf("创建客户端失败: %v", err)
	}
	defer client.Close()

	// 认证
	if err := client.Auth(auth); err != nil {
		return fmt.Errorf("认证失败: %v", err)
	}

	// 设置发件人
	if err := client.Mail(mc.FromEmail); err != nil {
		return fmt.Errorf("设置发件人失败: %v", err)
	}

	// 设置收件人
	for _, addr := range to {
		if err := client.Rcpt(addr); err != nil {
			return fmt.Errorf("设置收件人%s失败: %v", addr, err)
		}
	}

	// 发送邮件内容
	data, err := client.Data()
	if err != nil {
		return fmt.Errorf("获取数据写入流失败: %v", err)
	}
	defer data.Close()

	_, err = data.Write([]byte(strings.ReplaceAll(message, "\t", "")))
	if err != nil {
		return fmt.Errorf("写入邮件内容失败: %v", err)
	}

	return nil
}

// SendVerificationCode 发送验证码邮件
func (mc *MailClient) SendVerificationCode(toEmail, code string) error {
	subject := "密码重置验证码"
	body := fmt.Sprintf(`
	<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
		<h2 style="color: #333;">密码重置验证码</h2>
		<p style="color: #555;">您的验证码是：</p>
		<div style="font-size: 24px; font-weight: bold; color: #2196F3; margin: 15px 0;">%s</div>
		<p style="color: #555;">请在5分钟内使用此验证码完成注册。</p>
		<p style="color: #888; font-size: 12px;">如果您没有请求此验证码，请忽略此邮件。</p>
	</div>
	`, code)

	return mc.SendEmail([]string{toEmail}, subject, body)
}

// buildEmailMessage 构建邮件消息
func buildEmailMessage(from string, to []string, subject, body string) string {
	var message strings.Builder

	// 添加收件人
	message.WriteString(fmt.Sprintf("To: %s\r\n", strings.Join(to, ", ")))

	// 添加发件人
	message.WriteString(fmt.Sprintf("From: %s\r\n", from))

	// 添加主题
	message.WriteString(fmt.Sprintf("Subject: %s\r\n", subject))

	// 添加邮件格式信息
	message.WriteString("MIME-Version: 1.0\r\n")
	message.WriteString("Content-Type: text/html; charset=UTF-8\r\n\r\n")

	// 添加邮件内容
	message.WriteString(body)

	return message.String()
}

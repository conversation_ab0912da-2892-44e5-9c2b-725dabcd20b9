package internetal

import (
	"errors"
	"fmt"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// ResetPassword 重置用户密码
func (s *UserService) ResetPassword(email, newPassword string) error {
	var user User
	result := s.DB.Where("email = ?", email).First(&user)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return errors.New("用户不存在")
		}
		return result.Error
	}

	// 密码加密 - 使用推荐的成本参数(12)而不是默认值，提供更好的安全性
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), 12)
	if err != nil {
		return fmt.Errorf("密码加密失败: %w", err)
	}

	// 更新密码
	user.Password = string(hashedPassword)
	if err := s.DB.Save(&user).Error; err != nil {
		return fmt.Errorf("密码更新失败: %v", err)
	}

	return nil
}

// UserService 用户服务
type UserService struct {
	DB *gorm.DB
}

// NewUserService 创建用户服务实例
func NewUserService(db *gorm.DB) *UserService {
	return &UserService{DB: db}
}

// Login 用户登录
type LoginRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

// 短信登录功能已调整为使用邮箱验证码
type SmsLoginRequest struct {
	Email string `json:"email"`
	Code  string `json:"code"`
}

// Login 验证用户登录信息
func (s *UserService) Login(req LoginRequest) (*User, error) {
	var user User
	result := s.DB.Where("username = ?", req.Username).First(&user)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		return nil, result.Error
	}

	// 验证密码
	err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password))
	if err != nil {
		if err == bcrypt.ErrMismatchedHashAndPassword {
			return nil, errors.New("密码错误")
		}
		return nil, fmt.Errorf("密码验证失败: %w", err)
	}

	return &user, nil
}

// 使用邮箱验证码
func (s *UserService) SmsLogin(req SmsLoginRequest) (*User, error) {
	var user User
	result := s.DB.Where("email = ?", req.Email).First(&user)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		return nil, result.Error
	}

	return &user, nil
}

// Register 用户注册
func (s *UserService) Register(user *User) error {
	// 1. 检查邮箱是否已注册（使用Unscoped()包含软删除记录）
	if err := s.DB.Unscoped().Where("email = ?", user.Email).First(&User{}).Error; err == nil {
		return errors.New("邮箱已注册")
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		return fmt.Errorf("数据库查询错误: %v", err)
	}

	// 2. 密码加密 - 使用推荐的成本参数(12)而不是默认值，提供更好的安全性
	// 参考: https://pkg.go.dev/golang.org/x/crypto/bcrypt
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(user.Password), 12)
	if err != nil {
		return fmt.Errorf("密码加密失败: %w", err) // 使用%w包装错误，便于上层处理
	}
	user.Password = string(hashedPassword)

	// 3. 创建用户（GORM会自动处理时间戳）
	if err := s.DB.Create(user).Error; err != nil {
		return fmt.Errorf("用户创建失败: %v", err)
	}

	return nil
}

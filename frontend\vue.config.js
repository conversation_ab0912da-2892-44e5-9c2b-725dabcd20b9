const { defineConfig } = require('@vue/cli-service')
module.exports = defineConfig({
  transpileDependencies: true,

  // ✅ 加上这句，确保资源路径是相对的（部署到后端不会 404）
  publicPath: './',

  devServer: {
    port: 3000,
    proxy:{
      '/api':{
        target: 'http://127.0.0.1:8080',
        changeOrigin: true,
        PathRewrite: {
          '^/api':'/api'
        }
      }
    }
  },

  chainWebpack: config => {
    config.plugin('define').tap(definitions => {
      Object.assign(definitions[0], {
        __VUE_OPTIONS_API__: 'true',
        __VUE_PROD_DEVTOOLS__: 'false',
        __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: 'false'
      })
      return definitions
    })
  }
})

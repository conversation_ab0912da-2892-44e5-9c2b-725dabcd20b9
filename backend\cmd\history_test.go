package main

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// 初始化 Gin 引擎（只挂载我们要测试的接口）
func setupRouter() *gin.Engine {
	r := gin.Default()

	r.POST("/api/history/save", SaveConversationHistory)
	r.POST("/api/history/list", GetHistoryList)
	r.POST("/api/history/messages", GetHistoryMessages)
	r.POST("/api/history/delete", DeleteHistory)

	return r
}

// 测试保存对话记录
func TestSaveConversationHistory(t *testing.T) {
	router := setupRouter()

	payload := map[string]interface{}{
		"assistant_id":   1,
		"assistant_name": "测试助手",
		"messages": []map[string]interface{}{
			{
				"content":     "你好呀",
				"isUser":      true,
				"messageType": "text",
			},
			{
				"content":     "你好，有什么可以帮您？",
				"isUser":      false,
				"messageType": "text",
			},
		},
	}

	body, _ := json.Marshal(payload)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/api/history/save", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")

	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var res map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &res)
	assert.Equal(t, float64(200), res["code"])
	assert.NotNil(t, res["data"])
}

// 你也可以分别测试 list、messages、delete 接口
// 示例：TestGetHistoryList、TestGetHistoryMessages、TestDeleteHistory

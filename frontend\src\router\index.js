import { createRouter, createWebHistory } from 'vue-router'
import App from '../App.vue'
import Login from '../components/Login.vue'
import Register from '../components/Register.vue'
import ResetPassword from '../components/ResetPassword.vue'

const routes = [
  {
    path: '/',
    name: 'App',
    component: App
  },
  {
    path: '/login',
    name: 'Login',
    component: Login
  },
  {
    path: '/register',
    name: 'Register',
    component: Register
  },
  {
    path: '/reset-password',
    name: 'ResetPassword',
    component: ResetPassword
  }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})

// 添加导航守卫，用于处理登录状态
router.beforeEach((to, from, next) => {
  // 检查是否存在登录状态
  const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true'

  // 允许所有用户访问登录、注册和重置密码页面
  if (to.name === 'Login' || to.name === 'Register' || to.name === 'ResetPassword') {
    next()
    return
  }

  // 对于应用主页和其他页面，需要登录
  if (isLoggedIn) {
    next()
  } else {
    // 如果未登录，重定向到登录页面
    next({ name: 'Login' })
  }
})

export default router
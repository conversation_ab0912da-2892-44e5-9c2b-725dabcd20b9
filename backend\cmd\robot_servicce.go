package main

import (
	"backend/internetal"
	"errors"
	"time"

	"gorm.io/gorm"
)

func CreateRobotService(robot internetal.Robot) (internetal.Robot, error) {
	if robot.PersonnelDesign == "" {
		robot.PersonnelDesign = "你是一个友善、专业的AI助手。"
	}
	if robot.ReplyLogic == "" {
		robot.ReplyLogic = `{"systemRole":"你是一个专业的AI助手","dialogCommand":"请以简洁、专业的方式回答用户的问题","responseMode":"simple","temperature":0.6,"maxTokens":50,"language":"zh-cn","speaker":"601002","speechSpeed":1.0}`
	}
	if robot.KnowledgeConfig == "" {
		robot.KnowledgeConfig = `{"callMethod":"auto","searchStrategy":"mixed","maxRecall":5,"minScore":0.50,"queryRewrite":true,"resultRerank":true}`
	}
	robot.CreateTime = time.Now()
	robot.UpdateTime = time.Now()
	db := GetDB()
	result := db.Create(&robot)
	if result.Error != nil {
		return internetal.Robot{}, result.Error
	}
	return robot, nil
}

func DeleteRobotService(id int) error {
	result := SqlSession.Delete(&internetal.Robot{}, id)
	return result.Error
}

func FindByIdService(id int) (internetal.Robot, error) {
	var robot internetal.Robot
	result := SqlSession.First(&robot, id)
	return robot, result.Error
}

func FindByNameService(name string) (internetal.Robot, error) {
	var robot internetal.Robot
	result := SqlSession.Where("name = ?", name).First(&robot)
	return robot, result.Error
}

func UpdateRobotService(robot internetal.Robot) error {
	var findAssistant internetal.Robot
	result := SqlSession.First(&findAssistant, robot.ID)
	if result.Error != nil {
		return result.Error
	}
	if robot.Name == "" {
		robot.Name = findAssistant.Name
	}
	if robot.Description == "" {
		robot.Description = findAssistant.Description
	}
	if robot.PersonnelDesign == "" {
		robot.PersonnelDesign = findAssistant.PersonnelDesign
	}
	if robot.ReplyLogic == "" {
		robot.ReplyLogic = findAssistant.ReplyLogic
	}
	if robot.KnowledgeConfig == "" {
		robot.KnowledgeConfig = findAssistant.KnowledgeConfig
	}
	if robot.SelectedKnowledgeBases == "" {
		robot.SelectedKnowledgeBases = findAssistant.SelectedKnowledgeBases
	}
	robot.UpdateTime = time.Now()
	return SqlSession.Model(&findAssistant).Updates(robot).Error
}

func ListRobotsService(userID uint) ([]internetal.Robot, error) {
	var robots []internetal.Robot

	// 从数据库查询当前用户的所有机器人
	result := SqlSession.Where("user_id = ?", userID).Find(&robots)
	if result.Error != nil {
		return nil, result.Error
	}

	// 从数据库查询系统默认机器人(名称为"默认助手"且user_id为0)
	var defaultRobot internetal.Robot
	defaultResult := SqlSession.Where("name = ? AND user_id = 0", "默认助手").First(&defaultRobot)
	if defaultResult.Error == nil {
		// 检查默认机器人是否已在列表中
		isDuplicate := false
		for _, robot := range robots {
			if robot.ID == defaultRobot.ID {
				isDuplicate = true
				break
			}
		}
		if !isDuplicate {
			// 创建defaultRobot的副本，避免潜在的引用问题
			newDefaultRobot := defaultRobot
			robots = append(robots, newDefaultRobot)
		}
	} else if !errors.Is(defaultResult.Error, gorm.ErrRecordNotFound) {
		return nil, defaultResult.Error
	}

	// 如果数据库中没有机器人，创建一个默认的
	if len(robots) == 0 {
		defaultRobot := internetal.Robot{
			Name:                   "默认助手",
			Description:            "默认AI助手",
			PersonnelDesign:        "你是一个友善、专业的AI助手。",
			ReplyLogic:             `{"systemRole":"你是一个专业的AI助手","dialogCommand":"请以简洁、专业的方式回答用户的问题","responseMode":"simple","temperature":0.6,"maxTokens":50,"language":"zh-cn","speaker":"601002","speechSpeed":1.0}`,
			KnowledgeConfig:        `{"callMethod":"auto","searchStrategy":"mixed","maxRecall":5,"minScore":0.50,"queryRewrite":true,"resultRerank":true}`,
			SelectedKnowledgeBases: `{"text":[],"table":[],"image":[]}`,
			CreateTime:             time.Now(),
			UpdateTime:             time.Now(),
		}

		// 保存默认机器人到数据库
		createResult := SqlSession.Create(&defaultRobot)
		if createResult.Error != nil {
			return nil, createResult.Error
		}

		robots = append(robots, defaultRobot)
	}

	return robots, nil
}

// 根据电话号码查询机器人
func FindByRobotIdService(id int) (internetal.Robot, error) {
	var robot internetal.Robot
	result := SqlSession.Where("id = ?", id).First(&robot)
	return robot, result.Error
}

// 检查机器人是否是系统默认机器人
func IsDefaultRobot(robot internetal.Robot) bool {
	return robot.Name == "默认助手" && robot.UserID == 0
}

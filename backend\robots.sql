create database if not exists open_id;

use open_id;

-- 创建users表
CREATE TABLE IF NOT EXISTS users (
                                     `id` INT NOT NULL AUTO_INCREMENT,
                                     `username` VARCHAR ( 50 ) NOT NULL UNIQUE,
    `password` VARCHAR ( 100 ) NOT NULL,
    `phone` VARCHAR ( 20 ),
    `email` VARCHAR ( 100 ),
    `created_at` DATETIME NOT NULL,
    `updated_at` DATETIME NOT NULL ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY ( `id` ) USING BTREE
    );

-- 创建robots表
CREATE TABLE IF NOT EXISTS robots (
                        `id` INT NOT NULL AUTO_INCREMENT,
                        `name` VARCHAR ( 255 ) NOT NULL,
                        `description` VARCHAR ( 255 ) DEFAULT NULL,
                        `personnel_design` VARCHAR ( 255 ) DEFAULT NULL,
                        `create_time` DATETIME ( 6 ) NOT NULL ON UPDATE CURRENT_TIMESTAMP ( 6 ),
                        `update_time` DATETIME ( 6 ) NOT NULL,
                        PRIMARY KEY ( `id` ) USING BTREE
);

-- 创建assistants表
CREATE TABLE IF NOT EXISTS assistants (
                                     `id` INT NOT NULL AUTO_INCREMENT,
                                     `name` VARCHAR ( 100 ) NOT NULL,
                                     `description` TEXT,
                                     `model` VARCHAR ( 50 ) NOT NULL,
                                     `tools` TEXT,
                                     `created_at` DATETIME NOT NULL,
                                     `updated_at` DATETIME NOT NULL ON UPDATE CURRENT_TIMESTAMP,
                                     PRIMARY KEY ( `id` ) USING BTREE
);

-- 创建conversation_history表
CREATE TABLE IF NOT EXISTS conversation_history (
                                                    `id` INT NOT NULL AUTO_INCREMENT,
                                                    `user_id` INT NOT NULL,
                                                    `assistant_id` INT NOT NULL,
                                                    `title` VARCHAR ( 255 ) NOT NULL,
                                                    `created_at` DATETIME NOT NULL,
                                                    `updated_at` DATETIME NOT NULL ON UPDATE CURRENT_TIMESTAMP,
                                                    PRIMARY KEY ( `id` ) USING BTREE,
                                                    INDEX `idx_user_id` (`user_id`),
                                                    INDEX `idx_assistant_id` (`assistant_id`)
);

-- 创建conversation_messages表
CREATE TABLE IF NOT EXISTS conversation_messages (
                                                   `id` INT NOT NULL AUTO_INCREMENT,
                                                   `conversation_id` INT NOT NULL,
                                                   `role` VARCHAR ( 20 ) NOT NULL,
                                                   `content` TEXT NOT NULL,
                                                   `created_at` DATETIME NOT NULL,
                                                   PRIMARY KEY ( `id` ) USING BTREE,
                                                   INDEX `idx_conversation_id` (`conversation_id`)
);
{"name": "call", "version": "0.1.0", "private": true, "scripts": {"serve": "npx vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"core-js": "^3.8.3", "mammoth": "^1.9.1", "markdown-it": "^13.0.2", "vue": "^3.5.18", "vue-router": "^4.5.1", "webpack": "^5.101.0"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/eslint-parser": "^7.12.16", "@babel/plugin-transform-private-methods": "^7.27.1", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/compiler-sfc": "^3.5.18", "autoprefixer": "^10.4.21", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "vue-loader": "^17.4.2"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}
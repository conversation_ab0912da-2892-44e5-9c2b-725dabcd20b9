<template>
  <div class="login-container">
    <!-- 登录表单区域 -->
    <div class="login-form">
      <div class="form-header">
        <h2 class="form-title">欢迎登录</h2>
        <p class="form-subtitle">语音模型系统</p>
      </div>
      <!-- 登录方式切换标签 -->
      <div class="form-tabs">
        <label
          class="tab"
          :class="{ 'tab-active': isPasswordLogin }"
          @click="isPasswordLogin = true"
        >
          密码登录
        </label>
        <label
          class="tab"
          :class="{ 'tab-active': !isPasswordLogin }"
          @click="isPasswordLogin = false"
        >
          短信登录
        </label>
      </div>
      <!-- 密码登录表单 -->
      <div v-if="isPasswordLogin" class="form-content">
        <div class="form-group">
          <label class="form-label" for="username">用户名</label>
          <input
            id="username"
            type="text"
            placeholder="AD13632425342"
            class="form-input"
            v-model="username"
            @keyup.enter="handleLogin"
          />
        </div>
        <div class="form-group">
          <label class="form-label" for="password">密码</label>
          <input
            id="password"
            type="password"
            placeholder="请输入密码"
            class="form-input"
            v-model="password"
            @keyup.enter="handleLogin"
          />
        </div>
        <div class="checkbox-group flex justify-between items-center mb-6">
          <label class="flex items-center">
            <input type="checkbox" class="mr-2" v-model="smsLoginCheck" />
            <span class="text-gray-700">记住密码</span>
          </label>
        </div>
        <div class="text-right mb-4">
          <a href="javascript:void(0)" class="forgot-password text-blue-600 hover:text-blue-800 transition-colors" @click="goToForgotPassword">忘记密码？</a>
        </div>
        <button
            class="form-button"
            :class="{ 'opacity-70 cursor-not-allowed': isLoading || !username.trim() || !password }"
            @click="handleLogin"
            :disabled="isLoading || !username.trim() || !password"
          >
            {{ isLoading ? "登录中..." : "登录" }}
          </button>
        <div class="register-link mt-4 text-center">
          <span>还没有账号？</span>
          <button
            @click="goToRegister"
            class="text-blue-500 hover:text-blue-700 transition-colors duration-200 ml-1 bg-transparent border-none p-0"
          >
            立即注册
          </button>
        </div>
      </div>
      <!-- 短信登录表单 -->
      <div v-else class="form-content">
        <div class="form-group">
          <label class="form-label" for="smsUsername">邮箱</label>
          <input
            id="smsUsername"
            type="text"
            placeholder="<EMAIL>"

            class="form-input"
            v-model="username"
            @keyup.enter="handleSmsLogin"
          />
        </div>
        <div class="form-group">
          <label class="form-label" for="smsCode">邮箱验证码</label>
          <div class="sms-code-container">
            <input
              id="smsCode"
              type="text"
              placeholder="请输入验证码"
              class="form-input sms-code-input"
              v-model="smsCode"
              @keyup.enter="handleSmsLogin"
            />
            <button
              class="sms-code-button"
              @click="sendSmsCode"
              :disabled="countdownTime > 0"
            >
              {{
                countdownTime > 0
                  ? `${countdownTime}秒后重新获取`
                  : "获取验证码"
              }}
            </button>
          </div>
        </div>
        <button
          class="form-button"
          :class="{ 'opacity-70 cursor-not-allowed': isLoading }"
          @click="handleSmsLogin"
          :disabled="isLoading"
        >
          {{ isLoading ? "登录中..." : "登录" }}
        </button>
        <div class="register-link mt-4 text-center">
          <span>还没有账号？</span>
          <button
            @click="goToRegister"
            class="text-blue-500 hover:text-blue-700 transition-colors duration-200 ml-1 bg-transparent border-none p-0"
          >
            立即注册
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "LoginForm",
  data() {
    return {
      isPasswordLogin: true, // 控制登录方式，true为密码登录，false为短信登录
      username: "", // 用户名
      password: "", // 密码
      smsCode: "", // 短信验证码
      smsLoginCheck: false, // 密码登录时的短信登录复选框状态
      countdownTime: 0, // 验证码倒计时时间
      isLoading: false, // 登录按钮加载状态
    };
  },
  methods: {
    // 跳转到注册页面
    goToRegister() {
      this.$router.push("/register");
    },
    // 跳转到忘记密码页面
    goToForgotPassword() {
      this.$router.push({ name: 'ResetPassword' });
    },
    handleLogin() {
      // 表单校验
      if (!this.username.trim()) {
        alert("请输入用户名");
        return;
      }
      // 用户名格式校验：至少3位字符
      if (this.username.length < 3) {
        alert("用户名长度至少为3位");
        return;
      }
      if (!this.password) {
        alert("请输入密码");
        return;
      }
      // 密码格式校验：至少6位
      if (this.password.length < 6) {
        alert("密码长度至少为6位");
        return;
      }

      // 密码登录逻辑
      console.log("密码登录：", this.username, this.password);

      // 显示加载状态
      this.isLoading = true;

      // 使用API进行密码登录
      fetch("http://localhost:8080/api/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          username: this.username,
          password: this.password,
        }),
      })
        .then((response) => {
          if (!response.ok) {
            return response.json().then((errData) => {
              throw new Error(errData.error || "登录失败，请检查用户名和密码");
            });
          }
          return response.json();
        })
        .then((data) => {
          // 登录成功，存储token和登录状态
          localStorage.setItem("token", data.token);
          localStorage.setItem("userInfo", JSON.stringify(data.user));
          localStorage.setItem("isLoggedIn", "true");

          // 提示成功
          alert("登录成功");

          // 跳转到应用页面
          this.$router.push({ name: 'App' });
        })
        .catch((error) => {
          // 登录失败
          alert(error.message);
          console.error("登录错误:", error);
        })
        .finally(() => {
          // 隐藏加载状态
          this.isLoading = false;
        });
    },
    sendSmsCode() {
      if (this.countdownTime > 0) return;

      // 验证邮箱格式
      if (!this.username.trim()) {
        alert("请输入邮箱");
        return;
      }
      // 验证邮箱格式
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      if (!emailRegex.test(this.username.trim())) {
        alert("请输入有效的邮箱地址");
        return;
      }

      // 显示加载状态
      this.isLoading = true;

      // 向后端发送获取验证码的请求
      fetch("http://localhost:8080/api/send-captcha", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: this.username.trim(),
        }),
      })
        .then((response) => {
          if (!response.ok) {
            return response.json().then((errData) => {
              throw new Error(errData.error || "发送验证码失败，请重试");
            });
          }
          return response.json();
        })
        .then((data) => {
          // 发送成功
          alert("验证码发送成功，请查收");
          console.log("验证码发送成功:", data);

          // 设置倒计时60秒
          this.countdownTime = 60;
          const timer = setInterval(() => {
            this.countdownTime--;
            if (this.countdownTime <= 0) {
              clearInterval(timer);
            }
          }, 1000);
        })
        .catch((error) => {
          // 发送失败
          // 显示更具体的错误信息
          alert("发送验证码失败: " + error.message);
          console.error("发送验证码错误:", error);
        })
        .finally(() => {
          // 隐藏加载状态
          this.isLoading = false;
        });
    },
    handleSmsLogin() {
      // 表单校验
      if (!this.username.trim()) {
        alert("请输入用户名");
        return;
      }
      if (!this.smsCode.trim()) {
        alert("请输入验证码");
        return;
      }
      // 验证码格式校验：6位数字
      if (!/^\d{6}$/.test(this.smsCode.trim())) {
        alert("验证码必须是6位数字");
        return;
      }

      // 邮箱验证码登录逻辑
      console.log("邮箱验证码登录：", this.username, this.smsCode);

      // 显示加载状态
      this.isLoading = true;

      // 发送登录请求
      // 使用API进行短信登录
      fetch("http://localhost:8080/api/smsLogin", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: this.username,
          code: this.smsCode,
        }),
      })
        .then((response) => {
          if (!response.ok) {
            return response.json().then((errData) => {
              throw new Error(
                errData.error || "登录失败，请检查用户名和验证码"
              );
            });
          }
          return response.json();
        })
        .then((data) => {
          // 登录成功，存储token和登录状态
          localStorage.setItem("token", data.token);
          localStorage.setItem("userInfo", JSON.stringify(data.user));
          localStorage.setItem("isLoggedIn", "true");

          // 提示成功
          alert("登录成功");

          // 跳转到应用页面
          this.$router.push({ name: 'App' });
        })
        .catch((error) => {
          // 登录失败
          alert(error.message);
          console.error("登录错误:", error);
        })
        .finally(() => {
          // 隐藏加载状态
          this.isLoading = false;
        });
    },
  },
};
</script>

<style scoped lang="postcss">
.login-container {
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-image: url('@/assets/Login .png');
  background-size: cover;
  background-position: center;
}
.login-form {
  @apply w-full max-w-md bg-white rounded-xl shadow-2xl overflow-hidden transform transition-all duration-300 hover:shadow-xl;
}

.form-header {
  @apply bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6;
}

.form-title {
  @apply text-2xl font-bold mb-1;
}

.form-subtitle {
  @apply text-blue-100;
}

.form-tabs {
  @apply flex border-b border-gray-200;
}

.tab {
  @apply flex-1 py-3 px-4 text-center cursor-pointer transition-all duration-200;
}

.tab-active {
  @apply text-blue-600 border-b-2 border-blue-500 font-medium;
}

.form-content {
  @apply p-6;
}

.form-group {
  @apply mb-4;
}

.form-label {
  @apply block text-gray-700 mb-2 font-medium;
}

.form-input {
  @apply w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200;
  font-family: "SimHei", "Microsoft YaHei", "Heiti TC", sans-serif;
}

.checkbox-group {
  @apply flex items-center justify-between text-sm;
}

.form-button {
  @apply w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50;
}

.form-button:disabled {
  @apply opacity-70 cursor-not-allowed hover:scale-100;
}

.forgot-password {
  @apply text-blue-500 hover:text-blue-700 transition-colors duration-200;
}

.sms-code-container {
  @apply flex space-x-2;
}

.sms-code-input {
  @apply flex-1;
}

.sms-code-button {
  @apply bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg transition-all duration-200;
}

.register-link {
  @apply text-gray-600;
}

.sms-code-button:disabled {
  @apply opacity-70 cursor-not-allowed bg-gray-400 hover:bg-gray-400;
}
</style>

package main

import (
	"backend/internetal/utils"
	"bufio"
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type Tool struct {
	Type     string                   `json:"type"`
	Function utils.FunctionDefinition `json:"function"`
}

// 修正请求体结构，与前端发送的格式匹配
type TextSendRequest struct {
	UserID    string        `json:"user_id"`   // 对应前端的user_id
	Message   utils.Message `json:"message"`   // 对应前端的message
	Streaming bool          `json:"streaming"` // 保持原有流式参数
}

// 统一响应结构
type ApiResponse struct {
	Code        int            `json:"code"`
	Data        string         `json:"data"`
	Msg         string         `json:"msg"`
	Stats       *ResponseStats `json:"stats,omitempty"`
	Suggestions []string       `json:"suggestions,omitempty"`
}

// 响应统计信息
type ResponseStats struct {
	ResponseTime int `json:"responseTime"` // 响应时间(毫秒)
	InputTokens  int `json:"inputTokens"`  // 输入Token数
	OutputTokens int `json:"outputTokens"` // 输出Token数
	TotalTokens  int `json:"totalTokens"`  // 总Token数
}

type RobotBody struct {
	Model    string          `json:"model"`
	Messages []utils.Message `json:"messages"`
	Tools    []Tool          `json:"tools"`
	Stream   bool            `json:"stream"`
}

type Delta struct {
	Content string `json:"content"`
}

type Choice struct {
	Delta        Delta         `json:"delta"`
	Message      utils.Message `json:"message"`
	FinishReason string        `json:"finish_reason"`
	Index        int           `json:"index"`
}

type Response struct {
	Choices []Choice `json:"choices"`
}

var apiKey string = "sk-488d6fd9be04416ca82063d51071ad57"
var url1 string = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
var modelName string = "qwen-plus"
var sessionStore = utils.GetSessionStore()

func LlmInteraction(c *gin.Context) {
	startTime := time.Now() // 开始计时
	client := &http.Client{}
	var fullContent string
	var req TextSendRequest // 使用修正后的请求体结构
	var questions utils.Message
	var messages []utils.Message
	var streaming bool

	// 1. 解析前端请求体（修正：使用匹配的结构体）
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, ApiResponse{
			Code: 400,
			Msg:  "请求体解析失败: " + err.Error(),
			Data: "",
		})
		return
	}

	// 2. 提取请求参数（修正：使用新结构体的字段）
	streaming = req.Streaming
	questions = req.Message
	if questions.Content == "" {
		c.JSON(http.StatusBadRequest, ApiResponse{
			Code: 400,
			Msg:  "消息内容不能为空",
			Data: "",
		})
		return
	}

	// 3. 处理会话存储（修正：使用user_id作为会话标识）
	store := sessionStore
	if store == nil {
		c.JSON(http.StatusInternalServerError, ApiResponse{
			Code: 500,
			Msg:  "会话存储初始化失败",
			Data: "",
		})
		return
	}
	messagesStore := store.GetMessageStore(req.UserID)
	messages = messagesStore.MessageStore
	messages = append(messages, questions)
	store.AddMessage(req.UserID, questions)

	// 4. 处理system类型消息（保持原有逻辑，优化响应格式）
	if questions.Role == "system" {
		c.JSON(http.StatusOK, ApiResponse{
			Code: 200,
			Msg:  "设置成功",
			Data: "",
		})
		return
	}

	// 5. 定义天气工具（保持原有逻辑）
	weatherFunction := Tool{
		Type: "function",
		Function: utils.FunctionDefinition{
			Name:        "get_weather",
			Description: "获取指定城市的天气信息",
			Parameters: json.RawMessage([]byte(`{
              "type": "object",
              "properties": {
              "city": {
                 "type": "string",
                 "description": "要查询天气的城市名称"
                  }
             },
                  "required": ["city"]
            }`)),
		},
	}

	// 6. 尝试知识库搜索（如果失败则继续AI对话）
	knowledgeBaseResults := []KnowledgeSearchResult{} // 初始化为空

	// 尝试搜索知识库，但不让错误阻止正常对话
	func() {
		defer func() {
			if r := recover(); r != nil {
				log.Printf("知识库搜索发生panic: %v", r)
			}
		}()

		results := searchKnowledgeBaseContent(questions.Content, []string{}, 5, 0.3)
		if len(results) > 0 {
			knowledgeBaseResults = results
		}
	}()

	// 如果知识库有相关内容，直接返回
	if len(knowledgeBaseResults) > 0 {
		// 构建基于知识库的回复
		knowledgeResponse := buildKnowledgeBaseResponse(questions.Content, knowledgeBaseResults)

		// 保存机器人回复
		answerBody := utils.Message{
			Role:    "assistant",
			Content: knowledgeResponse,
		}
		store.AddMessage(req.UserID, answerBody)

		// 计算响应时间和Token统计
		responseTime := int(time.Since(startTime).Milliseconds())
		inputTokens := estimateTokens(questions.Content)
		outputTokens := estimateTokens(knowledgeResponse)

		// 生成相关提问建议
		suggestions := generateSuggestions(questions.Content, knowledgeBaseResults)

		c.JSON(http.StatusOK, ApiResponse{
			Code: 200,
			Msg:  "success (knowledge base)",
			Data: knowledgeResponse,
			Stats: &ResponseStats{
				ResponseTime: responseTime,
				InputTokens:  inputTokens,
				OutputTokens: outputTokens,
				TotalTokens:  inputTokens + outputTokens,
			},
			Suggestions: suggestions,
		})
		return
	}

	// 7. 如果知识库没有相关内容，构建AI请求体
	requestBody := RobotBody{
		Model:    modelName,
		Messages: messages,
		Tools:    []Tool{weatherFunction},
		Stream:   streaming,
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		c.JSON(http.StatusBadRequest, ApiResponse{
			Code: 400,
			Msg:  "请求体序列化失败: " + err.Error(),
			Data: "",
		})
		return
	}

	// 7. 调用第三方AI接口（增加响应状态检查和回退机制）
	reqAI, err := http.NewRequest("POST", url1, bytes.NewBuffer(jsonData))
	if err != nil {
		// 回退到简单回复
		fallbackResponse := generateFallbackResponse(questions.Content)
		answerBody := utils.Message{
			Role:    "assistant",
			Content: fallbackResponse,
		}
		store.AddMessage(req.UserID, answerBody)

		// 计算响应时间和Token统计
		responseTime := int(time.Since(startTime).Milliseconds())
		inputTokens := estimateTokens(questions.Content)
		outputTokens := estimateTokens(fallbackResponse)
		suggestions := generateSuggestions(questions.Content, []KnowledgeSearchResult{})

		c.JSON(http.StatusOK, ApiResponse{
			Code: 200,
			Msg:  "success (fallback)",
			Data: fallbackResponse,
			Stats: &ResponseStats{
				ResponseTime: responseTime,
				InputTokens:  inputTokens,
				OutputTokens: outputTokens,
				TotalTokens:  inputTokens + outputTokens,
			},
			Suggestions: suggestions,
		})
		return
	}

	reqAI.Header.Set("Authorization", "Bearer "+apiKey)
	reqAI.Header.Set("Content-Type", "application/json")

	resp, err := client.Do(reqAI)
	if err != nil {
		// 回退到简单回复
		fallbackResponse := generateFallbackResponse(questions.Content)
		answerBody := utils.Message{
			Role:    "assistant",
			Content: fallbackResponse,
		}
		store.AddMessage(req.UserID, answerBody)

		// 计算响应时间和Token统计
		responseTime := int(time.Since(startTime).Milliseconds())
		inputTokens := estimateTokens(questions.Content)
		outputTokens := estimateTokens(fallbackResponse)
		suggestions := generateSuggestions(questions.Content, []KnowledgeSearchResult{})

		c.JSON(http.StatusOK, ApiResponse{
			Code: 200,
			Msg:  "success (fallback)",
			Data: fallbackResponse,
			Stats: &ResponseStats{
				ResponseTime: responseTime,
				InputTokens:  inputTokens,
				OutputTokens: outputTokens,
				TotalTokens:  inputTokens + outputTokens,
			},
			Suggestions: suggestions,
		})
		return
	}
	defer resp.Body.Close()

	// 关键修复：检查第三方API响应状态
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		log.Printf("AI服务返回错误 (状态码: %d): %s", resp.StatusCode, string(body))

		// 回退到简单回复
		fallbackResponse := generateFallbackResponse(questions.Content)
		answerBody := utils.Message{
			Role:    "assistant",
			Content: fallbackResponse,
		}
		store.AddMessage(req.UserID, answerBody)

		// 计算响应时间和Token统计
		responseTime := int(time.Since(startTime).Milliseconds())
		inputTokens := estimateTokens(questions.Content)
		outputTokens := estimateTokens(fallbackResponse)
		suggestions := generateSuggestions(questions.Content, []KnowledgeSearchResult{})

		c.JSON(http.StatusOK, ApiResponse{
			Code: 200,
			Msg:  "success (fallback)",
			Data: fallbackResponse,
			Stats: &ResponseStats{
				ResponseTime: responseTime,
				InputTokens:  inputTokens,
				OutputTokens: outputTokens,
				TotalTokens:  inputTokens + outputTokens,
			},
			Suggestions: suggestions,
		})
		return
	}

	// 8. 处理流式响应（保持原有逻辑，优化响应头）
	if streaming {
		c.Header("Content-Type", "text/event-stream")
		c.Header("Cache-Control", "no-cache")
		c.Header("Connection", "keep-alive")
		c.Writer.WriteHeader(http.StatusOK)

		scanner := bufio.NewScanner(resp.Body)
		for scanner.Scan() {
			line := scanner.Text()
			if strings.HasPrefix(line, "data:") {
				data := strings.TrimSpace(line[5:])
				if data == "DONE" {
					break
				}
				var chunk Response
				if err := json.Unmarshal([]byte(data), &chunk); err != nil {
					continue // 忽略解析失败的chunk
				}
				if len(chunk.Choices) > 0 {
					content := chunk.Choices[0].Delta.Content
					if content != "" {
						c.Writer.WriteString(content + "\n")
						c.Writer.Flush()
						fullContent += content
					}
				}
			}
		}
	} else {
		// 9. 处理非流式响应（增加索引越界检查）
		bodyText, err := io.ReadAll(resp.Body)
		if err != nil {
			c.JSON(http.StatusInternalServerError, ApiResponse{
				Code: 500,
				Msg:  "读取AI响应失败: " + err.Error(),
				Data: "",
			})
			return
		}

		var res Response
		if err := json.Unmarshal(bodyText, &res); err != nil {
			c.JSON(http.StatusInternalServerError, ApiResponse{
				Code: 500,
				Msg:  "解析AI响应失败: " + err.Error(),
				Data: "",
			})
			return
		}

		// 关键修复：检查Choices长度，避免索引越界
		if len(res.Choices) == 0 {
			c.JSON(http.StatusInternalServerError, ApiResponse{
				Code: 500,
				Msg:  "AI响应内容为空",
				Data: "",
			})
			return
		}

		// 处理工具调用（保持原有逻辑）
		if len(res.Choices[0].Message.ToolCalls) > 0 {
			for _, toolCall := range res.Choices[0].Message.ToolCalls {
				if toolCall.Function.Name == "get_weather" {
					var rawArgs map[string]interface{}
					var argsStr string
					if err := json.Unmarshal(toolCall.Function.Arguments, &argsStr); err != nil {
						c.JSON(http.StatusInternalServerError, ApiResponse{
							Code: 500,
							Msg:  "解析工具调用参数字符串失败: " + err.Error(),
							Data: "",
						})
						return
					}

					// 再对字符串进行 json 解析
					if err := json.Unmarshal([]byte(argsStr), &rawArgs); err != nil {
						c.JSON(http.StatusInternalServerError, ApiResponse{
							Code: 500,
							Msg:  "解析工具调用参数失败: " + err.Error(),
							Data: "",
						})
						return
					}

					city, ok := rawArgs["city"].(string)
					if !ok {
						c.JSON(http.StatusInternalServerError, ApiResponse{
							Code: 500,
							Msg:  "城市参数解析失败",
							Data: "",
						})
						return
					}

					weather := GetNowWeather(city)
					fullContent = FormatWeatherResponse(city, []byte(weather))

					// 计算响应时间和Token统计
					responseTime := int(time.Since(startTime).Milliseconds())
					inputTokens := estimateTokens(questions.Content)
					outputTokens := estimateTokens(fullContent)
					suggestions := generateSuggestions(questions.Content, []KnowledgeSearchResult{})

					c.JSON(http.StatusOK, ApiResponse{
						Code: 200,
						Msg:  "success",
						Data: fullContent,
						Stats: &ResponseStats{
							ResponseTime: responseTime,
							InputTokens:  inputTokens,
							OutputTokens: outputTokens,
							TotalTokens:  inputTokens + outputTokens,
						},
						Suggestions: suggestions,
					})
					return
				}
			}
		} else {
			// 普通文本响应
			fullContent = res.Choices[0].Message.Content

			// 计算响应时间和Token统计
			responseTime := int(time.Since(startTime).Milliseconds())
			inputTokens := estimateTokens(questions.Content)
			outputTokens := estimateTokens(fullContent)
			suggestions := generateSuggestions(questions.Content, []KnowledgeSearchResult{})

			c.JSON(http.StatusOK, ApiResponse{
				Code: 200,
				Msg:  "success",
				Data: fullContent,
				Stats: &ResponseStats{
					ResponseTime: responseTime,
					InputTokens:  inputTokens,
					OutputTokens: outputTokens,
					TotalTokens:  inputTokens + outputTokens,
				},
				Suggestions: suggestions,
			})
		}
	}

	// 10. 保存对话记录（保持原有逻辑）
	answerBody := utils.Message{
		Role:    "assistant",
		Content: fullContent,
	}
	store.AddMessage(req.UserID, answerBody)
}
func buildKnowledgeBaseResponse(query string, results []KnowledgeSearchResult) string {
	if len(results) == 0 {
		return "抱歉，我在知识库中没有找到相关信息。"
	}

	// 根据查询内容和结果构建更智能的回复
	response := fmt.Sprintf("根据您的问题「%s」，我在知识库中找到了相关信息：\n\n", query)

	for i, result := range results {
		if i >= 3 { // 最多显示3个结果
			break
		}

		// 根据匹配度调整展示方式
		if result.Score > 0.7 {
			response += fmt.Sprintf("✅ **高度相关**: %s\n", result.Source)
		} else if result.Score > 0.5 {
			response += fmt.Sprintf("📋 **相关内容**: %s\n", result.Source)
		} else {
			response += fmt.Sprintf("💡 **可能相关**: %s\n", result.Source)
		}

		// 智能截取内容，确保显示最相关的部分
		content := result.Content
		if len(content) > 200 {
			// 尝试找到包含查询关键词的部分
			queryLower := strings.ToLower(query)
			contentLower := strings.ToLower(content)
			if idx := strings.Index(contentLower, queryLower); idx != -1 {
				start := max(0, idx-50)
				end := min(len(content), idx+150)
				content = content[start:end]
				if start > 0 {
					content = "..." + content
				}
				if end < len(result.Content) {
					content = content + "..."
				}
			} else {
				content = content[:200] + "..."
			}
		}

		response += fmt.Sprintf("%s\n", content)

		if len(result.Keywords) > 0 && i == 0 { // 只在第一个结果显示关键词
			keywordsToShow := result.Keywords
			if len(keywordsToShow) > 3 {
				keywordsToShow = keywordsToShow[:3]
			}
			response += fmt.Sprintf("🏷️ 相关标签: %s\n", strings.Join(keywordsToShow, "、"))
		}
		response += "\n"
	}

	if len(results) > 3 {
		response += fmt.Sprintf("📚 还有 %d 个相关结果，如需了解更多请进一步提问。\n\n", len(results)-3)
	}

	response += "💬 如果您需要更详细的解释或有其他问题，请随时告诉我！"
	return response
}

// 生成回退响应
func generateFallbackResponse(userInput string) string {
	// 简单的关键词匹配回复
	input := strings.ToLower(userInput)

	if strings.Contains(input, "你好") || strings.Contains(input, "hello") || strings.Contains(input, "hi") {
		return "你好！我是AI助手，很高兴为您服务。目前AI服务暂时不可用，但我仍然可以为您提供基本的帮助。"
	}

	if strings.Contains(input, "谢谢") || strings.Contains(input, "thank") {
		return "不客气！如果您有其他问题，请随时告诉我。"
	}

	if strings.Contains(input, "再见") || strings.Contains(input, "bye") {
		return "再见！祝您生活愉快！"
	}

	// 检查是否为具体技术问题，如果是则不触发固定回复
	if isSpecificTechnicalQuestion(input) {
		// 让AI处理具体技术问题
		return fmt.Sprintf("感谢您的提问：「%s」。目前AI服务暂时不可用，但我已经记录了您的问题。请稍后再试，或者您可以尝试重新表述您的问题。", userInput)
	}

	// 只匹配基础概念定义问题
	if isBasicDefinitionQuestion(input, "go") || isBasicDefinitionQuestion(input, "golang") {
		return "Go是Google开发的一种编程语言，以其简洁、高效和并发性能著称。它特别适合构建网络服务和分布式系统。"
	}

	if isBasicDefinitionQuestion(input, "vue") {
		return "Vue.js是一个渐进式JavaScript框架，用于构建用户界面。它易于学习，具有响应式数据绑定和组件化架构。"
	}

	if isBasicDefinitionQuestion(input, "react") {
		return "React 是一个由 Facebook 开发的开源 JavaScript 库，主要用于构建用户界面。"
	}

	if isBasicDefinitionQuestion(input, "token") {
		return "Token 是一个多义词，在不同领域有着不同的含义，但核心都指向 \"作为某种象征或替代物的事物\"。它可以是物理实体，也可以是数字形式，通常用于代表某种权益、身份、价值或权限。"
	}

	if isBasicDefinitionQuestion(input, "rust") {
		return "Rust 是一种系统编程语言，以其内存安全、高性能和并发编程能力而闻名。"
	}

	if isBasicDefinitionQuestion(input, "js") || isBasicDefinitionQuestion(input, "javascript") {
		return "JavaScript（简称 JS）是一种高级、解释型、弱类型的编程语言，主要用于网页前端开发，同时也可用于服务器端（如 Node.js 环境）、移动应用等领域。"
	}

	if strings.Contains(input, "什么") || strings.Contains(input, "是什么") || strings.Contains(input, "what") {
		return "抱歉，目前AI服务暂时不可用，我无法提供详细的解答。请稍后再试，或者您可以尝试更具体的问题。"
	}

	// 默认回复
	return fmt.Sprintf("感谢您的提问：「%s」。目前AI服务暂时不可用，但我已经记录了您的问题。请稍后再试，或者您可以尝试重新表述您的问题。", userInput)
}

// 估算Token数量（简单估算：中文按字符数，英文按单词数*1.3）
func estimateTokens(text string) int {
	if text == "" {
		return 0
	}

	// 简单的Token估算算法
	runes := []rune(text)
	chineseCount := 0
	englishWords := 0

	for _, r := range runes {
		if r >= 0x4e00 && r <= 0x9fff { // 中文字符范围
			chineseCount++
		}
	}

	// 估算英文单词数
	words := strings.Fields(text)
	for _, word := range words {
		if len(word) > 0 && word[0] < 128 { // ASCII字符
			englishWords++
		}
	}

	// Token估算：中文1字符≈1token，英文1单词≈1.3token
	return chineseCount + int(float64(englishWords)*1.3)
}

// 生成相关提问建议
func generateSuggestions(userQuery string, knowledgeResults []KnowledgeSearchResult) []string {
	suggestions := []string{}

	// 基于用户查询内容生成建议
	query := strings.ToLower(userQuery)

	if strings.Contains(query, "vue") {
		suggestions = append(suggestions, []string{
			"Vue组件化开发的优势是什么？",
			"如何在Vue中进行路由配置？",
			"Vue的响应式原理是什么？",
		}...)
	} else if strings.Contains(query, "go") || strings.Contains(query, "golang") {
		suggestions = append(suggestions, []string{
			"Go语言的并发模型有什么特点？",
			"如何在Go中处理错误？",
			"Go的垃圾回收机制是怎样的？",
		}...)
	} else if strings.Contains(query, "react") {
		suggestions = append(suggestions, []string{
			"React Hooks的使用场景？",
			"React组件的生命周期？",
			"如何优化React应用性能？",
		}...)
	} else {
		// 基于知识库结果生成建议
		if len(knowledgeResults) > 0 {
			for _, result := range knowledgeResults[:min(3, len(knowledgeResults))] {
				if len(result.Keywords) > 0 {
					keyword := result.Keywords[0]
					suggestions = append(suggestions, fmt.Sprintf("深入了解%s的相关原理？", keyword))
				}
			}
		}

		// 默认建议
		if len(suggestions) == 0 {
			suggestions = []string{
				"如何提高编程技能？",
				"最新的技术趋势有哪些？",
				"如何选择合适的技术栈？",
			}
		}
	}

	// 限制返回3个建议
	if len(suggestions) > 3 {
		suggestions = suggestions[:3]
	}

	return suggestions
}

// 辅助函数
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// 检查是否为具体技术问题
func isSpecificTechnicalQuestion(input string) bool {
	// 技术实现相关关键词
	technicalKeywords := []string{
		"异步", "async", "await", "promise", "callback",
		"编程", "开发", "实现", "怎么", "如何", "方法",
		"语法", "代码", "函数", "变量", "循环", "条件",
		"框架", "库", "工具", "配置", "安装", "部署",
		"性能", "优化", "调试", "错误", "bug",
		"api", "接口", "数据库", "服务器", "客户端",
		"组件", "模块", "包", "依赖", "版本",
		"教程", "学习", "入门", "进阶", "高级",
		"原理", "机制", "流程", "步骤", "过程",
	}

	for _, keyword := range technicalKeywords {
		if strings.Contains(input, keyword) {
			return true
		}
	}
	return false
}

// 检查是否为基础定义问题
func isBasicDefinitionQuestion(input, technology string) bool {
	// 只有明确询问"是什么"或基础定义时才返回true
	definitionPatterns := []string{
		technology + "是什么",
		technology + " 是什么",
		"什么是" + technology,
		"什么是 " + technology,
		technology + "的定义",
		technology + " 的定义",
	}

	for _, pattern := range definitionPatterns {
		if strings.Contains(input, pattern) {
			return true
		}
	}

	// 单独的技术名词（如只输入"js"、"vue"等）也视为询问定义
	trimmedInput := strings.TrimSpace(input)
	if trimmedInput == technology || trimmedInput == strings.ToUpper(technology) || trimmedInput == strings.ToLower(technology) {
		return true
	}

	return false
}

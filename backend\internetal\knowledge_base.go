package internetal

import (
	"encoding/json"
	"time"
)

// 知识库主表
type KnowledgeBase struct {
	ID           string    `gorm:"primaryKey;size:50" json:"id"`
	Name         string    `gorm:"size:200;not null" json:"name"`
	Description  string    `gorm:"type:text" json:"description"`
	Type         string    `gorm:"size:50;not null;comment:知识库类型：text,table,image,document" json:"type"`
	Status       string    `gorm:"size:50;default:processing;comment:状态：processing,completed,failed" json:"status"`
	SegmentCount int       `gorm:"default:0;comment:分段总数" json:"segmentCount"`
	CreatedAt    time.Time `gorm:"autoCreateTime" json:"createdAt"`
	UpdatedAt    time.Time `gorm:"autoUpdateTime" json:"updatedAt"`

	// 关联关系
	Files    []KnowledgeBaseFile    `gorm:"foreignKey:KnowledgeBaseID;constraint:OnDelete:CASCADE" json:"files,omitempty"`
	Settings *KnowledgeBaseSettings `gorm:"foreignKey:KnowledgeBaseID;constraint:OnDelete:CASCADE" json:"settings,omitempty"`
}

// 知识库文件表
type KnowledgeBaseFile struct {
	ID              string    `gorm:"primaryKey;size:50" json:"id"`
	KnowledgeBaseID string    `gorm:"size:50;not null;index;comment:知识库ID" json:"knowledgeBaseId"`
	Name            string    `gorm:"size:255;not null;comment:文件名" json:"name"`
	Size            int64     `gorm:"not null;comment:文件大小（字节）" json:"size"`
	Content         string    `gorm:"type:longtext;comment:文件内容" json:"content,omitempty"`
	Status          string    `gorm:"size:50;default:processing;comment:状态：processing,completed,failed" json:"status"`
	Progress        int       `gorm:"default:0;comment:处理进度（0-100）" json:"progress"`
	Error           string    `gorm:"type:text;comment:错误信息" json:"error,omitempty"`
	AIEnhanced      bool      `gorm:"default:false;comment:是否AI增强" json:"aiEnhanced"`
	CreatedAt       time.Time `gorm:"autoCreateTime" json:"createdAt"`
	UpdatedAt       time.Time `gorm:"autoUpdateTime" json:"updatedAt"`

	// 关联关系
	Segments []KnowledgeSegment `gorm:"foreignKey:FileID;constraint:OnDelete:CASCADE" json:"segments,omitempty"`
}

// 知识库分段表
type KnowledgeSegment struct {
	ID              string    `gorm:"primaryKey;size:50" json:"id"`
	FileID          string    `gorm:"size:50;not null;index;comment:文件ID" json:"fileId"`
	KnowledgeBaseID string    `gorm:"size:50;not null;index;comment:知识库ID" json:"knowledgeBaseId"`
	Content         string    `gorm:"type:text;not null;comment:分段内容" json:"content"`
	Keywords        string    `gorm:"type:text;comment:关键词（JSON格式）" json:"keywords,omitempty"`
	Summary         string    `gorm:"type:text;comment:摘要" json:"summary,omitempty"`
	Length          int       `gorm:"not null;comment:内容长度" json:"length"`
	Position        int       `gorm:"not null;comment:在文件中的位置" json:"position"`
	Embedding       string    `gorm:"type:longtext;comment:向量数据（JSON格式）" json:"embedding,omitempty"`
	CreatedAt       time.Time `gorm:"autoCreateTime" json:"createdAt"`
	UpdatedAt       time.Time `gorm:"autoUpdateTime" json:"updatedAt"`
}

// 知识库设置表
type KnowledgeBaseSettings struct {
	ID              uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	KnowledgeBaseID string    `gorm:"size:50;not null;uniqueIndex;comment:知识库ID" json:"knowledgeBaseId"`
	ParseText       bool      `gorm:"default:true;comment:是否解析文本" json:"parseText"`
	ParseTable      bool      `gorm:"default:false;comment:是否解析表格" json:"parseTable"`
	ContentFilter   string    `gorm:"type:text;comment:内容过滤规则" json:"contentFilter,omitempty"`
	SegmentStrategy string    `gorm:"size:50;default:auto;comment:分段策略" json:"segmentStrategy"`
	SegmentLength   int       `gorm:"default:1000;comment:分段长度" json:"segmentLength"`
	SegmentOverlap  int       `gorm:"default:100;comment:分段重叠长度" json:"segmentOverlap"`
	Separator       string    `gorm:"size:50;comment:分隔符" json:"separator,omitempty"`
	ExtractKeywords bool      `gorm:"default:true;comment:是否提取关键词" json:"extractKeywords"`
	GenerateSummary bool      `gorm:"default:false;comment:是否生成摘要" json:"generateSummary"`
	UseAI           bool      `gorm:"default:true;comment:是否使用AI" json:"useAI"`
	FallbackToLocal bool      `gorm:"default:true;comment:是否回退到本地处理" json:"fallbackToLocal"`
	CreatedAt       time.Time `gorm:"autoCreateTime" json:"createdAt"`
	UpdatedAt       time.Time `gorm:"autoUpdateTime" json:"updatedAt"`
}

// 知识库搜索结果（用于搜索API）
type KnowledgeSearchResult struct {
	ID              string   `json:"id"`
	Content         string   `json:"content"`
	Score           float64  `json:"score"`
	Source          string   `json:"source"`
	KnowledgeBaseId string   `json:"knowledgeBaseId"`
	Keywords        []string `json:"keywords"`
	Summary         string   `json:"summary"`
}

// 辅助方法：将Keywords字符串转换为数组
func (ks *KnowledgeSegment) GetKeywords() []string {
	if ks.Keywords == "" {
		return []string{}
	}
	var keywords []string
	json.Unmarshal([]byte(ks.Keywords), &keywords)
	return keywords
}

// 辅助方法：设置Keywords数组
func (ks *KnowledgeSegment) SetKeywords(keywords []string) {
	if keywords == nil {
		keywords = []string{}
	}
	data, _ := json.Marshal(keywords)
	ks.Keywords = string(data)
}

// 辅助方法：获取向量数据
func (ks *KnowledgeSegment) GetEmbedding() []float64 {
	if ks.Embedding == "" {
		return []float64{}
	}
	var embedding []float64
	json.Unmarshal([]byte(ks.Embedding), &embedding)
	return embedding
}

// 辅助方法：设置向量数据
func (ks *KnowledgeSegment) SetEmbedding(embedding []float64) {
	if embedding == nil {
		embedding = []float64{}
	}
	data, _ := json.Marshal(embedding)
	ks.Embedding = string(data)
}

// 表名设置
func (KnowledgeBase) TableName() string {
	return "knowledge_bases"
}

func (KnowledgeBaseFile) TableName() string {
	return "knowledge_base_files"
}

func (KnowledgeSegment) TableName() string {
	return "knowledge_segments"
}

func (KnowledgeBaseSettings) TableName() string {
	return "knowledge_base_settings"
}

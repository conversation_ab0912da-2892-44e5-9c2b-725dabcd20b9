package internetal

import (
	"encoding/json"
	//"errors"
	"fmt"
	"regexp"
	//"strings"
	//"time"
)

// 验证结果结构
type ValidationResult struct {
	IsValid  bool     `json:"isValid"`
	Errors   []string `json:"errors"`
	Warnings []string `json:"warnings"`
}

// 数据验证器
type DataValidator struct{}

// 验证知识库数据
func (v *DataValidator) ValidateKnowledgeBase(kb *KnowledgeBase) ValidationResult {
	result := ValidationResult{
		IsValid:  true,
		Errors:   []string{},
		Warnings: []string{},
	}

	// 必填字段验证
	if kb.ID == "" {
		result.Errors = append(result.Errors, "知识库ID不能为空")
	} else if !v.isValidID(kb.ID) {
		result.Errors = append(result.Errors, "知识库ID格式无效")
	}

	if kb.Name == "" {
		result.Errors = append(result.Errors, "知识库名称不能为空")
	} else if len(kb.Name) > 200 {
		result.Errors = append(result.Errors, "知识库名称长度不能超过200字符")
	}

	if !v.isValidKnowledgeBaseType(kb.Type) {
		result.Errors = append(result.Errors, "知识库类型无效，必须为: text, table, image, document")
	}

	if !v.isValidKnowledgeBaseStatus(kb.Status) {
		result.Errors = append(result.Errors, "知识库状态无效，必须为: processing, completed, failed")
	}

	// 数值字段验证
	if kb.SegmentCount < 0 {
		result.Errors = append(result.Errors, "分段数量不能为负数")
	}

	// 描述长度验证
	if len(kb.Description) > 65535 {
		result.Warnings = append(result.Warnings, "知识库描述过长，可能影响性能")
	}

	// 关联数据验证
	if kb.Files != nil {
		for i, file := range kb.Files {
			fileResult := v.ValidateKnowledgeBaseFile(&file)
			if !fileResult.IsValid {
				for _, err := range fileResult.Errors {
					result.Errors = append(result.Errors, fmt.Sprintf("文件[%d]: %s", i, err))
				}
			}
			for _, warning := range fileResult.Warnings {
				result.Warnings = append(result.Warnings, fmt.Sprintf("文件[%d]: %s", i, warning))
			}
		}
	}

	if kb.Settings != nil {
		settingsResult := v.ValidateKnowledgeBaseSettings(kb.Settings)
		if !settingsResult.IsValid {
			for _, err := range settingsResult.Errors {
				result.Errors = append(result.Errors, fmt.Sprintf("设置: %s", err))
			}
		}
		for _, warning := range settingsResult.Warnings {
			result.Warnings = append(result.Warnings, fmt.Sprintf("设置: %s", warning))
		}
	}

	result.IsValid = len(result.Errors) == 0
	return result
}

// 验证知识库文件数据
func (v *DataValidator) ValidateKnowledgeBaseFile(file *KnowledgeBaseFile) ValidationResult {
	result := ValidationResult{
		IsValid:  true,
		Errors:   []string{},
		Warnings: []string{},
	}

	// 必填字段验证
	if file.ID == "" {
		result.Errors = append(result.Errors, "文件ID不能为空")
	} else if !v.isValidID(file.ID) {
		result.Errors = append(result.Errors, "文件ID格式无效")
	}

	if file.KnowledgeBaseID == "" {
		result.Errors = append(result.Errors, "知识库ID不能为空")
	}

	if file.Name == "" {
		result.Errors = append(result.Errors, "文件名不能为空")
	} else if len(file.Name) > 255 {
		result.Errors = append(result.Errors, "文件名长度不能超过255字符")
	}

	if file.Size < 0 {
		result.Errors = append(result.Errors, "文件大小不能为负数")
	}

	if !v.isValidFileStatus(file.Status) {
		result.Errors = append(result.Errors, "文件状态无效，必须为: processing, completed, failed")
	}

	// 进度验证
	if file.Progress < 0 || file.Progress > 100 {
		result.Errors = append(result.Errors, "处理进度必须为0-100之间的数字")
	}

	// 文件大小警告
	if file.Size > 100*1024*1024 { // 100MB
		result.Warnings = append(result.Warnings, "文件大小超过100MB，可能影响处理性能")
	}

	// 分段数据验证
	if file.Segments != nil {
		for i, segment := range file.Segments {
			segmentResult := v.ValidateKnowledgeSegment(&segment)
			if !segmentResult.IsValid {
				for _, err := range segmentResult.Errors {
					result.Errors = append(result.Errors, fmt.Sprintf("分段[%d]: %s", i, err))
				}
			}
			for _, warning := range segmentResult.Warnings {
				result.Warnings = append(result.Warnings, fmt.Sprintf("分段[%d]: %s", i, warning))
			}
		}
	}

	result.IsValid = len(result.Errors) == 0
	return result
}

// 验证知识库分段数据
func (v *DataValidator) ValidateKnowledgeSegment(segment *KnowledgeSegment) ValidationResult {
	result := ValidationResult{
		IsValid:  true,
		Errors:   []string{},
		Warnings: []string{},
	}

	// 必填字段验证
	if segment.ID == "" {
		result.Errors = append(result.Errors, "分段ID不能为空")
	}

	if segment.FileID == "" {
		result.Errors = append(result.Errors, "文件ID不能为空")
	}

	if segment.KnowledgeBaseID == "" {
		result.Errors = append(result.Errors, "知识库ID不能为空")
	}

	if segment.Content == "" {
		result.Errors = append(result.Errors, "分段内容不能为空")
	}

	// 数值字段验证
	if segment.Length < 0 {
		result.Errors = append(result.Errors, "内容长度不能为负数")
	}

	if segment.Position < 0 {
		result.Errors = append(result.Errors, "位置不能为负数")
	}

	// 内容长度一致性验证
	if segment.Length != len(segment.Content) {
		result.Warnings = append(result.Warnings, "内容长度字段与实际内容长度不一致")
	}

	// 关键词验证
	if segment.Keywords != "" {
		if !v.isValidJSONArray(segment.Keywords) {
			result.Warnings = append(result.Warnings, "关键词格式可能不正确，应为JSON字符串数组")
		}
	}

	// 向量数据验证
	if segment.Embedding != "" {
		if !v.isValidJSONNumberArray(segment.Embedding) {
			result.Warnings = append(result.Warnings, "向量数据格式可能不正确，应为JSON数字数组")
		}
	}

	result.IsValid = len(result.Errors) == 0
	return result
}

// 验证知识库设置数据
func (v *DataValidator) ValidateKnowledgeBaseSettings(settings *KnowledgeBaseSettings) ValidationResult {
	result := ValidationResult{
		IsValid:  true,
		Errors:   []string{},
		Warnings: []string{},
	}

	// 必填字段验证
	if settings.KnowledgeBaseID == "" {
		result.Errors = append(result.Errors, "知识库ID不能为空")
	}

	// 分段策略验证
	if !v.isValidSegmentStrategy(settings.SegmentStrategy) {
		result.Errors = append(result.Errors, "分段策略无效，必须为: auto, semantic, fixed, manual")
	}

	// 数值字段验证
	if settings.SegmentLength <= 0 {
		result.Errors = append(result.Errors, "分段长度必须为正数")
	}

	if settings.SegmentOverlap < 0 {
		result.Errors = append(result.Errors, "分段重叠长度不能为负数")
	}

	// 逻辑验证
	if settings.SegmentOverlap >= settings.SegmentLength {
		result.Warnings = append(result.Warnings, "分段重叠长度不应大于等于分段长度")
	}

	// 性能建议
	if settings.SegmentLength > 5000 {
		result.Warnings = append(result.Warnings, "分段长度过大，可能影响处理性能")
	}

	if settings.SegmentOverlap > 500 {
		result.Warnings = append(result.Warnings, "分段重叠长度过大，可能影响存储效率")
	}

	result.IsValid = len(result.Errors) == 0
	return result
}

// 辅助验证方法
func (v *DataValidator) isValidID(id string) bool {
	// ID格式验证：字母、数字、下划线、连字符，长度3-50
	matched, _ := regexp.MatchString(`^[a-zA-Z0-9_-]{3,50}$`, id)
	return matched
}

func (v *DataValidator) isValidKnowledgeBaseType(t string) bool {
	validTypes := []string{"text", "table", "image", "document"}
	for _, validType := range validTypes {
		if t == validType {
			return true
		}
	}
	return false
}

func (v *DataValidator) isValidKnowledgeBaseStatus(status string) bool {
	validStatuses := []string{"processing", "completed", "failed"}
	for _, validStatus := range validStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}

func (v *DataValidator) isValidFileStatus(status string) bool {
	validStatuses := []string{"processing", "completed", "failed"}
	for _, validStatus := range validStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}

func (v *DataValidator) isValidSegmentStrategy(strategy string) bool {
	validStrategies := []string{"auto", "semantic", "fixed", "manual"}
	for _, validStrategy := range validStrategies {
		if strategy == validStrategy {
			return true
		}
	}
	return false
}

func (v *DataValidator) isValidJSONArray(jsonStr string) bool {
	var arr []string
	return json.Unmarshal([]byte(jsonStr), &arr) == nil
}

func (v *DataValidator) isValidJSONNumberArray(jsonStr string) bool {
	var arr []float64
	return json.Unmarshal([]byte(jsonStr), &arr) == nil
}

// 批量验证器
type BatchValidator struct {
	validator *DataValidator
}

func NewBatchValidator() *BatchValidator {
	return &BatchValidator{
		validator: &DataValidator{},
	}
}

// 批量验证知识库列表
func (bv *BatchValidator) ValidateKnowledgeBaseList(kbList []KnowledgeBase) ValidationResult {
	result := ValidationResult{
		IsValid:  true,
		Errors:   []string{},
		Warnings: []string{},
	}

	for i, kb := range kbList {
		kbResult := bv.validator.ValidateKnowledgeBase(&kb)
		if !kbResult.IsValid {
			for _, err := range kbResult.Errors {
				result.Errors = append(result.Errors, fmt.Sprintf("知识库[%d]: %s", i, err))
			}
		}
		for _, warning := range kbResult.Warnings {
			result.Warnings = append(result.Warnings, fmt.Sprintf("知识库[%d]: %s", i, warning))
		}
	}

	result.IsValid = len(result.Errors) == 0
	return result
}

// 验证创建知识库请求
func (bv *BatchValidator) ValidateCreateKnowledgeBaseRequest(req map[string]interface{}) ValidationResult {
	result := ValidationResult{
		IsValid:  true,
		Errors:   []string{},
		Warnings: []string{},
	}

	// 验证必填字段
	if name, ok := req["name"].(string); !ok || name == "" {
		result.Errors = append(result.Errors, "知识库名称不能为空")
	}

	if kbType, ok := req["type"].(string); !ok || !bv.validator.isValidKnowledgeBaseType(kbType) {
		result.Errors = append(result.Errors, "知识库类型无效")
	}

	// 验证设置
	if settings, ok := req["settings"].(map[string]interface{}); ok {
		if segmentLength, ok := settings["segmentLength"].(float64); ok && segmentLength <= 0 {
			result.Errors = append(result.Errors, "分段长度必须为正数")
		}

		if segmentOverlap, ok := settings["segmentOverlap"].(float64); ok && segmentOverlap < 0 {
			result.Errors = append(result.Errors, "分段重叠长度不能为负数")
		}
	}

	result.IsValid = len(result.Errors) == 0
	return result
}

// 数据完整性检查器
type DataIntegrityChecker struct{}

// 检查知识库数据完整性
func (dic *DataIntegrityChecker) CheckKnowledgeBaseIntegrity(kb *KnowledgeBase) []string {
	issues := []string{}

	// 检查分段数量一致性
	if kb.Files != nil {
		actualSegmentCount := 0
		for _, file := range kb.Files {
			if file.Segments != nil {
				actualSegmentCount += len(file.Segments)
			}
		}
		if actualSegmentCount != kb.SegmentCount {
			issues = append(issues, fmt.Sprintf("分段数量不一致：记录为%d，实际为%d", kb.SegmentCount, actualSegmentCount))
		}
	}

	// 检查时间逻辑
	if !kb.CreatedAt.IsZero() && !kb.UpdatedAt.IsZero() && kb.UpdatedAt.Before(kb.CreatedAt) {
		issues = append(issues, "更新时间早于创建时间")
	}

	return issues
}

// 检查文件数据完整性
func (dic *DataIntegrityChecker) CheckFileIntegrity(file *KnowledgeBaseFile) []string {
	issues := []string{}

	// 检查文件状态与进度一致性
	if file.Status == "completed" && file.Progress != 100 {
		issues = append(issues, "文件状态为完成但进度不是100%")
	}

	if file.Status == "failed" && file.Error == "" {
		issues = append(issues, "文件状态为失败但没有错误信息")
	}

	// 检查内容与大小一致性
	if file.Content != "" && int64(len(file.Content)) != file.Size {
		issues = append(issues, "文件内容长度与记录的大小不一致")
	}

	return issues
}

// 全局验证器实例
var GlobalValidator = &DataValidator{}
var GlobalBatchValidator = NewBatchValidator()
var GlobalIntegrityChecker = &DataIntegrityChecker{}

package main

import (
	"backend/internetal"
	"net/http"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

func simpleMain() {
	r := gin.Default()

	// 配置 CORS 中间件
	r.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"http://localhost:3000", "http://localhost:3001", "http://localhost:3002", "http://localhost:8081"},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"*"},
		AllowCredentials: true,
	}))

	// 加载配置
	config, err := loadConfig()
	if err != nil {
		panic("加载配置失败: " + err.Error())
	}

	// 调用统一的数据库初始化
	InitDB()
	db := GetDB() // 使用 GetDB() 获取连接

	// 初始化数据库表结构
	db.AutoMigrate(&internetal.Robot{})

	// 创建邮件客户端 (使用配置文件中的信息)
	mailClient := internetal.NewMailClient(
		config.Mail.SMTPServer, // SMTP服务器地址
		config.Mail.SMTPPort,   // SMTP端口
		config.Mail.FromEmail,  // 发件人邮箱
		config.Mail.AuthCode,   // 邮箱授权码
	)

	// 创建用户控制器
	userController := internetal.NewUserController(db, mailClient)

	// 创建数据库管理器
	dbManager := internetal.NewDBManager(db)

	// 用户相关路由
	user := r.Group("/api")
	{
		user.POST("/login", userController.Login)
		user.POST("/sms-login", userController.SmsLogin)
		user.POST("/register", userController.Register)
		user.POST("/send-captcha", userController.SendCaptcha)
		user.POST("/reset-password", userController.ResetPassword)
	}

	// 机器人相关路由（添加认证中间件保护）
	robot := r.Group("/api/robot", internetal.AuthMiddleware(dbManager))
	{
		robot.POST("/create", createRobot)
		robot.POST("/find/by_id", findByID)
		robot.POST("/find/by_name", findByName)
		robot.POST("/update", updateRobot)
		robot.POST("/delete", deleteRobot)
		robot.POST("/list", listRobots)
	}

	r.Run(":8080")
}

// 删除 initDatabase 函数
// 使用 database.go 中的统一初始化

// 所有数据库操作函数添加 GetDB() 调用
func createRobot(c *gin.Context) {
	var robot internetal.Robot
	if err := c.ShouldBindJSON(&robot); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "参数绑定失败"})
		return
	}

	// 从上下文中获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"code": 401, "msg": "未认证"})
		return
	}

	// 安全地断言userID为uint类型
	userIDUint, ok := userID.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "用户ID类型错误"})
		return
	}

	// 设置机器人的用户ID
	robot.UserID = userIDUint

	// 设置默认值
	if robot.PersonnelDesign == "" {
		robot.PersonnelDesign = "你是一个友善、专业的AI助手。"
	}
	if robot.ReplyLogic == "" {
		robot.ReplyLogic = `{"systemRole":"你是一个专业的AI助手","dialogCommand":"请以简洁、专业的方式回答用户的问题","responseMode":"simple","temperature":0.6,"maxTokens":50,"language":"zh-cn","speaker":"601002","speechSpeed":1.0}`
	}
	if robot.KnowledgeConfig == "" {
		robot.KnowledgeConfig = `{"callMethod":"auto","searchStrategy":"mixed","maxRecall":5,"minScore":0.50,"queryRewrite":true,"resultRerank":true}`
	}

	// 设置时间戳
	robot.CreateTime = time.Now()
	robot.UpdateTime = time.Now()

	// 保存到数据库
	db := GetDB() // 获取数据库连接
	result := db.Create(&robot)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "创建失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"code": 200, "data": robot, "msg": "创建成功"})
}

// 其他函数采用相同的修改模式...
func findByID(c *gin.Context) {
	var req struct {
		ID int `json:"id"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "参数绑定失败"})
		return
	}

	db := GetDB() // 获取数据库连接
	var robot internetal.Robot
	result := db.First(&robot, req.ID)
	if result.Error != nil {
		c.JSON(http.StatusNotFound, gin.H{"code": 404, "msg": "机器人不存在"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"code": 200, "data": robot})
}

func findByName(c *gin.Context) {

	var req struct {
		Name string `json:"name"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "参数绑定失败"})
		return
	}

	var robot internetal.Robot
	db := GetDB() // 获取数据库连接
	result := db.Where("name = ?", req.Name).First(&robot)
	if result.Error != nil {
		c.JSON(http.StatusNotFound, gin.H{"code": 404, "msg": "机器人不存在"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"code": 200, "data": robot})
}

func updateRobot(c *gin.Context) {
	var robot internetal.Robot
	if err := c.ShouldBindJSON(&robot); err != nil || robot.ID == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "参数绑定失败或缺少id"})
		return
	}

	// 查找现有记录
	var existing internetal.Robot
	db := GetDB() // 获取数据库连接
	result := db.First(&existing, robot.ID)
	if result.Error != nil {
		c.JSON(http.StatusNotFound, gin.H{"code": 404, "msg": "机器人不存在"})
		return
	}

	// 只更新非空字段
	if robot.Name != "" {
		existing.Name = robot.Name
	}
	if robot.Description != "" {
		existing.Description = robot.Description
	}
	if robot.PersonnelDesign != "" {
		existing.PersonnelDesign = robot.PersonnelDesign
	}
	if robot.ReplyLogic != "" {
		existing.ReplyLogic = robot.ReplyLogic
	}
	if robot.KnowledgeConfig != "" {
		existing.KnowledgeConfig = robot.KnowledgeConfig
	}

	// 更新更新时间
	existing.UpdateTime = time.Now()

	// 保存更新
	result = db.Save(&existing)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "更新失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"code": 200, "msg": "更新成功"})
}

func deleteRobot(c *gin.Context) {
	var req struct {
		ID int `json:"id"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "参数绑定失败"})
		return
	}

	// 从数据库删除
	db := GetDB() // 获取数据库连接
	result := db.Delete(&internetal.Robot{}, req.ID)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "删除失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"code": 200, "msg": "删除成功"})
}

func listRobots(c *gin.Context) {
	// 从上下文中获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"code": 401, "msg": "未认证"})
		return
	}

	// 安全地断言userID为uint类型
	userIDUint, ok := userID.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "用户ID类型错误"})
		return
	}

	var robots []internetal.Robot
	// 查询属于指定用户的机器人
	db := GetDB() // 获取数据库连接
	result := db.Where("user_id = ?", userIDUint).Find(&robots)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "获取列表失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"code": 200, "data": robots})
}

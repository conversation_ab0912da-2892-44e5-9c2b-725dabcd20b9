package main

import (
	"backend/internetal"
	"context"
	"fmt"
	"io"
	"log"
	"math"
	"net/http"
	"regexp"
	"sort"
	"strconv"

	"github.com/goccy/go-json"
	"gopkg.in/yaml.v2"

	"bytes"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"

	"github.com/gorilla/websocket"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

type Config struct {
	Database struct {
		Host     string `yaml:"host"`
		Port     string `yaml:"port"`
		Username string `yaml:"username"`
		Password string `yaml:"password"`
		Base     string `yaml:"base"`
	} `yaml:"database"`
	Mail struct {
		SMTPServer string `yaml:"smtp_server"`
		SMTPPort   string `yaml:"smtp_port"`
		FromEmail  string `yaml:"from_email"`
		AuthCode   string `yaml:"auth_code"`
	} `yaml:"mail"`
}

var (
	frontendUrl string = "localhost:8080"
	rustUrl     string = "ws://**************:8080/call/webrtc"
)

// 连接数据库
var SqlSession *gorm.DB

func InitAql(config *Config) {
	// 构建数据库连接字符串
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		config.Database.Username,
		config.Database.Password,
		config.Database.Host,
		config.Database.Port,
		config.Database.Base,
	)
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		panic(err)
	}
	SqlSession = db
}

func loadConfig() (*Config, error) {
	_, b, _, _ := runtime.Caller(0)
	basepath := filepath.Dir(b)
	configPath := filepath.Join(basepath, "config.yaml")
	log.Printf("basepath: %s", basepath)
	log.Printf("configPath: %s", configPath)
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}
	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}
	return &config, nil
}

func main() {

	r := gin.Default()

	// 配置 CORS 中间件（作用于当前 r）
	r.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"http://localhost:3000", "http://localhost:8081", "http://localhost:3001", "http://localhost:3002"},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"*"},
		AllowCredentials: true,
	}))

	InitDB()

	// 加载配置（如果需要）
	config, err := loadConfig()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}
	InitAql(config)

	// 初始化数据库（注意：你的 InitDB 还没贴，确保这里没问题）
	InitDB()

	// 初始化知识库服务
	knowledgeBaseService = internetal.NewKnowledgeBaseService(GetDB())
	// 创建数据库管理器
	dbManager := internetal.NewDBManager(SqlSession)

	// 初始化邮件客户端
	mailClient := internetal.NewMailClient(
		config.Mail.SMTPServer,
		config.Mail.SMTPPort,
		config.Mail.FromEmail,
		config.Mail.AuthCode,
	)

	// 创建助手API实例
	assistantApi := internetal.NewAssistantApi(SqlSession)

	// 添加认证中间件保护的助手API路由
	assistant := r.Group("/api/assistant", internetal.AuthMiddleware(dbManager))
	{
		assistant.POST("/create", assistantApi.HandleCreate)
		assistant.POST("/update", assistantApi.HandleUpdate)
		assistant.DELETE("/:id", assistantApi.HandleDelete)
		assistant.GET("/list", assistantApi.HandleList)
	}

	// 加载配置（如果需要
	// 添加提示词优化路由
	prompt := r.Group("/api/prompt")
	{
		prompt.POST("/optimize", OptimizePrompt)
		prompt.POST("/optimize-debug", OptimizeBasedOnDebug)
	}

	// 添加知识库相关路由
	kb := r.Group("/api/knowledge-base")
	{
		kb.POST("/analyze-document", AnalyzeDocument)
		kb.POST("/segment-document", SegmentDocument)
		kb.POST("/extract-keywords", ExtractKeywords)
		kb.POST("/generate-summary", GenerateSummary)
		kb.POST("/batch-process", BatchProcessDocuments)
		kb.POST("/search", SearchKnowledgeBase)
		kb.POST("/create", CreateKnowledgeBase)
		kb.POST("/save", SaveKnowledgeBase)
		kb.POST("/update", UpdateKnowledgeBase)
		kb.POST("/list", ListKnowledgeBases)
		kb.GET("/detail/:id", GetKnowledgeBaseDetail)
		kb.POST("/delete", DeleteKnowledgeBase)
	}

	config, err = loadConfig()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}
	InitAql(config)
	// 添加认证中间件保护的机器人路由
	robot := r.Group("/api/robot", internetal.AuthMiddleware(dbManager))
	{
		robot.POST("/create", CreateRobotRepository)
		robot.POST("/find/by_id", FindByIdRepository)
		robot.POST("/find/by_name", FindByNameRepository)
		robot.POST("/update", UpdateRobotRepository)
		robot.POST("/delete", DeleteRobotRepository)
		robot.POST("/list", ListRobotsRepository)
		robot.GET("/find/by_id", FindByRobotIdRepository)

	}
	// 初始化用户控制器并传入邮件客户端
	userController := internetal.NewUserController(SqlSession, mailClient)

	// 用户API路由
	user := r.Group("/api")
	{
		user.POST("/login", userController.Login)
		user.POST("/send-captcha", userController.SendCaptcha)
		user.POST("/register", userController.Register)
		user.POST("/smsLogin", userController.SmsLogin)            // 短信登录功能已调整为使用邮箱验证码
		user.POST("/reset-password", userController.ResetPassword) // 添加重置密码路由
	}

	// 添加认证中间件保护的历史记录路由
	history := r.Group("/api/history", internetal.AuthMiddleware(dbManager))
	{
		history.POST("/save", SaveConversationHistory)
		history.POST("/list", GetHistoryList)
		history.POST("/messages", GetHistoryMessages)
		history.POST("/delete", DeleteHistory)
	}

	llm := r.Group("api/llm")
	{
		llm.POST("/communication", LlmInteraction)
	}
	r.POST("/api/voice/settings", handleVoiceSettings)
	r.GET("/api/voice/settings", handleGetVoiceSettings)
	r.POST("/api/tts/test", handleTTSTest)

	// 语音包管理相关API
	r.GET("/api/voice/packages/list", handleListVoicePackages)
	r.GET("/api/voice/packages/download", handleDownloadVoicePackage)
	r.POST("/api/voice/packages/upload", handleUploadVoicePackage)

	r.GET("/ws", handleConnection)

	r.POST("/api/text/send", func(c *gin.Context) {
		LlmInteraction(c)
	})

	r.POST("/api/call/status", UpdateCallStatus)
	r.GET("/api/call/status", GetCallStatus)

	r.Static("/js", "./static/js")
	r.Static("/css", "./static/css")
	r.StaticFile("/", "./static/index.html")

	// 支持 SPA 的其他路径
	r.NoRoute(func(c *gin.Context) {
		c.File("./static/index.html")
	})
	// 注意端口号
	r.Run(":8080")

}

var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	// 检查请求来源，这里设置为允许所有来源
	CheckOrigin: func(r *http.Request) bool {
		return true
	},
}

// 通话状态管理
type CallStatusRequest struct {
	RobotID int    `json:"robot_id"`
	Status  string `json:"status"` // "calling", "ended"
	Id      string `json:"id"`
}

var callStatusMap = make(map[int]string) // 存储机器人通话状态

func UpdateCallStatus(c *gin.Context) {
	var req CallStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "参数错误"})
		return
	}

	callStatusMap[req.RobotID] = req.Status
	c.JSON(http.StatusOK, gin.H{"code": 200, "msg": "状态更新成功"})
}

func GetCallStatus(c *gin.Context) {
	robotID, err := strconv.Atoi(c.Query("robot_id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "参数错误"})
		return
	}

	status, exists := callStatusMap[robotID]
	if !exists {
		status = "idle"
	}

	c.JSON(http.StatusOK, gin.H{"code": 200, "data": status})
}

func handleConnection(c *gin.Context) {

	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Println("Error upgrading connection:", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to upgrade connection"})
		return
	}
	defer conn.Close()
	ctx, cancel := context.WithCancel(c)
	defer cancel()
	for {
		_, message, err := conn.ReadMessage()
		var req WebRTCMessage
		if err := json.Unmarshal(message, &req); err != nil {
			log.Printf("无法解析消息: %v, 原始消息: %s", err, string(message))
			continue
		}
		err = json.Unmarshal(message, &req)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to upgrade connection"})
			return
		}
		fmt.Println(string(message))
		if req.Type == "offer" {
			log.Printf("处理offer请求，当前语音设置: Language=%s, Voice=%s",
				globalVoiceSettings.Language, globalVoiceSettings.Voice)

			// 使用全局语音设置，并转换为腾讯云支持的VoiceType
			tencentVoiceType := getTencentVoiceType(globalVoiceSettings.Language, globalVoiceSettings.Voice)
			log.Printf("最终使用的腾讯云VoiceType: %s", tencentVoiceType)
			NewSignalingClient(conn, ctx, rustUrl, PBXMessage{
				Command: "invite",
				Option: &CallOptions{
					Asr: &AsrConfig{
						ModelType: "16k_zh",
						Endpoint:  "asr.tencentapi.com",
						Language:  globalVoiceSettings.Language,
						Provider:  "tencent",
						AppId:     "**********",
						SecretId:  "AKIDqfPySltodHk3WzRoyywseCZkiSyHPK3b",
						SecretKey: "PaWFk26w12tX2LSPAlBRXUI7oKgojPJ2",
					},
					Tts: &TtsConfig{
						Endpoint:  "tts.tencentapi.com",
						Provider:  "tencent",
						Speaker:   tencentVoiceType,
						Speed:     float32(globalVoiceSettings.Rate),
						Volume:    globalVoiceSettings.Volume,
						AppId:     "**********",
						SecretId:  "AKIDqfPySltodHk3WzRoyywseCZkiSyHPK3b",
						SecretKey: "PaWFk26w12tX2LSPAlBRXUI7oKgojPJ2",
					},
					Offer: req.SDP,
				},
			})
		}
	}
}

type SignalingClient struct {
	outConn      *websocket.Conn
	conn         *websocket.Conn
	ctx          context.Context
	cancel       context.CancelFunc
	recvDone     chan struct{}
	isTTSPlaying bool // 新增字段
}

type PBXMessage struct {
	Command string       `json:"command"` // 操作类型，如 'invite', 'tts'
	Option  *CallOptions `json:"option,omitempty"`
	Text    string       `json:"text,omitempty"`
	PlayId  string       `json:"playId,omitempty"`
}

// CallOptions 包含呼叫配置的详细信息
type CallOptions struct {
	Asr   *AsrConfig `json:"asr,omitempty"`   // 自动语音识别配置
	Tts   *TtsConfig `json:"tts,omitempty"`   // 语音合成配置
	Offer string     `json:"offer,omitempty"` // SDP Offer 信息
}

// AsrConfig 自动语音识别配置
type AsrConfig struct {
	ModelType string `json:"modelType"`
	Endpoint  string `json:"endpoint"`
	Provider  string `json:"provider"`
	AppId     string `json:"appId"`
	SecretId  string `json:"secretId"`
	SecretKey string `json:"secretKey"`
	Language  string `json:"language"`
}

// TtsConfig 文本转语音配置
type TtsConfig struct {
	Endpoint  string  `json:"endpoint"`
	Provider  string  `json:"provider"`
	Speaker   string  `json:"speaker"`
	AppId     string  `json:"appId"`
	SecretId  string  `json:"secretId"`
	SecretKey string  `json:"secretKey"`
	Speed     float32 `json:"speed"`
	Volume    int     `json:"volume"`
}

// EventMessage 表示服务端发送的事件通知
type EventMessage struct {
	Event     string                 `json:"event"`
	TrackId   string                 `json:"trackId,omitempty"`
	Timestamp *uint64                `json:"timestamp,omitempty"`
	Key       string                 `json:"key,omitempty"`
	Duration  uint32                 `json:"duration,omitempty"`
	SDP       string                 `json:"sdp,omitempty"`
	Data      map[string]interface{} `json:"data,omitempty"`
	Text      string                 `json:"text,omitempty"`
}

type WebRTCMessage struct {
	Type string `json:"type"`          // 消息类型: offer / answer / ice-candidate
	SDP  string `json:"sdp,omitempty"` // SDP 内容（仅 offer / answer 时有）
	Text string `json:"text,omitempty"`
}

func NewSignalingClient(Conn *websocket.Conn, ctx context.Context, serverAddr string, initial PBXMessage) (*SignalingClient, error) {
	ctx, cancel := context.WithCancel(ctx)

	conn, _, err := websocket.DefaultDialer.Dial(serverAddr, nil)
	if err != nil {
		cancel()
		return nil, fmt.Errorf("websocket dial failed: %w", err)
	}

	msg, err := json.Marshal(initial)
	if err != nil {
		cancel()
		return nil, fmt.Errorf("failed to marshal initial PBXMessage: %w", err)
	}
	if err := conn.WriteMessage(websocket.TextMessage, msg); err != nil {
		cancel()
		return nil, fmt.Errorf("failed to send initial message: %w", err)
	}
	log.Println("Send invite command to RustPBX....")

	client := &SignalingClient{
		outConn:  Conn,
		conn:     conn,
		ctx:      ctx,
		cancel:   cancel,
		recvDone: make(chan struct{}),
	}
	go client.listen()
	return client, nil
}

func (s *SignalingClient) listen() {
	defer close(s.recvDone)
	client := NewOpenAIClient("5cd96106bc3a488a8833da74f2bfaced.z7C5LFWGcBhl6nYT")
	for {
		select {
		case <-s.ctx.Done():
			return
		default:
			typeVal, data, err := s.conn.ReadMessage()
			if err != nil {
				return
			}
			if typeVal != websocket.TextMessage {
				continue
			}
			log.Println("received:", string(data))
			var evt EventMessage
			if err := json.Unmarshal(data, &evt); err != nil {
				continue
			}
			s.handleEvent(evt, client)
		}
	}
}
func extractCityFromText(text string) string {
	// 简单写死几个城市名
	cities := []string{
		"北京", "上海", "天津", "重庆", "哈尔滨", "长春", "沈阳", "大连", "呼和浩特", "石家庄", "太原", "西安", "济南", "青岛", "郑州", "南京", "苏州", "杭州", "宁波", "合肥", "福州", "厦门", "南昌", "长沙", "武汉", "广州", "深圳", "南宁", "海口", "成都", "贵阳", "昆明", "拉萨", "兰州", "西宁", "银川", "乌鲁木齐", "香港", "澳门", "台北",
	}
	for _, city := range cities {
		if strings.Contains(text, city) {
			return city
		}
	}
	return ""
}

func (s *SignalingClient) handleEvent(evt EventMessage, llm *OpenAIClient) {

	switch evt.Event {
	case "answer":
		var message = WebRTCMessage{
			SDP:  evt.SDP,
			Type: "answer",
		}
		marshal, _ := json.Marshal(message)
		s.outConn.WriteMessage(websocket.TextMessage, marshal)

		// 使用当前语音设置发送TTS
		log.Printf("使用语音设置: Language=%s, Voice=%s, Rate=%.1f, Volume=%d",
			globalVoiceSettings.Language, globalVoiceSettings.Voice,
			globalVoiceSettings.Rate, globalVoiceSettings.Volume)

		// 根据语言选择合适的问候语
		greetingText := getGreetingByLanguage(globalVoiceSettings.Language)

		resp := PBXMessage{
			Command: "tts",
			Text:    greetingText,
		}

		marshal, _ = json.Marshal(resp)
		s.conn.WriteMessage(websocket.TextMessage, marshal)
	case "asrFinal":
		log.Println("[ASR识别] 用户语音转文字:", evt.Text)

		// 简单判断是否是天气问题
		if strings.Contains(evt.Text, "天气") {
			// 1. 先发送用户的问题给前端显示（补充这一步）
			userQuestionMsg := WebRTCMessage{
				Text: evt.Text,   // 用户的天气问题文本
				Type: "asrFinal", // 前端识别此类型显示用户输入
			}
			userMarshal, _ := json.Marshal(userQuestionMsg)
			s.outConn.WriteMessage(websocket.TextMessage, userMarshal)

			// 2. 原有天气处理逻辑（提取城市、获取天气数据）
			city := extractCityFromText(evt.Text)
			if city == "" {
				city = "北京" // 默认城市（可自定义）
			}
			jsonStr := GetNowWeather(city)
			weatherText := FormatWeatherResponse(city, []byte(jsonStr))
			log.Println("[天气模块] 回复内容:", weatherText)

			// 3. 发送天气回复给前端显示（补充这一步）
			weatherReplyMsg := WebRTCMessage{
				Text: weatherText,  // 天气回复文本
				Type: "aiResponse", // 前端识别此类型显示系统回复
			}
			weatherMarshal, _ := json.Marshal(weatherReplyMsg)
			s.outConn.WriteMessage(websocket.TextMessage, weatherMarshal)

			// 4. 发送TTS命令播放天气（原有逻辑保留）
			resp := PBXMessage{
				Command: "tts",
				Text:    weatherText,
			}
			marshal, _ := json.Marshal(resp)
			s.conn.WriteMessage(websocket.TextMessage, marshal)

			return // 不继续发给AI
		}
		// 非天气内容，照常转发给 AI
		var message = WebRTCMessage{
			Text: evt.Text,
			Type: "asrFinal",
		}
		marshal, _ := json.Marshal(message)
		s.outConn.WriteMessage(websocket.TextMessage, marshal)

		// 获取AI回复（使用原始中文进行思考）
		chineseReply, _ := llm.GenerateText("请回答", evt.Text, "", "")

		// 翻译AI回复到目标语言
		var aiDisplayText string
		if globalVoiceSettings.Language == "zh-cn" {
			aiDisplayText = chineseReply
		} else {
			aiDisplayText = translateForTTS(chineseReply, globalVoiceSettings.Language)
			log.Printf("AI回复翻译: %s -> %s", chineseReply, aiDisplayText)
		}

		// 发送翻译后的AI回复
		aiMessage := WebRTCMessage{
			Text: aiDisplayText, // 发送翻译后的AI回复
			Type: "aiResponse",
		}
		aiMarshal, _ := json.Marshal(aiMessage)
		s.outConn.WriteMessage(websocket.TextMessage, aiMarshal)

		// 发送TTS开始信号
		ttsStartMessage := WebRTCMessage{
			Type: "ttsStart",
		}
		ttsStartMarshal, _ := json.Marshal(ttsStartMessage)
		s.outConn.WriteMessage(websocket.TextMessage, ttsStartMarshal)

		// 发送TTS命令（使用翻译后的文本）
		resp := PBXMessage{
			Command: "tts",
			Text:    aiDisplayText, // TTS也使用翻译后的文本
		}
		marshal, _ = json.Marshal(resp)
		s.conn.WriteMessage(websocket.TextMessage, marshal)
	case "ttsComplete":
		// TTS播放完成，发送信号给前端
		ttsEndMessage := WebRTCMessage{
			Type: "ttsEnd",
		}
		marshal, _ := json.Marshal(ttsEndMessage)
		s.outConn.WriteMessage(websocket.TextMessage, marshal)

	case "asrDelta":
		// ASR实时识别结果，可以选择处理或忽略
		log.Printf("ASR实时识别: %s", evt.Text)

	case "metrics":
		// 性能指标，记录日志即可
		log.Printf("性能指标: %s", evt.Key)

	case "trackStart":
		log.Printf("音轨开始: %s", evt.TrackId)

	case "trackEnd":
		log.Printf("音轨结束: %s", evt.TrackId)

	case "error":
		log.Printf("TTS错误: %s", evt.Data)
		// 发送错误信息给前端
		errorMessage := WebRTCMessage{
			Type: "error",
			Text: fmt.Sprintf("TTS错误: %v", evt.Data),
		}
		marshal, _ := json.Marshal(errorMessage)
		s.outConn.WriteMessage(websocket.TextMessage, marshal)

	default:
		log.Printf("Unhandled event: %s", evt.Event)
	}

}
func getGreetingByLanguage(language string) string {
	switch language {
	case "zh-cn":
		return "我是语音助手"
	case "en-us":
		return "Hello, I am your voice assistant"
	case "ja-jp":
		return "こんにちは、私は音声アシスタントです"
	case "ko-kr":
		return "안녕하세요, 저는 음성 어시스턴트입니다"
	case "fr-fr":
		return "Bonjour, je suis votre assistant vocal"
	case "de-de":
		return "Hallo, ich bin Ihr Sprachassistent"
	default:
		return "我是语音助手"
	}
}

// getTencentVoiceType 将前端voice参数转换为腾讯云TTS支持的VoiceType
func getTencentVoiceType(language, voice string) string {
	// 腾讯云TTS支持的VoiceType映射
	voiceMap := map[string]map[string]string{
		"zh-cn": {
			"601002": "101001",                // 智瑜 - 温暖女声
			"601003": "101002",                // 智聆 - 通用女声
			"601004": "101003",                // 智美 - 甜美女声
			"601005": "101004",                // 智云 - 通用男声
			"601006": "10case \"asrFinal1005", // 智莉 - 通用女声
			"601007": "101006",                // 智言 - 助手女声
		},
		"en-us": {
			"601101": "1001", // Emma (Female) - 英文女声
			"601102": "1002", // Brian (Male) - 英文男声
			"601103": "1050", // Amy (Female) - 英文女声
			"601104": "1051", // Russell (Male) - 英文男声
		},
		"ja-jp": {
			"601201": "1003", // さくら (Female) - 日文女声
			"601202": "1004", // たかし (Male) - 日文男声
		},
		"ko-kr": {
			"601301": "1005", // 지은 (Female) - 韩文女声
			"601302": "1006", // 민수 (Male) - 韩文男声
		},
		"fr-fr": {
			"601401": "1007", // Céline (Female) - 法文女声
			"601402": "1008", // Pierre (Male) - 法文男声
		},
		"de-de": {
			"601501": "1009", // Anna (Female) - 德文女声
			"601502": "1010", // Hans (Male) - 德文男声
		},
	}

	if langMap, exists := voiceMap[language]; exists {
		if tencentVoice, exists := langMap[voice]; exists {
			log.Printf("语音映射: %s-%s -> %s", language, voice, tencentVoice)
			return tencentVoice
		}
	}

	// 默认返回中文女声
	log.Printf("使用默认语音: 101001 (原始: %s-%s)", language, voice)
	return "101001"
}

// translateForTTS 专门为TTS翻译文本
func translateForTTS(chineseText, targetLanguage string) string {
	if targetLanguage == "zh-cn" {
		return chineseText
	}

	// 构建翻译提示词
	var translatePrompt string
	switch targetLanguage {
	case "en-us":
		translatePrompt = "Translate this Chinese text to natural English:"
	case "ja-jp":
		translatePrompt = "この中国語を自然な日本語に翻訳してください:"
	case "ko-kr":
		translatePrompt = "이 중국어를 자연스러운 한국어로 번역해주세요:"
	case "fr-fr":
		translatePrompt = "Traduisez ce texte chinois en français naturel:"
	case "de-de":
		translatePrompt = "Übersetzen Sie diesen chinesischen Text ins natürliche Deutsche:"
	default:
		return chineseText
	}

	// 使用现有的LLM客户端进行翻译
	client := NewOpenAIClient("5cd96106bc3a488a8833da74f2bfaced.z7C5LFWGcBhl6nYT")
	translatedText, err := client.GenerateText(translatePrompt, chineseText, "", "")
	if err != nil {
		log.Printf("翻译失败: %v, 使用原文", err)
		return chineseText
	}

	return translatedText
}

// 知识库相关的请求和响应结构体
type DocumentAnalysisRequest struct {
	Content string `json:"content"`
}

type DocumentAnalysisResponse struct {
	DocumentType   string   `json:"documentType"`
	StructureScore int      `json:"structureScore"`
	QualityScore   int      `json:"qualityScore"`
	Strategy       string   `json:"recommendedStrategy"`
	MainTopics     []string `json:"mainTopics"`
}

type SegmentDocumentRequest struct {
	Content   string `json:"content"`
	MaxLength int    `json:"maxLength"`
	Strategy  string `json:"strategy"`
}

type Segment struct {
	ID       string   `json:"id"`
	Content  string   `json:"content"`
	Keywords []string `json:"keywords"`
	Summary  string   `json:"summary"`
	Length   int      `json:"length"`
}

type SegmentDocumentResponse struct {
	Segments []Segment `json:"segments"`
}

// 知识库管理相关数据结构
type KnowledgeBase struct {
	ID           string                `json:"id"`
	Name         string                `json:"name"`
	Description  string                `json:"description"`
	Type         string                `json:"type"`
	Files        []KnowledgeBaseFile   `json:"files"`
	Settings     KnowledgeBaseSettings `json:"settings"`
	Status       string                `json:"status"`
	CreatedAt    string                `json:"createdAt"`
	UpdatedAt    string                `json:"updatedAt"`
	SegmentCount int                   `json:"segmentCount"`
}

type KnowledgeBaseFile struct {
	ID         string    `json:"id"`
	Name       string    `json:"name"`
	Size       int64     `json:"size"`
	Content    string    `json:"content"`
	Segments   []Segment `json:"segments"`
	Status     string    `json:"status"`
	Progress   int       `json:"progress"`
	Error      string    `json:"error,omitempty"`
	AIEnhanced bool      `json:"aiEnhanced"`
}

type KnowledgeBaseSettings struct {
	ParseText       bool   `json:"parseText"`
	ParseTable      bool   `json:"parseTable"`
	ContentFilter   string `json:"contentFilter"`
	SegmentStrategy string `json:"segmentStrategy"`
	SegmentLength   int    `json:"segmentLength"`
	SegmentOverlap  int    `json:"segmentOverlap"`
	Separator       string `json:"separator"`
	ExtractKeywords bool   `json:"extractKeywords"`
	GenerateSummary bool   `json:"generateSummary"`
	UseAI           bool   `json:"useAI"`
	FallbackToLocal bool   `json:"fallbackToLocal"`
}

type CreateKnowledgeBaseRequest struct {
	Name        string                `json:"name"`
	Description string                `json:"description"`
	Type        string                `json:"type"`
	Files       []KnowledgeBaseFile   `json:"files"`
	Settings    KnowledgeBaseSettings `json:"settings"`
}

type SaveKnowledgeBaseRequest struct {
	KnowledgeBase KnowledgeBase `json:"knowledgeBase"`
	Segments      []Segment     `json:"segments"`
}

type ExtractKeywordsRequest struct {
	Content string `json:"content"`
}

type ExtractKeywordsResponse struct {
	Keywords []string `json:"keywords"`
}

type GenerateSummaryRequest struct {
	Content string `json:"content"`
}

type GenerateSummaryResponse struct {
	Summary string `json:"summary"`
}

type BatchProcessRequest struct {
	Documents []struct {
		Name    string `json:"name"`
		Content string `json:"content"`
	} `json:"documents"`
	Settings struct {
		MaxLength int    `json:"maxLength"`
		Strategy  string `json:"strategy"`
	} `json:"settings"`
}

type BatchProcessResponse struct {
	Results []struct {
		Name     string    `json:"name"`
		Segments []Segment `json:"segments"`
		Status   string    `json:"status"`
		Error    string    `json:"error,omitempty"`
	} `json:"results"`
}

// 知识库搜索相关结构体
type KnowledgeSearchRequest struct {
	Query            string   `json:"query"`
	KnowledgeBaseIds []string `json:"knowledgeBaseIds"`
	MaxResults       int      `json:"maxResults"`
	MinScore         float64  `json:"minScore"`
	SearchStrategy   string   `json:"searchStrategy"` // 搜索策略: mixed/semantic/full_text
	QueryRewrite     bool     `json:"queryRewrite"`   // 查询改写
	ResultRerank     bool     `json:"resultRerank"`   // 结果重排
}

type KnowledgeSearchResult struct {
	ID              string   `json:"id"`
	Content         string   `json:"content"`
	Score           float64  `json:"score"`
	Source          string   `json:"source"`
	KnowledgeBaseId string   `json:"knowledgeBaseId"`
	Keywords        []string `json:"keywords"`
	Summary         string   `json:"summary"`
}

type KnowledgeSearchResponse struct {
	Results    []KnowledgeSearchResult `json:"results"`
	TotalCount int                     `json:"totalCount"`
	HasMore    bool                    `json:"hasMore"`
}

// 文档分析API
func AnalyzeDocument(c *gin.Context) {
	var req DocumentAnalysisRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	// 简单的文档分析逻辑
	analysis := DocumentAnalysisResponse{
		DocumentType:   "通用文档",
		StructureScore: 7,
		QualityScore:   7,
		Strategy:       "auto",
		MainTopics:     []string{"通用内容"},
	}

	// 根据内容长度和结构进行简单分析
	contentLength := len(req.Content)
	if contentLength > 5000 {
		analysis.QualityScore = 8
		analysis.StructureScore = 8
	}

	// 检查是否包含标题结构
	if strings.Contains(req.Content, "#") || strings.Contains(req.Content, "第") {
		analysis.DocumentType = "结构化文档"
		analysis.StructureScore = 9
		analysis.Strategy = "semantic"
	}

	c.JSON(http.StatusOK, analysis)
}

// 文档分段API
func SegmentDocument(c *gin.Context) {
	var req SegmentDocumentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	segments := segmentContent(req.Content, req.MaxLength, req.Strategy)

	response := SegmentDocumentResponse{
		Segments: segments,
	}

	c.JSON(http.StatusOK, response)
}

// 关键词提取API
func ExtractKeywords(c *gin.Context) {
	var req ExtractKeywordsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	keywords := extractKeywords(req.Content)

	response := ExtractKeywordsResponse{
		Keywords: keywords,
	}

	c.JSON(http.StatusOK, response)
}

// 摘要生成API
func GenerateSummary(c *gin.Context) {
	var req GenerateSummaryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	summary := generateSummary(req.Content)

	response := GenerateSummaryResponse{
		Summary: summary,
	}

	c.JSON(http.StatusOK, response)
}

// 批量处理API
func BatchProcessDocuments(c *gin.Context) {
	var req BatchProcessRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	var results []struct {
		Name     string    `json:"name"`
		Segments []Segment `json:"segments"`
		Status   string    `json:"status"`
		Error    string    `json:"error,omitempty"`
	}

	for _, doc := range req.Documents {
		segments := segmentContent(doc.Content, req.Settings.MaxLength, req.Settings.Strategy)

		results = append(results, struct {
			Name     string    `json:"name"`
			Segments []Segment `json:"segments"`
			Status   string    `json:"status"`
			Error    string    `json:"error,omitempty"`
		}{
			Name:     doc.Name,
			Segments: segments,
			Status:   "completed",
		})
	}

	response := BatchProcessResponse{
		Results: results,
	}

	c.JSON(http.StatusOK, response)
}

// 知识库搜索API
func SearchKnowledgeBase(c *gin.Context) {
	var req KnowledgeSearchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	// 设置默认值
	if req.SearchStrategy == "" {
		req.SearchStrategy = "mixed"
	}
	if req.MaxResults <= 0 {
		req.MaxResults = 5
	}
	if req.MinScore <= 0 {
		req.MinScore = 0.5
	}

	log.Printf("知识库搜索请求: 查询=%s, 策略=%s, 最大结果=%d, 最小分数=%.2f",
		req.Query, req.SearchStrategy, req.MaxResults, req.MinScore)

	// 根据搜索策略执行不同的搜索逻辑
	var results []KnowledgeSearchResult
	var err error

	switch req.SearchStrategy {
	case "semantic":
		results, err = searchWithSemanticStrategy(req)
	case "full_text":
		results, err = searchWithFullTextStrategy(req)
	case "mixed":
		results, err = searchWithMixedStrategy(req)
	default:
		results, err = searchWithMixedStrategy(req)
	}

	if err != nil {
		log.Printf("搜索失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "搜索失败: " + err.Error()})
		return
	}

	response := KnowledgeSearchResponse{
		Results:    results,
		TotalCount: len(results),
		HasMore:    false,
	}

	c.JSON(http.StatusOK, response)
}

// 知识库管理API
var knowledgeBases = make(map[string]KnowledgeBase)
var knowledgeBaseService *internetal.KnowledgeBaseService // 内存存储

// 创建知识库
func CreateKnowledgeBase(c *gin.Context) {
	var req CreateKnowledgeBaseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	// 生成知识库ID
	kbID := fmt.Sprintf("kb_%d", time.Now().Unix())

	// 计算总分段数
	totalSegments := 0
	for _, file := range req.Files {
		totalSegments += len(file.Segments)
	}

	kb := KnowledgeBase{
		ID:           kbID,
		Name:         req.Name,
		Description:  req.Description,
		Type:         req.Type,
		Files:        req.Files,
		Settings:     req.Settings,
		Status:       "completed",
		CreatedAt:    time.Now().Format("2006-01-02 15:04:05"),
		UpdatedAt:    time.Now().Format("2006-01-02 15:04:05"),
		SegmentCount: totalSegments,
	}
	// 将本地KnowledgeBase结构转换为internetal.KnowledgeBase
	internalKB := &internetal.KnowledgeBase{
		ID:           kb.ID,
		Name:         kb.Name,
		Description:  kb.Description,
		Type:         kb.Type,
		Status:       kb.Status,
		SegmentCount: kb.SegmentCount,
		Settings: &internetal.KnowledgeBaseSettings{
			KnowledgeBaseID: kb.ID,
			ParseText:       kb.Settings.ParseText,
			ParseTable:      kb.Settings.ParseTable,
			ContentFilter:   kb.Settings.ContentFilter,
			SegmentStrategy: kb.Settings.SegmentStrategy,
			SegmentLength:   kb.Settings.SegmentLength,
			SegmentOverlap:  kb.Settings.SegmentOverlap,
			Separator:       kb.Settings.Separator,
			ExtractKeywords: kb.Settings.ExtractKeywords,
			GenerateSummary: kb.Settings.GenerateSummary,
			UseAI:           kb.Settings.UseAI,
			FallbackToLocal: kb.Settings.FallbackToLocal,
		},
	}

	// 使用知识库服务保存到数据库
	if err := knowledgeBaseService.CreateKnowledgeBase(internalKB); err != nil {
		log.Printf("保存知识库到数据库失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  "保存知识库失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "知识库创建成功",
		"data": kb,
	})
}

func SaveKnowledgeBase(c *gin.Context) {
	var req SaveKnowledgeBaseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	// 更新知识库信息
	kb := req.KnowledgeBase
	kb.UpdatedAt = time.Now().Format("2006-01-02 15:04:05")

	// 计算总分段数
	totalSegments := 0
	for _, file := range kb.Files {
		totalSegments += len(file.Segments)
	}

	// 如果有额外的分段数据，也要计算进去
	if len(req.Segments) > 0 {
		totalSegments += len(req.Segments)
	}

	kb.SegmentCount = totalSegments

	// 将本地KnowledgeBase结构转换为internetal.KnowledgeBase
	internalKB := &internetal.KnowledgeBase{
		ID:           kb.ID,
		Name:         kb.Name,
		Description:  kb.Description,
		Type:         kb.Type,
		Status:       kb.Status,
		SegmentCount: kb.SegmentCount,
		Settings: &internetal.KnowledgeBaseSettings{
			KnowledgeBaseID: kb.ID,
			ParseText:       kb.Settings.ParseText,
			ParseTable:      kb.Settings.ParseTable,
			ContentFilter:   kb.Settings.ContentFilter,
			SegmentStrategy: kb.Settings.SegmentStrategy,
			SegmentLength:   kb.Settings.SegmentLength,
			SegmentOverlap:  kb.Settings.SegmentOverlap,
			Separator:       kb.Settings.Separator,
			ExtractKeywords: kb.Settings.ExtractKeywords,
			GenerateSummary: kb.Settings.GenerateSummary,
			UseAI:           kb.Settings.UseAI,
			FallbackToLocal: kb.Settings.FallbackToLocal,
		},
	}

	// 使用知识库服务更新到数据库
	if err := knowledgeBaseService.UpdateKnowledgeBase(internalKB); err != nil {
		log.Printf("更新知识库到数据库失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  "更新知识库失败: " + err.Error(),
		})
		return
	}

	// 返回更新后的知识库信息
	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "知识库保存成功",
		"data": gin.H{
			"id":           kb.ID,
			"name":         kb.Name,
			"description":  kb.Description,
			"segmentCount": kb.SegmentCount,
			"status":       kb.Status,
			"updatedAt":    kb.UpdatedAt,
		},
	})
}

func ListKnowledgeBases(c *gin.Context) {
	// 从数据库获取知识库列表
	kbList, err := knowledgeBaseService.ListKnowledgeBases()
	if err != nil {
		log.Printf("获取知识库列表失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  "获取知识库列表失败: " + err.Error(),
			"data": []interface{}{},
		})
		return
	}

	// 如果没有知识库，创建一些示例数据
	if len(kbList) == 0 {
		err := createSampleKnowledgeBases()
		if err != nil {
			log.Printf("创建示例知识库失败: %v", err)
		} else {
			// 重新获取列表
			kbList, _ = knowledgeBaseService.ListKnowledgeBases()
		}
	}

	// 确保返回的数据不为null
	if kbList == nil {
		kbList = []internetal.KnowledgeBase{}
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "获取成功",
		"data": kbList,
	})
}

// 获取知识库详细信息
func GetKnowledgeBaseDetail(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "知识库ID不能为空",
		})
		return
	}

	// 从数据库获取知识库详细信息
	kb, err := knowledgeBaseService.GetKnowledgeBaseByID(id)
	if err != nil {
		log.Printf("获取知识库详细信息失败: %v", err)
		c.JSON(http.StatusNotFound, gin.H{
			"code": 404,
			"msg":  "知识库不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "获取成功",
		"data": kb,
	})
}

// 创建示例知识库数据
func createSampleKnowledgeBases() error {
	// 检查示例知识库是否已存在，如果存在则删除重新创建
	existingKB1, _ := knowledgeBaseService.GetKnowledgeBaseByID("kb_sample_1")
	if existingKB1 != nil {
		log.Printf("示例知识库1已存在，删除后重新创建")
		knowledgeBaseService.DeleteKnowledgeBase("kb_sample_1")
	}

	existingKB2, _ := knowledgeBaseService.GetKnowledgeBaseByID("kb_sample_2")
	if existingKB2 != nil {
		log.Printf("示例知识库2已存在，删除后重新创建")
		knowledgeBaseService.DeleteKnowledgeBase("kb_sample_2")
	}

	// 创建示例知识库1
	sampleKB1 := &internetal.KnowledgeBase{
		ID:           "kb_sample_1",
		Name:         "Go语言完整教程",
		Description:  "Go语言从入门到精通的完整学习资料",
		Type:         "text",
		Status:       "completed",
		SegmentCount: 3,
		Settings: &internetal.KnowledgeBaseSettings{
			KnowledgeBaseID: "kb_sample_1",
			ParseText:       true,
			ParseTable:      false,
			ContentFilter:   "",
			SegmentStrategy: "auto",
			SegmentLength:   1000,
			SegmentOverlap:  100,
			Separator:       "",
			ExtractKeywords: true,
			GenerateSummary: false,
			UseAI:           true,
			FallbackToLocal: true,
		},
		Files: []internetal.KnowledgeBaseFile{
			{
				ID:              "file_1",
				KnowledgeBaseID: "kb_sample_1",
				Name:            "go_basics.md",
				Size:            1024,
				Content:         "Go语言基础教程内容...",
				Status:          "completed",
				Progress:        100,
				AIEnhanced:      true,
				Segments: []internetal.KnowledgeSegment{
					{
						ID:              "seg_1",
						FileID:          "file_1",
						KnowledgeBaseID: "kb_sample_1",
						Content:         "Go是Google开发的编程语言，具有简洁的语法和强大的性能。Go语言设计目标是提高程序员的开发效率，同时保证程序的运行效率。",
						Summary:         "Go语言简介",
						Length:          60,
						Position:        1,
					},
					{
						ID:              "seg_2",
						FileID:          "file_1",
						KnowledgeBaseID: "kb_sample_1",
						Content:         "Go具有垃圾回收和并发特性，支持goroutine和channel，使得并发编程变得简单而高效。这些特性让Go特别适合构建高并发的网络服务。",
						Summary:         "Go语言特性介绍",
						Length:          70,
						Position:        2,
					},
					{
						ID:              "seg_3",
						FileID:          "file_1",
						KnowledgeBaseID: "kb_sample_1",
						Content:         "Go适合构建高性能的网络服务、微服务架构、云原生应用等。许多知名公司如Docker、Kubernetes都使用Go语言开发。",
						Summary:         "Go语言应用场景",
						Length:          65,
						Position:        3,
					},
				},
			},
		},
	}

	// 创建示例知识库2
	sampleKB2 := &internetal.KnowledgeBase{
		ID:           "kb_sample_2",
		Name:         "Vue.js文档",
		Description:  "Vue.js框架学习资料",
		Type:         "text",
		Status:       "completed",
		SegmentCount: 2,
		Settings: &internetal.KnowledgeBaseSettings{
			KnowledgeBaseID: "kb_sample_2",
			ParseText:       true,
			ParseTable:      false,
			ContentFilter:   "",
			SegmentStrategy: "auto",
			SegmentLength:   1000,
			SegmentOverlap:  100,
			Separator:       "",
			ExtractKeywords: true,
			GenerateSummary: false,
			UseAI:           true,
			FallbackToLocal: true,
		},
		Files: []internetal.KnowledgeBaseFile{
			{
				ID:              "file_2",
				KnowledgeBaseID: "kb_sample_2",
				Name:            "vue_guide.md",
				Size:            2048,
				Content:         "Vue.js框架指南内容...",
				Status:          "completed",
				Progress:        100,
				AIEnhanced:      true,
				Segments: []internetal.KnowledgeSegment{
					{
						ID:              "seg_4",
						FileID:          "file_2",
						KnowledgeBaseID: "kb_sample_2",
						Content:         "Vue.js是渐进式JavaScript框架，可以自底向上逐层应用。Vue的核心库只关注视图层，易于上手，便于与第三方库或既有项目整合。",
						Summary:         "Vue.js框架介绍",
						Length:          75,
						Position:        1,
					},
					{
						ID:              "seg_5",
						FileID:          "file_2",
						KnowledgeBaseID: "kb_sample_2",
						Content:         "Vue.js具有响应式数据绑定特性，当数据发生变化时，视图会自动更新。Vue还提供了组件化开发模式，提高了代码的可维护性和复用性。",
						Summary:         "Vue.js响应式特性",
						Length:          80,
						Position:        2,
					},
				},
			},
		},
	}

	// 设置关键词
	sampleKB1.Files[0].Segments[0].SetKeywords([]string{"Go", "Google", "编程语言", "性能"})
	sampleKB1.Files[0].Segments[1].SetKeywords([]string{"垃圾回收", "并发", "goroutine", "channel"})
	sampleKB1.Files[0].Segments[2].SetKeywords([]string{"高性能", "网络服务", "微服务", "云原生"})

	sampleKB2.Files[0].Segments[0].SetKeywords([]string{"Vue.js", "渐进式", "JavaScript", "框架"})
	sampleKB2.Files[0].Segments[1].SetKeywords([]string{"响应式", "数据绑定", "组件化", "可维护性"})

	// 保存到数据库
	if err := knowledgeBaseService.CreateKnowledgeBase(sampleKB1); err != nil {
		return fmt.Errorf("创建示例知识库1失败: %v", err)
	}

	if err := knowledgeBaseService.CreateKnowledgeBase(sampleKB2); err != nil {
		return fmt.Errorf("创建示例知识库2失败: %v", err)
	}

	// 设置关键词（在创建知识库时已经包含分段数据）
	log.Printf("示例知识库创建完成，包含分段数据")

	return nil
}

// 更新知识库基本信息
func UpdateKnowledgeBase(c *gin.Context) {
	var req struct {
		ID          string `json:"id"`
		Name        string `json:"name"`
		Description string `json:"description"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "请求格式错误",
		})
		return
	}

	if req.ID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "知识库ID不能为空",
		})
		return
	}

	if req.Name == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "知识库名称不能为空",
		})
		return
	}

	// 获取现有知识库
	kb, err := knowledgeBaseService.GetKnowledgeBaseByID(req.ID)
	if err != nil {
		log.Printf("获取知识库失败: %v", err)
		c.JSON(http.StatusNotFound, gin.H{
			"code": 404,
			"msg":  "知识库不存在",
		})
		return
	}

	// 更新基本信息
	kb.Name = req.Name
	kb.Description = req.Description

	// 保存更新
	if err := knowledgeBaseService.UpdateKnowledgeBase(kb); err != nil {
		log.Printf("更新知识库失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  "更新失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "更新成功",
		"data": kb,
	})
}

// 删除知识库
func DeleteKnowledgeBase(c *gin.Context) {
	var req struct {
		ID string `json:"id"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	// 检查知识库是否存在
	_, err := knowledgeBaseService.GetKnowledgeBaseByID(req.ID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code": 404,
			"msg":  "知识库不存在",
		})
		return
	}

	// 删除知识库
	if err := knowledgeBaseService.DeleteKnowledgeBase(req.ID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  "删除知识库失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "知识库删除成功",
	})
}

// 辅助函数：内容分段
func segmentContent(content string, maxLength int, strategy string) []Segment {
	var segments []Segment

	if maxLength <= 0 {
		maxLength = 1000
	}

	// 预处理：清理内容
	content = strings.TrimSpace(content)
	if len(content) == 0 {
		return segments
	}

	switch strategy {
	case "ai":
		// AI增强分段
		return aiEnhancedSegmentation(content, maxLength)
	case "semantic":
		// 按段落分段（支持中文段落）
		paragraphs := strings.Split(content, "\n\n")
		if len(paragraphs) == 1 {
			// 如果没有双换行，尝试单换行
			paragraphs = strings.Split(content, "\n")
		}
		segmentID := 1

		for _, paragraph := range paragraphs {
			paragraph = strings.TrimSpace(paragraph)
			if len(paragraph) == 0 {
				continue
			}

			// 使用字符数而不是字节数来判断长度
			runeCount := len([]rune(paragraph))
			if runeCount <= maxLength {
				segment := Segment{
					ID:       fmt.Sprintf("segment_%d", segmentID),
					Content:  paragraph,
					Keywords: extractKeywords(paragraph),
					Summary:  generateSummary(paragraph),
					Length:   runeCount,
				}
				segments = append(segments, segment)
				segmentID++
			} else {
				// 长段落需要进一步分割
				subSegments := splitLongText(paragraph, maxLength, segmentID)
				segments = append(segments, subSegments...)
				segmentID += len(subSegments)
			}
		}
	case "fixed":
		// 固定长度分段（按字符数）
		runes := []rune(content)
		segmentID := 1

		for i := 0; i < len(runes); i += maxLength {
			end := i + maxLength
			if end > len(runes) {
				end = len(runes)
			}

			segmentText := string(runes[i:end])
			segment := Segment{
				ID:       fmt.Sprintf("segment_%d", segmentID),
				Content:  segmentText,
				Keywords: extractKeywords(segmentText),
				Summary:  generateSummary(segmentText),
				Length:   len([]rune(segmentText)),
			}
			segments = append(segments, segment)
			segmentID++
		}
	default: // "auto"
		// 自动分段：优先尝试AI分段，失败则回退到传统方法
		if len(content) > 500 { // 对于较长的文本，尝试AI分段
			aiSegments := aiEnhancedSegmentation(content, maxLength)
			if len(aiSegments) > 0 {
				return aiSegments
			}
		}

		// 如果AI分段失败或文本较短，使用传统分段
		return traditionalSegmentation(content, maxLength)
	}

	return segments
}

// 传统分段函数（避免递归调用）
func traditionalSegmentation(content string, maxLength int) []Segment {
	var segments []Segment

	// 传统自动分段：优先按段落，超长则分割（支持中文）
	paragraphs := strings.Split(content, "\n")
	segmentID := 1
	currentSegment := ""

	for _, paragraph := range paragraphs {
		paragraph = strings.TrimSpace(paragraph)
		if len(paragraph) == 0 {
			continue
		}

		// 计算当前分段和新段落的字符数
		currentRuneCount := len([]rune(currentSegment))
		paragraphRuneCount := len([]rune(paragraph))

		if currentRuneCount+paragraphRuneCount+1 <= maxLength {
			if currentSegment != "" {
				currentSegment += "\n"
			}
			currentSegment += paragraph
		} else {
			if currentSegment != "" {
				segment := Segment{
					ID:       fmt.Sprintf("segment_%d", segmentID),
					Content:  currentSegment,
					Keywords: extractKeywords(currentSegment),
					Summary:  generateSummary(currentSegment),
					Length:   len([]rune(currentSegment)),
				}
				segments = append(segments, segment)
				segmentID++
			}

			// 如果单个段落太长，需要分割
			if paragraphRuneCount > maxLength {
				subSegments := splitLongText(paragraph, maxLength, segmentID)
				segments = append(segments, subSegments...)
				segmentID += len(subSegments)
				currentSegment = ""
			} else {
				currentSegment = paragraph
			}
		}
	}

	// 处理最后一个分段
	if currentSegment != "" {
		segment := Segment{
			ID:       fmt.Sprintf("segment_%d", segmentID),
			Content:  currentSegment,
			Keywords: extractKeywords(currentSegment),
			Summary:  generateSummary(currentSegment),
			Length:   len([]rune(currentSegment)),
		}
		segments = append(segments, segment)
	}

	return segments
}

// AI增强分段函数
func aiEnhancedSegmentation(content string, maxLength int) []Segment {
	// 首先尝试使用AI进行智能分段
	aiSegments, err := callAIForSegmentation(content, maxLength)
	if err != nil {
		// AI分段失败，回退到自动分段
		fmt.Printf("AI分段失败，回退到自动分段: %v\n", err)
		return segmentContent(content, maxLength, "auto")
	}

	// 对AI分段结果进行后处理
	var processedSegments []Segment
	for i, segment := range aiSegments {
		processedSegment := Segment{
			ID:       fmt.Sprintf("ai_segment_%d", i+1),
			Content:  segment.Content,
			Keywords: extractKeywords(segment.Content),
			Summary:  generateSummary(segment.Content),
			Length:   len([]rune(segment.Content)),
		}
		processedSegments = append(processedSegments, processedSegment)
	}

	return processedSegments
}

// 调用AI进行分段
// 调用AI进行分段
func callAIForSegmentation(content string, maxLength int) ([]Segment, error) {
	// 构建AI分段请求，使用markdown格式
	prompt := fmt.Sprintf(`请将以下文本进行智能分段，每个分段不超过%d个字符。

## 分段要求：
1. **保持语义完整性** - 确保每个分段都有完整的意思
2. **按照逻辑结构分段** - 根据内容的逻辑关系进行分段
3. **确保每个分段都有明确的主题** - 每段应该围绕一个核心主题
4. **返回markdown格式** - 使用清晰的markdown格式组织内容

## 文本内容：
%s

## 返回格式要求：
请返回JSON格式，包含segments数组，每个元素有content字段：

`+"```json"+`
{
  "segments": [
    {"content": "第一段内容..."},
    {"content": "第二段内容..."}
  ]
}
`+"```"+`

请确保返回的内容格式工整，便于阅读。`, maxLength, content)

	// 调用DashScope API
	apiKey := "sk-0baf684faf044823a593986c15617b12"
	requestBody := map[string]interface{}{
		"model": "qwen-turbo",
		"input": map[string]interface{}{
			"messages": []map[string]interface{}{
				{
					"role":    "user",
					"content": prompt,
				},
			},
		},
		"parameters": map[string]interface{}{
			"result_format": "message",
		},
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %v", err)
	}

	req, err := http.NewRequest("POST", "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Authorization", "Bearer "+apiKey)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("API调用失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API返回错误状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 解析AI响应
	var aiResponse struct {
		Output struct {
			Text string `json:"text"`
		} `json:"output"`
	}

	if err := json.Unmarshal(body, &aiResponse); err != nil {
		return nil, fmt.Errorf("解析AI响应失败: %v", err)
	}

	// 尝试解析AI返回的JSON分段结果
	var segmentResult struct {
		Segments []struct {
			Content string `json:"content"`
		} `json:"segments"`
	}

	// 提取JSON部分
	jsonMatch := extractJSONFromResponse(aiResponse.Output.Text)
	if jsonMatch != "" {
		if err := json.Unmarshal([]byte(jsonMatch), &segmentResult); err == nil {
			// 转换为标准Segment格式
			var segments []Segment
			for i, seg := range segmentResult.Segments {
				segments = append(segments, Segment{
					ID:      fmt.Sprintf("ai_segment_%d", i+1),
					Content: seg.Content,
					Length:  len([]rune(seg.Content)),
				})
			}
			return segments, nil
		}
	}

	// 如果AI没有返回标准JSON格式，手动解析文本
	return parseAITextResponse(aiResponse.Output.Text, maxLength), nil
}

// 从AI响应中提取JSON内容
func extractJSONFromResponse(response string) string {
	// 尝试匹配JSON代码块
	jsonBlockRegex := regexp.MustCompile("```json\\s*([\\s\\S]*?)\\s*```")
	if matches := jsonBlockRegex.FindStringSubmatch(response); len(matches) > 1 {
		return strings.TrimSpace(matches[1])
	}

	// 尝试匹配普通JSON
	jsonRegex := regexp.MustCompile("\\{[\\s\\S]*\\}")
	if match := jsonRegex.FindString(response); match != "" {
		return match
	}

	return ""
}

// 解析AI文本响应
func parseAITextResponse(text string, maxLength int) []Segment {
	// 如果AI没有返回JSON格式，尝试按段落分割
	lines := strings.Split(text, "\n")
	var segments []Segment
	var currentSegment strings.Builder

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if len(line) == 0 {
			continue
		}

		// 如果当前分段加上新行会超过最大长度，创建新分段
		if currentSegment.Len() > 0 && len([]rune(currentSegment.String()+"\n"+line)) > maxLength {
			segments = append(segments, Segment{
				Content: currentSegment.String(),
			})
			currentSegment.Reset()
		}

		if currentSegment.Len() > 0 {
			currentSegment.WriteString("\n")
		}
		currentSegment.WriteString(line)
	}

	// 添加最后一个分段
	if currentSegment.Len() > 0 {
		segments = append(segments, Segment{
			Content: currentSegment.String(),
		})
	}

	return segments
}

// 辅助函数：分割长文本（支持中文）
func splitLongText(text string, maxLength int, startID int) []Segment {
	var segments []Segment
	segmentID := startID
	runes := []rune(text)

	for i := 0; i < len(runes); i += maxLength {
		end := i + maxLength
		if end > len(runes) {
			end = len(runes)
		}

		segmentText := string(runes[i:end])
		segment := Segment{
			ID:       fmt.Sprintf("segment_%d", segmentID),
			Content:  segmentText,
			Keywords: extractKeywords(segmentText),
			Summary:  generateSummary(segmentText),
			Length:   len([]rune(segmentText)),
		}
		segments = append(segments, segment)
		segmentID++
	}

	return segments
}

// 辅助函数：关键词提取
func extractKeywords(content string) []string {
	// 改进的关键词提取逻辑
	content = strings.TrimSpace(content)
	if len(content) == 0 {
		return []string{}
	}

	// 分割文本为词语（支持中文）
	words := strings.FieldsFunc(content, func(r rune) bool {
		return r == ' ' || r == '\n' || r == '\t' || r == '，' || r == '。' || r == '、' || r == '；'
	})

	wordCount := make(map[string]int)

	// 扩展的停用词列表
	stopWords := map[string]bool{
		"的": true, "了": true, "在": true, "是": true, "我": true, "有": true, "和": true,
		"就": true, "不": true, "人": true, "都": true, "一": true, "一个": true, "上": true,
		"也": true, "很": true, "到": true, "说": true, "要": true, "去": true, "你": true,
		"会": true, "着": true, "没有": true, "看": true, "好": true, "自己": true, "这": true,
		"与": true, "或": true, "等": true, "可以": true, "通过": true, "使用": true, "包括": true,
		"还": true, "为": true, "其": true, "中": true, "时": true, "将": true, "被": true,
	}

	// 统计词频
	for _, word := range words {
		word = strings.TrimSpace(word)
		// 过滤掉太短的词和停用词
		if len([]rune(word)) >= 2 && !stopWords[word] {
			wordCount[word]++
		}
	}

	// 按频率排序
	type wordFreq struct {
		word  string
		count int
	}

	var wordFreqs []wordFreq
	for word, count := range wordCount {
		wordFreqs = append(wordFreqs, wordFreq{word, count})
	}

	// 简单排序（按频率降序）
	for i := 0; i < len(wordFreqs)-1; i++ {
		for j := i + 1; j < len(wordFreqs); j++ {
			if wordFreqs[i].count < wordFreqs[j].count {
				wordFreqs[i], wordFreqs[j] = wordFreqs[j], wordFreqs[i]
			}
		}
	}

	// 返回前5个关键词
	var keywords []string
	maxKeywords := 5
	if len(wordFreqs) < maxKeywords {
		maxKeywords = len(wordFreqs)
	}

	for i := 0; i < maxKeywords; i++ {
		keywords = append(keywords, wordFreqs[i].word)
	}

	return keywords
}

// 辅助函数：生成摘要
func generateSummary(content string) string {
	// 简单的摘要生成逻辑
	sentences := strings.Split(content, "。")
	if len(sentences) == 0 {
		return content
	}

	// 如果内容很短，直接返回
	if len(content) <= 100 {
		return content
	}

	// 选择前两个句子作为摘要
	summary := ""
	sentenceCount := 0
	maxSentences := 2

	for _, sentence := range sentences {
		sentence = strings.TrimSpace(sentence)
		if len(sentence) > 10 && sentenceCount < maxSentences {
			if summary != "" {
				summary += "。"
			}
			summary += sentence
			sentenceCount++
		}
	}

	if summary != "" && !strings.HasSuffix(summary, "。") {
		summary += "。"
	}

	// 如果摘要太长，截断
	if len(summary) > 200 {
		summary = summary[:200] + "..."
	}

	return summary
}

// 知识库搜索函数 - 使用阿里云百炼文本向量化API
// 通过向量相似度搜索知识库内容
func searchKnowledgeBaseContent(query string, knowledgeBaseIds []string, maxResults int, minScore float64) []KnowledgeSearchResult {
	// 阿里云百炼配置
	apiKey := "sk-0baf684faf044823a593986c15617b12"

	// 您的知识库ID (用于标识不同的知识库)
	if len(knowledgeBaseIds) == 0 {
		knowledgeBaseIds = []string{
			"gxl6aa98hx", // react知识库
			"d3hxntlfia", // go知识库
			"w85a9w1b43", // vue知识库
		}
	}

	log.Printf("知识库向量搜索查询: %s", query)
	log.Printf("目标知识库: %v", knowledgeBaseIds)

	// 使用文本向量化API进行知识库搜索
	results, err := searchKnowledgeBaseWithEmbedding(apiKey, query, knowledgeBaseIds, maxResults, minScore)
	if err != nil {
		log.Printf("向量搜索失败: %v", err)
		// 如果API调用失败，返回空结果
		return []KnowledgeSearchResult{}
	}

	return results
}

// 知识库文档数据结构
type KnowledgeDocument struct {
	ID              string    `json:"id"`
	Title           string    `json:"title"`
	Content         string    `json:"content"`
	KnowledgeBaseId string    `json:"knowledge_base_id"`
	Keywords        []string  `json:"keywords"`
	Category        string    `json:"category"`
	Embedding       []float64 `json:"embedding,omitempty"` // 文档的向量表示
}

// 模拟知识库文档数据 (实际使用时应该从数据库或文件中加载)
var knowledgeDocuments = []KnowledgeDocument{
	{
		ID:              "react_001",
		Title:           "React组件基础",
		Content:         "React组件是构建用户界面的基本单元。组件可以是函数组件或类组件。函数组件是一个简单的JavaScript函数，接收props作为参数并返回JSX。类组件是ES6类，继承自React.Component。",
		KnowledgeBaseId: "gxl6aa98hx",
		Keywords:        []string{"React", "组件", "函数组件", "类组件", "JSX", "props"},
		Category:        "前端开发",
	},
	{
		ID:              "react_002",
		Title:           "React Hooks使用",
		Content:         "React Hooks是React 16.8引入的新特性，允许在函数组件中使用状态和其他React特性。常用的Hooks包括useState、useEffect、useContext等。useState用于在函数组件中添加状态，useEffect用于处理副作用。",
		KnowledgeBaseId: "gxl6aa98hx",
		Keywords:        []string{"React", "Hooks", "useState", "useEffect", "函数组件", "状态管理"},
		Category:        "前端开发",
	},
	{
		ID:              "go_001",
		Title:           "Go语言基础语法",
		Content:         "Go语言是Google开发的编程语言，具有简洁的语法和高效的性能。Go语言支持并发编程，通过goroutine和channel实现。变量声明使用var关键字，函数定义使用func关键字。",
		KnowledgeBaseId: "d3hxntlfia",
		Keywords:        []string{"Go", "语法", "并发", "goroutine", "channel", "变量", "函数"},
		Category:        "后端开发",
	},
	{
		ID:              "go_002",
		Title:           "Go并发编程",
		Content:         "Go语言的并发模型基于CSP（Communicating Sequential Processes）。Goroutine是轻量级线程，可以同时运行数千个。Channel用于goroutine之间的通信，遵循'不要通过共享内存来通信，而要通过通信来共享内存'的原则。",
		KnowledgeBaseId: "d3hxntlfia",
		Keywords:        []string{"Go", "并发", "CSP", "goroutine", "channel", "通信", "内存"},
		Category:        "后端开发",
	},
	{
		ID:              "vue_001",
		Title:           "Vue.js响应式原理",
		Content:         "Vue.js的响应式系统是其核心特性之一。Vue 3使用Proxy实现响应式，Vue 2使用Object.defineProperty。当数据发生变化时，视图会自动更新。响应式原理包括数据劫持、依赖收集和派发更新。",
		KnowledgeBaseId: "w85a9w1b43",
		Keywords:        []string{"Vue", "响应式", "Proxy", "数据劫持", "依赖收集", "视图更新"},
		Category:        "前端开发",
	},
	{
		ID:              "vue_002",
		Title:           "Vue组件通信",
		Content:         "Vue组件间通信有多种方式：父子组件通过props和emit通信，兄弟组件可以通过事件总线或Vuex，跨层级组件可以使用provide/inject。Vue 3还提供了Composition API，使组件逻辑更容易复用。",
		KnowledgeBaseId: "w85a9w1b43",
		Keywords:        []string{"Vue", "组件通信", "props", "emit", "Vuex", "provide", "inject", "Composition API"},
		Category:        "前端开发",
	},
}

// 使用文本向量化API进行知识库搜索
func searchKnowledgeBaseWithEmbedding(apiKey, query string, knowledgeBaseIds []string, maxResults int, minScore float64) ([]KnowledgeSearchResult, error) {
	// 1. 获取查询文本的向量
	queryEmbedding, err := getTextEmbedding(apiKey, query)
	if err != nil {
		return nil, fmt.Errorf("获取查询向量失败: %v", err)
	}

	// 2. 确保所有文档都有向量表示
	err = ensureDocumentEmbeddings(apiKey)
	if err != nil {
		log.Printf("更新文档向量时出错: %v", err)
		// 继续执行，使用现有向量或文本匹配
	}

	// 3. 计算相似度并排序
	var candidates []struct {
		doc   KnowledgeDocument
		score float64
	}

	for _, doc := range knowledgeDocuments {
		// 如果指定了知识库ID，只搜索指定的知识库
		if len(knowledgeBaseIds) > 0 {
			found := false
			for _, kbId := range knowledgeBaseIds {
				if doc.KnowledgeBaseId == kbId {
					found = true
					break
				}
			}
			if !found {
				continue
			}
		}

		var score float64
		if len(doc.Embedding) > 0 {
			// 使用向量相似度
			score = cosineSimilarity(queryEmbedding, doc.Embedding)
		} else {
			// 回退到文本匹配
			score = calculateTextSimilarity(query, doc.Content, doc.Keywords)
		}

		if score >= minScore {
			candidates = append(candidates, struct {
				doc   KnowledgeDocument
				score float64
			}{doc, score})
		}
	}

	// 按分数排序
	for i := 0; i < len(candidates)-1; i++ {
		for j := i + 1; j < len(candidates); j++ {
			if candidates[i].score < candidates[j].score {
				candidates[i], candidates[j] = candidates[j], candidates[i]
			}
		}
	}

	// 限制结果数量
	if len(candidates) > maxResults {
		candidates = candidates[:maxResults]
	}

	// 转换为搜索结果
	var results []KnowledgeSearchResult
	for _, candidate := range candidates {
		result := KnowledgeSearchResult{
			ID:              candidate.doc.ID,
			Content:         candidate.doc.Content,
			Score:           candidate.score,
			Source:          candidate.doc.Title,
			KnowledgeBaseId: candidate.doc.KnowledgeBaseId,
			Keywords:        candidate.doc.Keywords,
			Summary:         fmt.Sprintf("%s - %s", candidate.doc.Category, candidate.doc.Title),
		}
		results = append(results, result)
	}

	log.Printf("向量搜索完成，找到 %d 个相关结果", len(results))
	return results, nil
}

// 获取文本的向量表示
func getTextEmbedding(apiKey, text string) ([]float64, error) {
	// 构建请求体
	requestBody := map[string]interface{}{
		"model": "text-embedding-v4",
		"input": map[string]interface{}{
			"texts": []string{text},
		},
		"parameters": map[string]interface{}{
			"dimension": 1024, // 使用1024维向量
		},
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("序列化请求体失败: %v", err)
	}

	// 创建HTTP请求
	url := "https://dashscope.aliyuncs.com/api/v1/services/embeddings/text-embedding/text-embedding"
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Authorization", "Bearer "+apiKey)
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 检查响应状态
	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("API调用失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var response map[string]interface{}
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	// 提取向量
	if output, ok := response["output"].(map[string]interface{}); ok {
		if embeddings, ok := output["embeddings"].([]interface{}); ok && len(embeddings) > 0 {
			if embedding, ok := embeddings[0].(map[string]interface{}); ok {
				if embeddingArray, ok := embedding["embedding"].([]interface{}); ok {
					var result []float64
					for _, val := range embeddingArray {
						if floatVal, ok := val.(float64); ok {
							result = append(result, floatVal)
						}
					}
					return result, nil
				}
			}
		}
	}

	return nil, fmt.Errorf("无法从响应中提取向量")
}

// 确保所有文档都有向量表示
func ensureDocumentEmbeddings(apiKey string) error {
	for i := range knowledgeDocuments {
		if len(knowledgeDocuments[i].Embedding) == 0 {
			log.Printf("为文档 %s 生成向量...", knowledgeDocuments[i].ID)
			embedding, err := getTextEmbedding(apiKey, knowledgeDocuments[i].Content)
			if err != nil {
				log.Printf("为文档 %s 生成向量失败: %v", knowledgeDocuments[i].ID, err)
				continue
			}
			knowledgeDocuments[i].Embedding = embedding
			// 避免请求过于频繁
			time.Sleep(100 * time.Millisecond)
		}
	}
	return nil
}

// 计算两个向量的余弦相似度
func cosineSimilarity(a, b []float64) float64 {
	if len(a) != len(b) {
		return 0.0
	}

	var dotProduct, normA, normB float64
	for i := 0; i < len(a); i++ {
		dotProduct += a[i] * b[i]
		normA += a[i] * a[i]
		normB += b[i] * b[i]
	}

	if normA == 0 || normB == 0 {
		return 0.0
	}

	return dotProduct / (math.Sqrt(normA) * math.Sqrt(normB))
}

// 计算文本相似度 (回退方案)
func calculateTextSimilarity(query, content string, keywords []string) float64 {
	queryLower := strings.ToLower(query)
	contentLower := strings.ToLower(content)
	score := 0.0

	// 内容匹配
	if strings.Contains(contentLower, queryLower) {
		score += 0.6
	}

	// 关键词匹配
	for _, keyword := range keywords {
		if strings.Contains(strings.ToLower(keyword), queryLower) || strings.Contains(queryLower, strings.ToLower(keyword)) {
			score += 0.3
		}
	}

	// 部分词匹配
	queryWords := strings.Fields(queryLower)
	for _, word := range queryWords {
		if len(word) > 1 && strings.Contains(contentLower, word) {
			score += 0.1
		}
	}

	// 确保分数不超过1.0
	if score > 1.0 {
		score = 1.0
	}

	return score
}
func callBailianApplication(apiKey, appId, query string) ([]KnowledgeSearchResult, error) {
	// 构建请求体 - 使用应用调用格式
	requestBody := map[string]interface{}{
		"input": map[string]interface{}{
			"prompt": query,
		},
		"parameters": map[string]interface{}{},
		"debug":      map[string]interface{}{},
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("序列化请求体失败: %v", err)
	}

	// 创建HTTP请求 - 使用应用调用端点
	url := fmt.Sprintf("https://dashscope.aliyuncs.com/api/v1/apps/%s/completion", appId)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Authorization", "Bearer "+apiKey)
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	log.Printf("百炼应用响应状态码: %d", resp.StatusCode)
	log.Printf("百炼应用响应内容: %s", string(body))

	// 检查响应状态
	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("应用调用失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var response map[string]interface{}
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	// 解析应用响应并转换为知识库搜索结果
	return parseApplicationResponse(response, query)
}

// 解析百炼应用响应并转换为知识库搜索结果
func parseApplicationResponse(response map[string]interface{}, query string) ([]KnowledgeSearchResult, error) {
	var results []KnowledgeSearchResult

	// 应用调用的响应格式
	if output, ok := response["output"].(map[string]interface{}); ok {
		// 获取应用返回的文本内容
		text := getStringValue(output, "text")
		sessionId := getStringValue(output, "session_id")

		// 如果应用返回了内容，说明知识库搜索成功
		if text != "" {
			result := KnowledgeSearchResult{
				ID:              fmt.Sprintf("bailian_app_%s", sessionId),
				Content:         text,
				Score:           0.9, // 应用调用的结果给高分
				Source:          "百炼知识库应用",
				KnowledgeBaseId: "app_integrated_kb",
				Keywords:        []string{query},
				Summary:         fmt.Sprintf("通过百炼应用从知识库检索到关于'%s'的内容", query),
			}
			results = append(results, result)
		}
	}

	return results, nil
}

// 辅助函数：安全获取字符串值
func getStringValue(m map[string]interface{}, key string) string {
	if val, ok := m[key]; ok {
		if str, ok := val.(string); ok {
			return str
		}
	}
	return ""
}

// 语义搜索策略 - 使用向量相似度
func searchWithSemanticStrategy(req KnowledgeSearchRequest) ([]KnowledgeSearchResult, error) {
	log.Printf("执行语义搜索策略")

	// 查询改写
	query := req.Query
	if req.QueryRewrite {
		query = rewriteQuery(req.Query)
		log.Printf("查询改写: %s -> %s", req.Query, query)
	}

	// 使用现有的向量搜索函数
	results := searchKnowledgeBaseContent(query, req.KnowledgeBaseIds, req.MaxResults, req.MinScore)

	// 结果重排
	if req.ResultRerank {
		results = rerankResults(results, req.Query)
		log.Printf("结果重排完成")
	}

	return results, nil
}

// 全文搜索策略 - 使用关键词匹配
func searchWithFullTextStrategy(req KnowledgeSearchRequest) ([]KnowledgeSearchResult, error) {
	log.Printf("执行全文搜索策略")

	// 查询改写
	query := req.Query
	if req.QueryRewrite {
		query = rewriteQuery(req.Query)
		log.Printf("查询改写: %s -> %s", req.Query, query)
	}

	// 实现基于关键词的全文搜索
	results := searchWithKeywords(query, req.KnowledgeBaseIds, req.MaxResults, req.MinScore)

	// 结果重排
	if req.ResultRerank {
		results = rerankResults(results, req.Query)
		log.Printf("结果重排完成")
	}

	return results, nil
}

// 混合搜索策略 - 结合语义和全文搜索
func searchWithMixedStrategy(req KnowledgeSearchRequest) ([]KnowledgeSearchResult, error) {
	log.Printf("执行混合搜索策略")

	// 查询改写
	query := req.Query
	if req.QueryRewrite {
		query = rewriteQuery(req.Query)
		log.Printf("查询改写: %s -> %s", req.Query, query)
	}

	// 分别执行语义搜索和全文搜索
	semanticResults := searchKnowledgeBaseContent(query, req.KnowledgeBaseIds, req.MaxResults/2+1, req.MinScore)
	keywordResults := searchWithKeywords(query, req.KnowledgeBaseIds, req.MaxResults/2+1, req.MinScore)

	// 合并结果并去重
	results := mergeAndDeduplicateResults(semanticResults, keywordResults, req.MaxResults)

	// 结果重排
	if req.ResultRerank {
		results = rerankResults(results, req.Query)
		log.Printf("结果重排完成")
	}

	return results, nil
}

// 查询改写 - 扩展查询词汇
func rewriteQuery(originalQuery string) string {
	// 简单的查询改写逻辑，可以根据需要扩展
	query := strings.TrimSpace(originalQuery)

	// 添加同义词扩展
	synonyms := map[string][]string{
		"Go":    {"Golang", "Go语言"},
		"Vue":   {"Vue.js", "Vue框架"},
		"React": {"React.js", "React框架"},
		"前端":    {"前端开发", "前端技术", "客户端"},
		"后端":    {"后端开发", "服务端", "服务器端"},
		"数据库":   {"DB", "database", "存储"},
		"API":   {"接口", "应用程序接口"},
		"函数":    {"方法", "function", "method"},
	}

	words := strings.Fields(query)
	var expandedWords []string

	for _, word := range words {
		expandedWords = append(expandedWords, word)
		if syns, exists := synonyms[word]; exists {
			expandedWords = append(expandedWords, syns...)
		}
	}

	return strings.Join(expandedWords, " ")
}

// 基于关键词的全文搜索
func searchWithKeywords(query string, knowledgeBaseIds []string, maxResults int, minScore float64) []KnowledgeSearchResult {
	log.Printf("执行关键词搜索: %s", query)

	// 从数据库搜索包含关键词的分段
	results := searchSegmentsByKeywords(query, knowledgeBaseIds, maxResults, minScore)

	return results
}

// 从数据库搜索分段
func searchSegmentsByKeywords(query string, knowledgeBaseIds []string, maxResults int, minScore float64) []KnowledgeSearchResult {
	var results []KnowledgeSearchResult

	// 如果没有指定知识库ID，使用默认的示例知识库
	if len(knowledgeBaseIds) == 0 {
		knowledgeBaseIds = []string{"kb_sample_1", "kb_sample_2"}
	}

	// 查询关键词
	keywords := strings.Fields(strings.ToLower(query))

	// 从数据库查询分段
	for _, kbId := range knowledgeBaseIds {
		kb, err := knowledgeBaseService.GetKnowledgeBaseByID(kbId)
		if err != nil {
			log.Printf("获取知识库失败: %v", err)
			continue
		}

		for _, file := range kb.Files {
			for _, segment := range file.Segments {
				// 计算关键词匹配分数
				score := calculateKeywordScore(segment.Content, keywords)
				if score >= minScore {
					result := KnowledgeSearchResult{
						ID:              segment.ID,
						Content:         segment.Content,
						Score:           score,
						Source:          file.Name,
						KnowledgeBaseId: kbId,
						Keywords:        segment.GetKeywords(),
						Summary:         segment.Summary,
					}
					results = append(results, result)
				}
			}
		}
	}

	// 按分数排序
	sort.Slice(results, func(i, j int) bool {
		return results[i].Score > results[j].Score
	})

	// 限制结果数量
	if len(results) > maxResults {
		results = results[:maxResults]
	}

	return results
}

// 计算关键词匹配分数
func calculateKeywordScore(content string, keywords []string) float64 {
	if len(keywords) == 0 {
		return 0.0
	}

	contentLower := strings.ToLower(content)
	matchCount := 0

	for _, keyword := range keywords {
		if strings.Contains(contentLower, keyword) {
			matchCount++
		}
	}

	return float64(matchCount) / float64(len(keywords))
}

// 结果重排 - 根据查询相关性重新排序
func rerankResults(results []KnowledgeSearchResult, originalQuery string) []KnowledgeSearchResult {
	if len(results) == 0 {
		return results
	}

	// 简单的重排逻辑：根据内容与查询的相关性重新计算分数
	queryWords := strings.Fields(strings.ToLower(originalQuery))

	for i := range results {
		// 重新计算分数，考虑更多因素
		contentScore := calculateKeywordScore(results[i].Content, queryWords)
		summaryScore := calculateKeywordScore(results[i].Summary, queryWords)
		keywordScore := calculateKeywordMatchScore(results[i].Keywords, queryWords)

		// 综合分数
		results[i].Score = (contentScore*0.6 + summaryScore*0.2 + keywordScore*0.2)
	}

	// 重新排序
	sort.Slice(results, func(i, j int) bool {
		return results[i].Score > results[j].Score
	})

	return results
}

// 计算关键词匹配分数
func calculateKeywordMatchScore(keywords []string, queryWords []string) float64 {
	if len(keywords) == 0 || len(queryWords) == 0 {
		return 0.0
	}

	matchCount := 0
	for _, keyword := range keywords {
		keywordLower := strings.ToLower(keyword)
		for _, queryWord := range queryWords {
			if strings.Contains(keywordLower, queryWord) {
				matchCount++
				break
			}
		}
	}

	return float64(matchCount) / float64(len(queryWords))
}

// 合并并去重搜索结果
func mergeAndDeduplicateResults(semanticResults, keywordResults []KnowledgeSearchResult, maxResults int) []KnowledgeSearchResult {
	resultMap := make(map[string]KnowledgeSearchResult)

	// 添加语义搜索结果
	for _, result := range semanticResults {
		if existing, exists := resultMap[result.ID]; exists {
			// 如果已存在，取较高的分数
			if result.Score > existing.Score {
				resultMap[result.ID] = result
			}
		} else {
			resultMap[result.ID] = result
		}
	}

	// 添加关键词搜索结果
	for _, result := range keywordResults {
		if existing, exists := resultMap[result.ID]; exists {
			// 如果已存在，合并分数（加权平均）
			mergedScore := (existing.Score + result.Score) / 2
			existing.Score = mergedScore
			resultMap[result.ID] = existing
		} else {
			resultMap[result.ID] = result
		}
	}

	// 转换为切片并排序
	var results []KnowledgeSearchResult
	for _, result := range resultMap {
		results = append(results, result)
	}

	sort.Slice(results, func(i, j int) bool {
		return results[i].Score > results[j].Score
	})

	// 限制结果数量
	if len(results) > maxResults {
		results = results[:maxResults]
	}

	return results
}

// 计算匹配分数
func calculateMatchScore(query string, content string, keywords []string) float64 {
	contentLower := strings.ToLower(content)
	score := 0.0

	// 内容匹配
	if strings.Contains(contentLower, query) {
		score += 0.6
	}

	// 关键词匹配
	for _, keyword := range keywords {
		if strings.Contains(strings.ToLower(keyword), query) || strings.Contains(query, strings.ToLower(keyword)) {
			score += 0.3
		}
	}

	// 部分词匹配
	queryWords := strings.Fields(query)
	for _, word := range queryWords {
		if len(word) > 1 && strings.Contains(contentLower, word) {
			score += 0.1
		}
	}

	// 确保分数不超过1.0
	if score > 1.0 {
		score = 1.0
	}

	return score
}

package internetal

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"gorm.io/gorm"
)

// UserController 用户控制器
type UserController struct {
	UserService    *UserService
	CaptchaService *CaptchaService
	MailClient     *MailClient
}

// NewUserController 创建用户控制器
func NewUserController(db *gorm.DB, mailClient *MailClient) *UserController {
	return &UserController{
		UserService:    NewUserService(db),
		CaptchaService: NewCaptchaService(db),
		MailClient:     mailClient,
	}
}

// Login 处理用户密码登录请求
func (c *UserController) Login(ctx *gin.Context) {
	var req LoginRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "请求参数错误",
		})
		return
	}

	user, err := c.UserService.Login(req)
	if err != nil {
		statusCode := http.StatusUnauthorized
		if err.Error() == "用户不存在" {
			statusCode = http.StatusNotFound
		}
		ctx.JSON(statusCode, gin.H{
			"error": err.Error(),
		})
		return
	}

	// 生成JWT token
	token, err := GenerateToken(user.ID, user.Username)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "生成token失败: " + err.Error(),
		})
		return
	}

	// 将token存储在cookie中，有效期24小时
	ctx.SetCookie("auth_token", token, 86400, "/", "localhost", false, true)

	ctx.JSON(http.StatusOK, gin.H{
		"token": token,
		"user": gin.H{
			"id":       user.ID,
			"username": user.Username,
			"email":    user.Email,
		},
	})
}

// SmsLogin 使用邮箱验证码登录

func (c *UserController) SmsLogin(ctx *gin.Context) {
	var req SmsLoginRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "请求参数错误",
		})
		return
	}

	// 验证验证码
	if err := c.CaptchaService.VerifyCaptcha(req.Email, req.Code); err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"error": err.Error(),
		})
		return
	}

	user, err := c.UserService.SmsLogin(req)
	if err != nil {
		statusCode := http.StatusUnauthorized
		if err.Error() == "用户不存在" {
			statusCode = http.StatusNotFound
		}
		ctx.JSON(statusCode, gin.H{
			"error": err.Error(),
		})
		return
	}

	// 生成JWT token
	token, err := GenerateToken(user.ID, user.Username)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "生成token失败: " + err.Error(),
		})
		return
	}

	// 将token存储在cookie中，有效期24小时
	ctx.SetCookie("auth_token", token, 86400, "/", "localhost", false, true)

	ctx.JSON(http.StatusOK, gin.H{
		"token": token,
		"user": gin.H{
			"id":       user.ID,
			"username": user.Username,
			"email":    user.Email,
		},
	})
}

// RegisterRequest 注册请求结构体
type RegisterRequest struct {
	Username        string `json:"username"`
	Password        string `json:"password"`
	ConfirmPassword string `json:"confirmPassword"`
	Email           string `json:"email"`
	Code            string `json:"code"`
}

// SendCaptchaRequest 发送验证码请求结构体
type SendCaptchaRequest struct {
	Email string `json:"email"`
}

// SendCaptcha 发送验证码
func (c *UserController) SendCaptcha(ctx *gin.Context) {
	var req SendCaptchaRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 生成验证码
	code, err := c.CaptchaService.GenerateCaptcha(req.Email)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}

	// 使用注入的邮件客户端发送验证码邮件
	if err := c.MailClient.SendVerificationCode(req.Email, code); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "发送验证码邮件失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message": "验证码发送成功，请查收邮箱",
		"code":    code, // 仅用于开发测试，实际应用中不应返回验证码
	})
}

// ResetPasswordRequest 重置密码请求结构体
type ResetPasswordRequest struct {
	Email       string `json:"email"`
	Code        string `json:"code"`
	NewPassword string `json:"newPassword"`
}

// ResetPassword 处理重置密码请求
func (c *UserController) ResetPassword(ctx *gin.Context) {
	var req ResetPasswordRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 验证验证码
	if err := c.CaptchaService.VerifyCaptcha(req.Email, req.Code); err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"error": err.Error(),
		})
		return
	}

	// 验证密码长度
	if len(req.NewPassword) < 6 {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "密码长度至少为6位",
		})
		return
	}

	// 重置密码
	if err := c.UserService.ResetPassword(req.Email, req.NewPassword); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "重置密码失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message": "密码重置成功",
	})
}

// Register 处理用户注册请求
func (c *UserController) Register(ctx *gin.Context) {
	var req RegisterRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 验证码校验
	if err := c.CaptchaService.VerifyCaptcha(req.Email, req.Code); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}

	// 检查两次密码是否一致
	if req.Password != req.ConfirmPassword {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "两次输入的密码不一致",
		})
		return
	}

	// 创建用户
	user := &User{
		Username: req.Username,
		Password: req.Password,
		Email:    req.Email,
	}

	// 调用服务层进行注册
	if err := c.UserService.Register(user); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message": "注册成功",
		"user": gin.H{
			"id":       user.ID,
			"username": user.Username,
			"email":    user.Email,
		},
	})
}

<template>
  <div class="login-container min-h-screen flex items-center justify-center relative overflow-hidden">
    <div class="login-form bg-white rounded-2xl shadow-2xl p-8 w-full max-w-md relative z-10 transform transition-all duration-500 hover:scale-[1.02]">
      <div class="form-header text-center mb-8">
        <h2 class="form-title text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-500 bg-clip-text text-transparent">找回密码</h2>
        <p class="form-subtitle text-gray-500 mt-2">请输入您的邮箱获取验证码</p>
      </div>
      <div class="form-content space-y-5">
        <div class="form-group">
          <label class="form-label block text-sm font-medium text-gray-700 mb-1" for="email">邮箱</label>
          <input
            id="email"
            type="text"
            placeholder="请输入注册邮箱"
            class="form-input w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all"
            v-model="email"
            @keyup.enter="sendCode"
          />
        </div>
        <div class="form-group">
          <label class="form-label block text-sm font-medium text-gray-700 mb-1" for="code">验证码</label>
          <div class="sms-code-container flex space-x-2">
            <input
              id="code"
              type="text"
              placeholder="请输入验证码"
              class="form-input sms-code-input flex-1 px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all"
              v-model="code"
              @keyup.enter="resetPassword"
            />
            <button
              class="sms-code-button whitespace-nowrap px-4 py-3 rounded-lg bg-gradient-to-r from-purple-500 to-blue-500 text-white font-medium hover:opacity-90 transition-opacity"
              @click="sendCode"
              :disabled="countdownTime > 0"
              :class="{ 'opacity-50 cursor-not-allowed': countdownTime > 0 }"
            >
              {{ countdownTime > 0 ? `${countdownTime}秒后重新获取` : "获取验证码" }}
            </button>
          </div>
        </div>
        <div class="form-group">
          <label class="form-label block text-sm font-medium text-gray-700 mb-1" for="newPassword">新密码</label>
          <input
            id="newPassword"
            type="password"
            placeholder="请输入新密码（至少6位，包含字母和数字）"
            class="form-input w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all"
            v-model="newPassword"
            @input="checkPasswordStrength(newPassword)"
            @keyup.enter="resetPassword"
          />
          <div v-if="newPassword" class="mt-1 flex items-center">
            <div class="w-full bg-gray-200 rounded-full h-2.5 mr-2">
              <div
                class="h-2.5 rounded-full"
                :class="{
                  'bg-red-500': passwordStrength === 'weak',
                  'bg-yellow-500': passwordStrength === 'medium',
                  'bg-green-500': passwordStrength === 'strong'
                }"
                :style="{
                  width: passwordStrength === 'weak' ? '33%' : passwordStrength === 'medium' ? '66%' : '100%'
                }"
              ></div>
            </div>
            <span class="text-sm font-medium"
              :class="{
                'text-red-500': passwordStrength === 'weak',
                'text-yellow-500': passwordStrength === 'medium',
                'text-green-500': passwordStrength === 'strong'
              }"
            >
              {{ passwordStrength === 'weak' ? '弱' : passwordStrength === 'medium' ? '中' : '强' }}
            </span>
          </div>
        </div>
        <div class="form-group">
          <label class="form-label block text-sm font-medium text-gray-700 mb-1" for="confirmPassword">确认新密码</label>
          <input
            id="confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            class="form-input w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all"
            v-model="confirmPassword"
            @keyup.enter="resetPassword"
          />
        </div>
        <button
          class="form-button w-full py-3 rounded-lg bg-gradient-to-r from-purple-600 to-blue-500 text-white font-medium text-lg hover:opacity-90 transition-all"
          :class="{ 'opacity-70 cursor-not-allowed': isLoading }"
          @click="resetPassword"
          :disabled="isLoading"
        >
          {{ isLoading ? "重置中..." : "重置密码" }}
        </button>
        <div class="register-link mt-4 text-center">
          <span class="text-gray-600">返回登录？</span>
          <button
            @click="goToLogin"
            class="text-blue-500 hover:text-blue-700 transition-colors duration-200 ml-1 bg-transparent border-none p-0 font-medium"
          >
            立即登录
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ResetPassword",
  data() {
    return {
      email: "",
      code: "",
      newPassword: "",
      confirmPassword: "",
      countdownTime: 0,
      isLoading: false,
      passwordStrength: '' // 密码强度: 'weak', 'medium', 'strong'
    };
  },
  methods: {
    goToLogin() {
      // 使用命名路由跳转，更可靠
      this.$router.push({ name: 'Login' });
    },
    checkPasswordStrength(password) {
      // 检查密码强度
      let strength = 'weak';
      const hasLetter = /[a-zA-Z]/.test(password);
      const hasNumber = /\d/.test(password);
      const hasSpecial = /[^a-zA-Z0-9]/.test(password);

      if (password.length >= 8 && hasLetter && hasNumber && hasSpecial) {
        strength = 'strong';
      } else if (password.length >= 6 && hasLetter && hasNumber) {
        strength = 'medium';
      }

      this.passwordStrength = strength;
      return strength !== 'weak';
    },
    sendCode() {
      if (this.countdownTime > 0) return;

      // 验证邮箱格式
      if (!this.email.trim()) {
        alert("请输入邮箱");
        return;
      }
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      if (!emailRegex.test(this.email.trim())) {
        alert("请输入有效的邮箱地址");
        return;
      }

      this.isLoading = true;

      // 向后端发送获取验证码的请求
      fetch("http://localhost:8080/api/send-captcha", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: this.email.trim()
        }),
      })
        .then((response) => {
          if (!response.ok) {
            return response.json().then((errData) => {
              throw new Error(errData.error || "发送验证码失败，请重试");
            });
          }
          return response.json();
        })
        .then((data) => {
          alert("验证码发送成功，请查收");
          console.log("验证码发送成功:", data);

          // 设置倒计时60秒
          this.countdownTime = 60;
          const timer = setInterval(() => {
            this.countdownTime--;
            if (this.countdownTime <= 0) {
              clearInterval(timer);
            }
          }, 1000);
        })
        .catch((error) => {
          alert("发送验证码失败: " + error.message);
          console.error("发送验证码错误:", error);
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    resetPassword() {
      // 表单校验
      if (!this.email.trim()) {
        alert("请输入邮箱");
        return;
      }
      if (!this.code.trim()) {
        alert("请输入验证码");
        return;
      }
      if (!/^\d{6}$/.test(this.code.trim())) {
        alert("验证码必须是6位数字");
        return;
      }
      if (!this.newPassword) {
        alert("请输入新密码");
        return;
      }
      if (this.newPassword.length < 6) {
        alert("密码长度至少为6位");
        return;
      }
      // 检查密码强度
      if (!this.checkPasswordStrength(this.newPassword)) {
        alert("密码强度不足，请包含字母和数字");
        return;
      }
      if (this.newPassword !== this.confirmPassword) {
        alert("两次输入的密码不一致");
        return;
      }

      this.isLoading = true;

      // 向后端发送重置密码的请求
      fetch("http://localhost:8080/api/reset-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: this.email.trim(),
          code: this.code.trim(),
          newPassword: this.newPassword
        }),
      })
        .then((response) => {
          // 检查响应是否为JSON格式
          const contentType = response.headers.get('content-type');
          if (!response.ok) {
            if (contentType && contentType.includes('application/json')) {
              return response.json().then((errData) => {
                throw new Error(errData.error || "重置密码失败，请重试");
              });
            } else {
              // 非JSON响应，读取文本内容
              return response.text().then((text) => {
                throw new Error(`重置密码失败: ${text.substring(0, 100)}...`);
              });
            }
          }
          
          if (contentType && contentType.includes('application/json')) {
            return response.json();
          } else {
            throw new Error("服务器返回非JSON格式响应");
          }
        })
        .then((data) => {
          alert("密码重置成功，请登录");
          console.log("密码重置成功:", data);
          // 使用命名路由跳转，更可靠
          this.$router.push({ name: 'Login' });
        })
        .catch((error) => {
          alert("重置密码失败: " + error.message);
          console.error("重置密码错误:", error);
        })
        .finally(() => {
          this.isLoading = false;
        });
    }
  }
};
</script>

<style scoped>
.login-container {
  background-image: url("@/assets/Login .png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  width: 100%;
  margin: 0;
  padding: 0;
}

/* 额外的响应式调整 */
@media (max-width: 768px) {
  .login-form {
    margin: 0 1rem;
    padding: 2rem;
  }
}

/* 表单元素的额外样式 */
.form-input:focus {
  outline: none;
}

.form-button:active {
  transform: scale(0.98);
}
</style>
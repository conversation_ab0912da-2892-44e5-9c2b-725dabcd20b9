package internetal

import (
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// Assistant 数据模型
type Assistant struct {
	ID              uint      `gorm:"primaryKey" json:"id"`
	Name            string    `gorm:"size:100;not null" json:"name"`
	Description     string    `gorm:"size:500" json:"description"`
	PersonnelDesign string    `gorm:"type:text" json:"personnel_design"`
	UserID          uint      `json:"user_id" gorm:"index"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// API请求/响应结构体
type CreateRequest struct {
	Name            string `json:"name" binding:"required"`
	Description     string `json:"description"`
	PersonnelDesign string `json:"personnel_design"`
}

type UpdateRequest struct {
	ID              uint   `json:"id" binding:"required"`
	Name            string `json:"name"`
	Description     string `json:"description"`
	PersonnelDesign string `json:"personnel_design"`
}

type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// AssistantApi 助手API管理器
type AssistantApi struct {
	db *gorm.DB
}

// NewAssistantApi 创建助手API管理器
func NewAssistantApi(database *gorm.DB) *AssistantApi {
	api := &AssistantApi{db: database}
	// 自动迁移表结构
	if err := api.db.AutoMigrate(&Assistant{}); err != nil {
		log.Fatal("Failed to migrate tables:", err)
	}
	return api
}

// HandleCreate 创建助手
func (api *AssistantApi) HandleCreate(c *gin.Context) {
	var req CreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		sendError(c, http.StatusBadRequest, "Invalid request: "+err.Error())
		return
	}

	// 从上下文中获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		sendError(c, http.StatusUnauthorized, "未认证")
		return
	}

	assistant := Assistant{
		Name:            req.Name,
		Description:     req.Description,
		PersonnelDesign: req.PersonnelDesign,
		UserID:          userID.(uint),
	}

	if err := api.db.Create(&assistant).Error; err != nil {
		sendError(c, http.StatusInternalServerError, "Failed to create assistant: "+err.Error())
		return
	}

	sendSuccess(c, "Assistant created successfully", assistant)
}

func (api *AssistantApi) HandleUpdate(c *gin.Context) {
	var req UpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		sendError(c, http.StatusBadRequest, "Invalid request: "+err.Error())
		return
	}

	// 从上下文中获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		sendError(c, http.StatusUnauthorized, "未认证")
		return
	}

	var assistant Assistant
	if err := api.db.First(&assistant, req.ID).Error; err != nil {
		sendError(c, http.StatusNotFound, "Assistant not found")
		return
	}

	// 检查是否为当前用户的助手
	if assistant.UserID != userID.(uint) {
		sendError(c, http.StatusForbidden, "无权修改此助手")
		return
	}

	// 使用Select只更新非零值字段
	updates := map[string]interface{}{}
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.PersonnelDesign != "" {
		updates["personnel_design"] = req.PersonnelDesign
	}

	if err := api.db.Model(&assistant).Select("*").Updates(updates).Error; err != nil {
		sendError(c, http.StatusInternalServerError, "Failed to update assistant: "+err.Error())
		return
	}

	sendSuccess(c, "Assistant updated successfully", assistant)
}

func (api *AssistantApi) HandleDelete(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		sendError(c, http.StatusBadRequest, "Missing ID parameter")
		return
	}

	// 从上下文中获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		sendError(c, http.StatusUnauthorized, "未认证")
		return
	}

	var assistant Assistant
	if err := api.db.First(&assistant, id).Error; err != nil {
		sendError(c, http.StatusNotFound, "Assistant not found")
		return
	}

	// 检查是否为当前用户的助手
	if assistant.UserID != userID.(uint) {
		sendError(c, http.StatusForbidden, "无权删除此助手")
		return
	}

	if err := api.db.Delete(&assistant).Error; err != nil {
		sendError(c, http.StatusInternalServerError, "Failed to delete assistant: "+err.Error())
		return
	}

	sendSuccess(c, "Assistant deleted successfully", nil)
}

func (api *AssistantApi) HandleList(c *gin.Context) {
	// 从上下文中获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		sendError(c, http.StatusUnauthorized, "未认证")
		return
	}

	var assistants []Assistant
	if err := api.db.Where("user_id = ?", userID.(uint)).Order("created_at DESC").Find(&assistants).Error; err != nil {
		sendError(c, http.StatusInternalServerError, "Failed to fetch assistants: "+err.Error())
		return
	}

	sendSuccess(c, "Assistants fetched successfully", assistants)
}

// 辅助函数
func sendError(c *gin.Context, code int, message string) {
	c.JSON(code, Response{
		Code:    code,
		Message: message,
	})
}

func sendSuccess(c *gin.Context, message string, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:    http.StatusOK,
		Message: message,
		Data:    data,
	})
}

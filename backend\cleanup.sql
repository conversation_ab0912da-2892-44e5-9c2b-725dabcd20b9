-- 数据库清理脚本
-- 用于清理重复数据或重置数据库

USE knowledge_base_system;

-- 清理所有数据（保留表结构）
SET FOREIGN_KEY_CHECKS = 0;

-- 清理分段数据
TRUNCATE TABLE `knowledge_segments`;

-- 清理文件数据
TRUNCATE TABLE `knowledge_base_files`;

-- 清理设置数据
TRUNCATE TABLE `knowledge_base_settings`;

-- 清理知识库主数据
TRUNCATE TABLE `knowledge_bases`;

-- 清理机器人数据（可选）
TRUNCATE TABLE `robots`;

SET FOREIGN_KEY_CHECKS = 1;

-- 重置自增ID（如果需要）
ALTER TABLE `knowledge_base_settings` AUTO_INCREMENT = 1;
ALTER TABLE `robots` AUTO_INCREMENT = 1;

-- 显示清理结果
SELECT 'Database cleanup completed' as status;
SELECT
    TABLE_NAME,
    TABLE_ROWS
FROM
    information_schema.TABLES
WHERE
    TABLE_SCHEMA = 'knowledge_base_system'
  AND TABLE_TYPE = 'BASE TABLE';

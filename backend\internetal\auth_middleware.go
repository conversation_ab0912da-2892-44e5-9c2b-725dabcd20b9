package internetal

import (
	"errors"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"gorm.io/gorm"
)

var jwtSecret = []byte("your-secret-key-here") // 实际应用中应从配置文件加载

// Claims 定义JWT声明结构
type Claims struct {
	UserID   uint   `json:"user_id"`
	Username string `json:"username"`
	jwt.RegisteredClaims
}

// AuthMiddleware 认证中间件
func AuthMiddleware(dbManager *DBManager) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// 优先从请求头中获取token
		authHeader := ctx.GetHeader("Authorization")
		token := ""

		// 如果请求头中没有token，尝试从cookie中获取
		if authHeader == "" {
			cookie, err := ctx.Cookie("auth_token")
			if err == nil && cookie != "" {
				token = cookie
			} else {
				ctx.JSON(http.StatusUnauthorized, gin.H{
					"error": "未提供认证信息",
				})
				ctx.Abort()
				return
			}
		} else {
			// 解析请求头中的token
			tokenParts := strings.SplitN(authHeader, " ", 2)
			if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
				ctx.JSON(http.StatusUnauthorized, gin.H{
					"error": "无效的认证格式",
				})
				ctx.Abort()
				return
			}
			token = tokenParts[1]
		}

		// 验证并解析token
		claims, err := parseToken(token)
		if err != nil {
			ctx.JSON(http.StatusUnauthorized, gin.H{
				"error": "无效的token: " + err.Error(),
			})
			ctx.Abort()
			return
		}

		// 将用户ID和用户名存储在上下文中
		ctx.Set("userID", claims.UserID)
		ctx.Set("username", claims.Username)

		// 继续处理请求
		ctx.Next()
	}
}

// parseToken 解析JWT token
func parseToken(tokenString string) (*Claims, error) {
	// 解析token
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		// 验证签名算法
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("无效的签名算法")
		}
		return jwtSecret, nil
	})

	if err != nil {
		return nil, err
	}

	// 验证token是否有效
	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("无效的token")
}

// GenerateToken 生成JWT token
func GenerateToken(userID uint, username string) (string, error) {
	// 设置token过期时间为24小时
	expirationTime := time.Now().Add(24 * time.Hour)

	// 创建claims
	claims := &Claims{
		UserID:   userID,
		Username: username,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
		},
	}

	// 创建token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// 签名并获取完整的编码后的字符串token
	return token.SignedString(jwtSecret)
}

// DBManager 数据库连接管理器
type DBManager struct {
	db *gorm.DB
}

// NewDBManager 创建数据库连接管理器
func NewDBManager(database *gorm.DB) *DBManager {
	return &DBManager{db: database}
}

// GetUserByUsername 根据用户名查询用户
func (m *DBManager) GetUserByUsername(username string) (*User, error) {
	var user User
	result := m.db.Where("username = ?", username).First(&user)
	return &user, result.Error
}

// GetUserByID 根据ID查询用户
func (m *DBManager) GetUserByID(userID uint) (*User, error) {
	var user User
	result := m.db.Where("id = ?", userID).First(&user)
	return &user, result.Error
}

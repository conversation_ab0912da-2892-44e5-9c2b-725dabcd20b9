package internetal

import (
	"errors"
	"fmt"
	"math/rand"
	"regexp"
	"sync"
	"time"

	"gorm.io/gorm"
)

// CaptchaService 验证码服务
type CaptchaService struct {
	DB           *gorm.DB
	captchaStore map[string]CaptchaInfo // 内存存储验证码，实际应用中应使用Redis
	mutex        sync.RWMutex           // 保护captchaStore的互斥锁
}

// CaptchaInfo 验证码信息
type CaptchaInfo struct {
	Code      string    // 验证码内容
	ExpiresAt time.Time // 过期时间
	Email     string    // 关联的邮箱
}

// NewCaptchaService 创建验证码服务实例
func NewCaptchaService(db *gorm.DB) *CaptchaService {
	return &CaptchaService{
		DB:           db,
		captchaStore: make(map[string]CaptchaInfo),
	}
}

// GenerateCaptcha 生成验证码
func (s *CaptchaService) GenerateCaptcha(email string) (string, error) {
	// 检查邮箱格式
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	if !emailRegex.MatchString(email) {
		return "", errors.New("邮箱格式不正确")
	}

	// 生成6位数字验证码
	rand.Seed(time.Now().UnixNano())
	code := fmt.Sprintf("%06d", rand.Intn(1000000))

	// 存储验证码，有效期5分钟
	expiresAt := time.Now().Add(5 * time.Minute)

	s.mutex.Lock()
	s.captchaStore[email] = CaptchaInfo{
		Code:      code,
		ExpiresAt: expiresAt,
		Email:     email,
	}
	s.mutex.Unlock()

	// 在实际应用中，验证码将通过邮箱发送
	fmt.Printf("生成验证码 %s 用于邮箱 %s\n", code, email)

	return code, nil
}

// VerifyCaptcha 验证验证码
func (s *CaptchaService) VerifyCaptcha(email, code string) error {
	// 使用正则表达式验证验证码格式是否为6位数字
	captchaRegex := regexp.MustCompile(`^\d{6}$`)
	if !captchaRegex.MatchString(code) {
		return errors.New("验证码必须是6位数字")
	}

	s.mutex.RLock()
	captchaInfo, exists := s.captchaStore[email]
	s.mutex.RUnlock()

	if !exists {
		return errors.New("验证码不存在或已过期")
	}

	if time.Now().After(captchaInfo.ExpiresAt) {
		// 验证码已过期，删除
		s.mutex.Lock()
		delete(s.captchaStore, email)
		s.mutex.Unlock()
		return errors.New("验证码已过期")
	}

	if captchaInfo.Code != code {
		return errors.New("验证码错误")
	}

	// 验证成功，删除验证码
	s.mutex.Lock()
	delete(s.captchaStore, email)
	s.mutex.Unlock()

	return nil
}

<template>
  <div class="register-container min-h-screen flex items-center justify-center p-4">
    <!-- 注册表单区域 -->
    <div class="register-form">
      <div class="form-header">
        <h2 class="form-title">账号注册</h2>
        <p class="form-subtitle">创建新账号</p>
      </div>
      <div class="form-content">
        <div class="form-group">
          <label class="form-label" for="username">用户名</label>
          <input 
            id="username"
            type="text"
            placeholder="请输入用户名"
            class="form-input"
            v-model="username"
          >
        </div>
        <div class="form-group">
          <label class="form-label" for="password">密码</label>
          <input 
            id="password"
            type="password"
            placeholder="请输入密码"
            class="form-input"
            v-model="password"
          >
        </div>
        <div class="form-group">
          <label class="form-label" for="confirmPassword">确认密码</label>
          <input 
            id="confirmPassword"
            type="password"
            placeholder="请再次输入密码"
            class="form-input"
            v-model="confirmPassword"
          >
        </div>
        <div class="form-group">
          <label class="form-label" for="email">邮箱</label>
          <input 
            id="email"
            type="email"
            placeholder="请输入邮箱"
            class="form-input"
            v-model="email"
          >
        </div>
        <div class="form-group">
          <label class="form-label" for="smsCode">邮箱验证码</label>
          <div class="sms-code-container">
            <input 
              id="smsCode"
              type="text"
              placeholder="请输入验证码"
              class="form-input sms-code-input"
              v-model="smsCode"
            >
            <button 
                class="sms-code-button"
                @click="sendVerificationCode"
                :disabled="countdownTime > 0"
              >
                {{ countdownTime > 0 ? `${countdownTime}秒后重新获取` : '获取验证码' }}
              </button>
        </div>
      </div>
        <button 
          class="form-button"
          :class="{ 'opacity-70 cursor-not-allowed': isLoading }"
          @click="handleRegister"
          :disabled="isLoading"
        >
          {{ isLoading ? '注册中...' : '立即注册' }}
        </button>
        <div class="login-link mt-4 text-center">
          <span>已有账号？</span>
          <button @click="goToLogin" class="text-blue-500 hover:text-blue-700 transition-colors duration-200 ml-1 bg-transparent border-none p-0">立即登录</button>
        </div>
      </div>
    </div>
  </div>

  <!-- 验证码弹窗 -->
  <div v-if="showCaptchaModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white p-6 rounded-lg shadow-xl max-w-sm w-full mx-4">
      <h3 class="text-xl font-bold mb-4 text-center">验证码</h3>
      <p class="text-center text-2xl font-mono mb-6">{{ captchaCode }}</p>
      <p class="text-gray-600 text-center mb-6">请记住验证码并输入</p>
      <button 
        class="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200"
        @click="showCaptchaModal = false"
      >
        我已记住
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RegisterForm',
  data() {
    return {
      username: '', // 用户名
      password: '', // 密码
      confirmPassword: '', // 确认密码
      email: '', // 邮箱
      smsCode: '', // 邮箱验证码
    captchaCode: '', // 显示的验证码
    showCaptchaModal: false, // 验证码弹窗显示状态
    countdownTime: 0, // 验证码倒计时时间
      isLoading: false // 注册按钮加载状态
    }
  },
  methods: {
    // 跳转到登录页面
    goToLogin() {
      this.$router.push('/login');
    },
    handleRegister() {
      // 表单校验
      if (!this.username.trim()) {
        alert('请输入用户名');
        return;
      }
      if (!this.password) {
        alert('请输入密码');
        return;
      }
      // 密码格式校验：至少6位
      if (this.password.length < 6) {
        alert('密码长度至少为6位');
        return;
      }
      if (this.password !== this.confirmPassword) {
        alert('两次输入的密码不一致');
        return;
      }
      if (!this.email.trim()) {
        alert('请输入邮箱');
        return;
      }
      // 邮箱格式校验
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(this.email)) {
        alert('请输入有效的邮箱地址');
        return;
      }
      // 手机号验证已移除，改为邮箱验证
      if (!this.smsCode.trim()) {
        alert('请输入验证码');
        return;
      }
      // 验证码格式校验：6位数字
      if (!/^\d{6}$/.test(this.smsCode.trim())) {
        alert('验证码必须是6位数字');
        return;
      }
      // 验证码由后端验证，此处不再需要前端验证


      // 注册逻辑
      console.log('注册信息：', this.username, this.password, this.email);

      // 显示加载状态
      this.isLoading = true;

      // 使用API进行注册
      // 检查API地址是否正确，确保后端服务在运行
      fetch('/api/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          username: this.username,
          password: this.password,
          confirmPassword: this.confirmPassword,
          email: this.email,
          code: this.smsCode
        })
      })
      .then(response => {
        if (!response.ok) {
          // 检查响应是否为JSON格式
          const contentType = response.headers.get('content-type');
          if (contentType && contentType.includes('application/json')) {
            return response.json().then(errData => {
              throw new Error(errData.error || '注册失败，请稍后再试');
            });
          } else {
            // 非JSON响应，获取文本内容
            return response.text().then(responseText => {
              throw new Error(`注册失败: 服务器返回非JSON响应 - ${response.status} ${response.statusText}\n响应内容: ${responseText.substring(0, 100)}`);
            });
          }
        }
        return response.json();
      })
      .then(() => {
        // 注册成功
        console.log('注册成功，准备自动登录');
        alert('注册成功，即将自动登录');

        // 自动登录
        fetch('/api/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            username: this.username,
            password: this.password
          })
        })
        .then(response => {
          if (!response.ok) {
            // 检查响应是否为JSON格式
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
              return response.json().then(errData => {
                throw new Error(errData.error || '自动登录失败，请手动登录');
              });
            } else {
              // 非JSON响应，获取文本内容
            return response.text().then(responseText => {
              throw new Error(`自动登录失败: 服务器返回非JSON响应 - ${response.status} ${response.statusText}\n响应内容: ${responseText.substring(0, 100)}`);
            });
            }
          }
          return response.json();
        })
        .then(data => {
          // 登录成功，存储token和登录状态
          localStorage.setItem('token', data.token);
          localStorage.setItem('userInfo', JSON.stringify(data.user));
          localStorage.setItem('isLoggedIn', 'true');

          // 提示成功
          alert('登录成功');

          // 跳转到应用页面
          this.$router.push({ name: 'App' });
        })
        .catch(error => {
            // 自动登录失败 - 增强错误处理
            let errorMsg = '自动登录失败: ';
            if (error.name === 'TypeError' && error.message === 'Failed to fetch') {
              errorMsg += '无法连接到后端服务，请检查后端是否已启动';
            } else {
              errorMsg += error.message;
            }
            alert(errorMsg);
            console.error('自动登录错误详情:', error);
            console.log('登录请求地址:', '/api/login');
            // 触发注册成功事件，跳转到登录页面
            this.$emit('register-success');
          });

        // 注册成功事件已由父组件处理
      })
      .catch(error => {
        // 注册失败 - 增强错误处理
        let errorMsg = '注册失败: ';
        if (error.name === 'TypeError' && error.message === 'Failed to fetch') {
          errorMsg += '无法连接到后端服务，请检查后端是否已启动';
        } else if (error.response) {
          errorMsg += `服务器错误: ${error.response.status} ${error.response.statusText}`;
        } else {
          errorMsg += error.message;
        }
        alert(errorMsg);
        console.error('注册错误详情:', error);
        // 打印网络请求信息以便调试
        console.log('请求地址:', '/api/register');
        console.log('请求参数:', JSON.stringify({
          username: this.username,
          password: this.password,
          email: this.email,
          code: this.smsCode
        }, null, 2));
      })
      .finally(() => {
        // 隐藏加载状态
        this.isLoading = false;
      });
    },
    sendVerificationCode() {
      if (this.countdownTime > 0) return;
      if (!this.email.trim()) {
          alert('请输入邮箱');
          return;
        }
        // 邮箱格式校验
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(this.email)) {
          alert('请输入有效的邮箱地址');
          return;
        }

      // 向后端发送请求获取验证码
      fetch('/api/send-captcha', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: this.email
        })
      })
      .then(response => {
        if (!response.ok) {
          return response.json().then(errData => {
            throw new Error(errData.error || '获取验证码失败，请稍后再试');
          });
        }
        return response.json();
      })
      .then(data => {
        alert('验证码发送成功，请查收邮箱');
        console.log('验证码发送成功:', data);
        // 设置倒计时60秒
        this.countdownTime = 60;
        const timer = setInterval(() => {
          this.countdownTime--;
          if (this.countdownTime <= 0) {
            clearInterval(timer);
          }
        }, 1000);
      })
      .catch(error => {
        alert('获取验证码失败: ' + error.message);
        console.error('获取验证码错误:', error);
      });
    }
  }
}
</script>

<style scoped lang="postcss">
.register-container {
  width: 100%;
  height: 100vh;
  background-image: url('@/assets/Login .png');
  background-size: cover;
  background-position: center;
  display: flex;
  justify-content: center;
  align-items: center;
}
.register-form {
  @apply w-full max-w-md bg-white rounded-xl shadow-2xl overflow-hidden transform transition-all duration-300 hover:shadow-xl;
}

.form-header {
  @apply bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6;
}

.form-title {
  @apply text-2xl font-bold mb-1;
}

.form-subtitle {
  @apply text-blue-100;
}

.form-content {
  @apply p-6;
}

.form-group {
  @apply mb-4;
}

.form-label {
  @apply block text-gray-700 mb-2 font-medium;
}

.form-input {
  @apply w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200;
  font-family: 'SimHei', 'Microsoft YaHei', 'Heiti TC', sans-serif;
}

.form-button {
  @apply w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50;
}

.form-button:disabled {
  @apply opacity-70 cursor-not-allowed hover:scale-100;
}

.login-link {
  @apply text-gray-600;
}

.sms-code-container {
  @apply flex space-x-2;
}

.sms-code-input {
  @apply flex-1;
}

.sms-code-button {
  @apply bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg transition-all duration-200;
}
</style>
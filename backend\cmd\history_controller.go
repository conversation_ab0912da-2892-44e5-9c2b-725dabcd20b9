package main

import (
	"backend/internetal"
	"net/http"

	"github.com/gin-gonic/gin"
)

// 保存对话历史
func SaveConversationHistory(c *gin.Context) {
	var req struct {
		AssistantID    int                      `json:"assistant_id"`
		AssistantName  string                   `json:"assistant_name"`
		Messages       []map[string]interface{} `json:"messages"`
		ConversationID *uint                    `json:"conversation_id"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "参数错误"})
		return
	}

	// 从上下文中获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"code": 401, "msg": "未认证"})
		return
	}

	// 安全地断言userID为uint类型
	userIDUint, ok := userID.(uint)
	if !ok {
		c.JSO<PERSON>(http.StatusInternalServerError, gin.H{"code": 500, "msg": "用户ID类型错误"})
		return
	}

	db := GetDB()
	var conversation internetal.ConversationHistory

	if req.ConversationID != nil {
		// 更新现有对话 - 先清除旧消息
		db.First(&conversation, *req.ConversationID)
		db.Where("conversation_id = ?", *req.ConversationID).Delete(&internetal.ConversationMessage{})
	} else {
		// 创建新对话
		firstMessage := ""
		if len(req.Messages) > 0 {
			if content, ok := req.Messages[0]["content"].(string); ok {
				firstMessage = content
			}
		}

		conversation = internetal.ConversationHistory{
			UserID:        userIDUint,
			AssistantID:   req.AssistantID,
			AssistantName: req.AssistantName,
			FirstMessage:  firstMessage,
		}
		db.Create(&conversation)
	}

	// 保存所有消息
	for _, msg := range req.Messages {
		messageType := "text"
		if msgType, ok := msg["messageType"].(string); ok {
			messageType = msgType
		}

		message := internetal.ConversationMessage{
			ConversationID: conversation.ID,
			Content:        msg["content"].(string),
			IsUser:         msg["isUser"].(bool),
			MessageType:    messageType,
		}
		if err := db.Create(&message).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "保存消息失败"})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "保存成功",
		"data": gin.H{"conversation_id": conversation.ID},
	})
}

// 获取历史对话列表
func GetHistoryList(c *gin.Context) {
	var req struct {
		AssistantID *int `json:"assistant_id"`
	}
	c.ShouldBindJSON(&req)

	// 从上下文中获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"code": 401, "msg": "未认证"})
		return
	}

	// 安全地断言userID为uint类型
	userIDUint, ok := userID.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "用户ID类型错误"})
		return
	}

	db := GetDB()
	var histories []internetal.ConversationHistory

	// 构建查询：只返回当前用户的对话历史
	// 无论对话的是默认机器人还是非默认机器人，对话历史都属于用户个人
	query := db.Where("user_id = ?", userIDUint).Order("created_at DESC")

	if req.AssistantID != nil && *req.AssistantID > 0 {
		query = query.Where("assistant_id = ?", *req.AssistantID)
	}

	if err := query.Find(&histories).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "查询失败"})
		return
	}

	// 确保每个历史记录都有助手名称
	for i := range histories {
		if histories[i].AssistantName == "" {
			var robot internetal.Robot
			if err := db.First(&robot, histories[i].AssistantID).Error; err == nil {
				histories[i].AssistantName = robot.Name
			} else {
				histories[i].AssistantName = "未知助手"
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{"code": 200, "data": histories})
}

// 获取对话消息
func GetHistoryMessages(c *gin.Context) {
	var req struct {
		ConversationID uint `json:"conversation_id"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "参数错误"})
		return
	}

	// 从上下文中获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"code": 401, "msg": "未认证"})
		return
	}

	// 安全地断言userID为uint类型
	userIDUint, ok := userID.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "用户ID类型错误"})
		return
	}

	db := GetDB()

	// 检查对话是否存在
	var conversation internetal.ConversationHistory
	if err := db.First(&conversation, req.ConversationID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"code": 404, "msg": "对话不存在"})
		return
	}

	// 检查对话是否属于当前用户或是否是默认机器人的对话
	var isDefaultRobotConversation bool
	var robot internetal.Robot
	if err := db.First(&robot, conversation.AssistantID).Error; err == nil {
		isDefaultRobotConversation = IsDefaultRobot(robot)
	}

	if conversation.UserID != userIDUint && !isDefaultRobotConversation {
		c.JSON(http.StatusForbidden, gin.H{"code": 403, "msg": "无权访问此对话的消息"})
		return
	}

	var messages []internetal.ConversationMessage
	if err := db.Where("conversation_id = ?", req.ConversationID).Order("created_at ASC").Find(&messages).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "查询失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"code": 200, "data": messages})
}

// 删除历史对话
func DeleteHistory(c *gin.Context) {
	var req struct {
		ConversationID uint `json:"conversation_id"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "参数错误"})
		return
	}

	// 从上下文中获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"code": 401, "msg": "未认证"})
		return
	}

	// 安全地断言userID为uint类型
	userIDUint, ok := userID.(uint)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "用户ID类型错误"})
		return
	}

	db := GetDB()

	// 检查对话是否存在
	var conversation internetal.ConversationHistory
	if err := db.First(&conversation, req.ConversationID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"code": 404, "msg": "对话不存在"})
		return
	}

	// 检查对话是否属于当前用户或是否是默认机器人的对话
	var isDefaultRobotConversation bool
	var robot internetal.Robot
	if err := db.First(&robot, conversation.AssistantID).Error; err == nil {
		isDefaultRobotConversation = IsDefaultRobot(robot)
	}

	if conversation.UserID != userIDUint && !isDefaultRobotConversation {
		c.JSON(http.StatusForbidden, gin.H{"code": 403, "msg": "无权删除此对话"})
		return
	}

	// 删除消息
	db.Where("conversation_id = ?", req.ConversationID).Delete(&internetal.ConversationMessage{})
	// 删除对话记录
	db.Delete(&internetal.ConversationHistory{}, req.ConversationID)

	c.JSON(http.StatusOK, gin.H{"code": 200, "msg": "删除成功"})
}

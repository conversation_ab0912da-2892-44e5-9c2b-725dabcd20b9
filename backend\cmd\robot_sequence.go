package main

import (
	"backend/internetal"
	"gorm.io/gorm"
)

// 更新所有机器人的序号，确保连续
func UpdateRobotSequenceNumbers() error {
	var robots []internetal.Robot
	// 按ID升序获取所有机器人
	if err := SqlSession.Order("id ASC").Find(&robots).Error; err != nil {
		return err
	}

	// 开启事务批量更新序号
	return SqlSession.Transaction(func(tx *gorm.DB) error {
		for i, robot := range robots {
			newSequence := i + 1
			if robot.SequenceNumber != newSequence {
				if err := tx.Model(&internetal.Robot{}).Where("id = ?", robot.ID).Update("sequence_number", newSequence).Error; err != nil {
					return err
				}
			}
		}
		return nil
	})
}

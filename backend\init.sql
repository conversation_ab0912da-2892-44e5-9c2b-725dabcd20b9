-- 知识库系统数据库初始化脚本
-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS knowledge_base_system DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE knowledge_base_system;

-- 知识库主表
CREATE TABLE IF NOT EXISTS `knowledge_bases` (
                                                 `id` VARCHAR(50) NOT NULL PRIMARY KEY COMMENT '知识库ID',
    `name` VARCHAR(200) NOT NULL COMMENT '知识库名称',
    `description` TEXT COMMENT '知识库描述',
    `type` VARCHAR(50) NOT NULL COMMENT '知识库类型：text, table, image, document',
    `status` VARCHAR(50) DEFAULT 'processing' COMMENT '状态：processing, completed, failed',
    `segment_count` INT DEFAULT 0 COMMENT '分段总数',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_status` (`status`),
    INDEX `idx_type` (`type`),
    INDEX `idx_created_at` (`created_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识库主表';

-- 知识库设置表
CREATE TABLE IF NOT EXISTS `knowledge_base_settings` (
                                                         `id` INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '设置ID',
                                                         `knowledge_base_id` VARCHAR(50) NOT NULL COMMENT '知识库ID',
    `parse_text` BOOLEAN DEFAULT TRUE COMMENT '是否解析文本',
    `parse_table` BOOLEAN DEFAULT FALSE COMMENT '是否解析表格',
    `content_filter` TEXT COMMENT '内容过滤规则',
    `segment_strategy` VARCHAR(50) DEFAULT 'auto' COMMENT '分段策略',
    `segment_length` INT DEFAULT 1000 COMMENT '分段长度',
    `segment_overlap` INT DEFAULT 100 COMMENT '分段重叠长度',
    `separator` VARCHAR(50) COMMENT '分隔符',
    `extract_keywords` BOOLEAN DEFAULT TRUE COMMENT '是否提取关键词',
    `generate_summary` BOOLEAN DEFAULT FALSE COMMENT '是否生成摘要',
    `use_ai` BOOLEAN DEFAULT TRUE COMMENT '是否使用AI',
    `fallback_to_local` BOOLEAN DEFAULT TRUE COMMENT '是否回退到本地处理',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY `uk_knowledge_base_id` (`knowledge_base_id`),
    FOREIGN KEY (`knowledge_base_id`) REFERENCES `knowledge_bases`(`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识库设置表';

-- 知识库文件表
CREATE TABLE IF NOT EXISTS `knowledge_base_files` (
                                                      `id` VARCHAR(50) NOT NULL PRIMARY KEY COMMENT '文件ID',
    `knowledge_base_id` VARCHAR(50) NOT NULL COMMENT '知识库ID',
    `name` VARCHAR(255) NOT NULL COMMENT '文件名',
    `size` BIGINT NOT NULL COMMENT '文件大小（字节）',
    `content` LONGTEXT COMMENT '文件内容',
    `status` VARCHAR(50) DEFAULT 'processing' COMMENT '状态：processing, completed, failed',
    `progress` INT DEFAULT 0 COMMENT '处理进度（0-100）',
    `error` TEXT COMMENT '错误信息',
    `ai_enhanced` BOOLEAN DEFAULT FALSE COMMENT '是否AI增强',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_knowledge_base_id` (`knowledge_base_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created_at` (`created_at`),
    FOREIGN KEY (`knowledge_base_id`) REFERENCES `knowledge_bases`(`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识库文件表';

-- 知识库分段表
CREATE TABLE IF NOT EXISTS `knowledge_segments` (
                                                    `id` VARCHAR(50) NOT NULL PRIMARY KEY COMMENT '分段ID',
    `file_id` VARCHAR(50) NOT NULL COMMENT '文件ID',
    `knowledge_base_id` VARCHAR(50) NOT NULL COMMENT '知识库ID',
    `content` TEXT NOT NULL COMMENT '分段内容',
    `keywords` TEXT COMMENT '关键词（JSON格式）',
    `summary` TEXT COMMENT '摘要',
    `length` INT NOT NULL COMMENT '内容长度',
    `position` INT NOT NULL COMMENT '在文件中的位置',
    `embedding` LONGTEXT COMMENT '向量数据（JSON格式）',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_file_id` (`file_id`),
    INDEX `idx_knowledge_base_id` (`knowledge_base_id`),
    INDEX `idx_position` (`position`),
    INDEX `idx_length` (`length`),
    INDEX `idx_created_at` (`created_at`),
    FOREIGN KEY (`file_id`) REFERENCES `knowledge_base_files`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`knowledge_base_id`) REFERENCES `knowledge_bases`(`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识库分段表';

-- 机器人表
CREATE TABLE IF NOT EXISTS `robots` (
                                        `id` INT NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '机器人ID',
                                        `user_id` INT NOT NULL COMMENT '所属用户ID',
                                        `name` VARCHAR(255) NOT NULL COMMENT '机器人名称',
    `description` TEXT COMMENT '机器人描述',
    `personnel_design` TEXT COMMENT '人设设计',
    `create_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `reply_logic` TEXT COMMENT '回复逻辑配置（JSON格式）',
    `knowledge_config` TEXT COMMENT '知识库配置（JSON格式）',
    `selected_knowledge_bases` TEXT COMMENT '选中的知识库列表（JSON格式）',
    INDEX `idx_name` (`name`),
    INDEX `idx_create_time` (`create_time`),
    INDEX `idx_user_id` (`user_id`),
    FOREIGN KEY (`user_id`) REFERENCES `accounts`(`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='机器人表';

-- 插入示例数据（可选）
-- 注意：这些示例数据会在应用启动时通过代码自动创建，这里仅作为参考

-- 示例知识库1
-- INSERT INTO `knowledge_bases` (`id`, `name`, `description`, `type`, `status`, `segment_count`)
-- VALUES ('kb_sample_1', 'Go语言文档', 'Go语言学习资料和API文档', 'text', 'completed', 3);

-- 示例知识库2
-- INSERT INTO `knowledge_bases` (`id`, `name`, `description`, `type`, `status`, `segment_count`)
-- VALUES ('kb_sample_2', 'Vue.js文档', 'Vue.js框架学习资料', 'text', 'completed', 2);

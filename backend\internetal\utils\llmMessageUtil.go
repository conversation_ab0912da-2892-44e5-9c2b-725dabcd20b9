package utils

import (
	"bytes"

	"github.com/goccy/go-json"

	"io"
	"log"
	"net/http"
)

type Tool struct {
	Type     string             `json:"type"`
	Function FunctionDefinition `json:"function"`
}

type RequestBody struct {
	Name string  `json:"name"`
	Body Message `json:"body"`
}

type RobotBody struct {
	Model    string    `json:"model"`
	Messages []Message `json:"messages"`
	Tools    []Tool    `json:"tools"`
	Stream   bool      `json:"stream"`
	//Stream   bool      `json:"stream"`
}

type Choice struct {
	Message      Message `json:"message"`
	FinishReason string  `json:"finish_reason"`
	Index        int     `json:"index"`
}

type Response struct {
	Choices []Choice `json:"choices"`
}

var apiKey string = "sk-488d6fd9be04416ca82063d51071ad57"
var llmUrl string = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
var modelName string = "qwen-plus"
var answer string

func MessageTreatMentions(context string, name string) string {
	// 创建 HTTP 客户端
	client := &http.Client{}
	var request RequestBody
	var res Response
	var answer string

	request.Name = name
	request.Body.Role = "user"
	request.Body.Content = context

	// 获取 SessionStore
	store := sessionStore
	messagesStore := store.GetMessageStore(request.Name)

	questions := request.Body
	store.AddMessage(name, questions)

	weatherFunction := Tool{
		Type: "function",
		Function: FunctionDefinition{
			Name:        "get_weather",
			Description: "获取指定城市的天气信息",
			Parameters: json.RawMessage([]byte(`{
              "type": "object",
              "properties": {
              "city": {
                 "type": "string",
                 "description": "要查询天气的城市名称"
                  }
             },
                  "required": ["city"]
            }`)),
		},
	}

	// 构建请求体
	requestBody := RobotBody{
		Model:    modelName,
		Messages: messagesStore.MessageStore,
		Tools:    []Tool{weatherFunction},
	}
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return ""
	}

	// 创建 POST 请求
	req, err := http.NewRequest("POST", llmUrl, bytes.NewBuffer(jsonData))
	if err != nil {
		return ""
	}
	req.Header.Set("Authorization", "Bearer "+apiKey)
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {

		return ""
	}
	defer resp.Body.Close()

	// 读取响应体
	bodyText, err := io.ReadAll(resp.Body)
	if err != nil {
		return ""
	}

	// 解析 JSON 响应
	if err := json.Unmarshal(bodyText, &res); err != nil {
		log.Printf("JSON 解析失败: %v", err)
		log.Printf("原始响应 body: %s", bodyText)
		return ""
	}

	// 如果是 system 角色设置，直接返回成功

	if request.Body.Role == "system" {
		return ""
	}

	// 检查是否有有效结果
	if len(res.Choices) == 0 {
		return ""
	}

	// 提取回答
	answer = res.Choices[0].Message.Content

	// 保存机器人回复
	answerBody := Message{
		Role:    "assistant",
		Content: answer,
	}
	store.AddMessage(request.Name, answerBody)

	// 返回成功响应
	return answer
}

<template>
  <div class="h-screen bg-gradient-to-br from-gray-50 to-gray-100 font-serif">
    <!-- 顶部导航 - 仅在非登录/注册页面显示 -->
    <div v-if="!($route.name === 'Login' || $route.name === 'Register' || $route.name === 'ResetPassword')" class="bg-white shadow-sm border-b border-gray-200 px-6 py-3">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-6">
          <h1 class="text-xl font-bold text-gray-900">AI助手平台</h1>
          <nav class="flex space-x-4">
            <button
                @click="currentView = 'chat'"
                :class="[
                'px-4 py-2 rounded-lg transition-colors',
                currentView === 'chat' ? 'bg-blue-500 text-white' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              ]"
            >
              对话助手
            </button>
            <button
                @click="currentView = 'knowledge'"
                :class="[
                'px-4 py-2 rounded-lg transition-colors',
                currentView === 'knowledge' ? 'bg-blue-500 text-white' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              ]"
            >
              知识库管理
            </button>
            <button
                @click="currentView = 'parser-test'"
                :class="[
                'px-4 py-2 rounded-lg transition-colors',
                currentView === 'parser-test' ? 'bg-blue-500 text-white' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              ]"
            >
              解析测试
            </button>
          </nav>
        </div>
        <div class="text-sm text-gray-500">
          当前视图: {{ getViewName() }}
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="h-[calc(100vh-64px)]">
      <!-- 对话助手视图 -->
      <div v-if="currentView === 'chat'" class="flex h-full">
        <!-- 登录/注册/重置密码页面 -->
        <template v-if="$route.name === 'Login' || $route.name === 'Register' || $route.name === 'ResetPassword'">
          <router-view />
        </template>
        <!-- 应用主界面 - 仅在非登录/注册页面显示 -->
        <template v-else>
          <!-- 左侧助手管理 -->
          <AssistantList
              :assistants="assistants"
              :current-selected-assistant="currentSelectedAssistant"
              :search-query="searchQuery"
              :is-logged-in="isLoggedIn"
              @add="handleActionWithLoginCheck(openAddModal)"
              @search="searchQuery = $event"
              @select="selectAssistant"
              @edit="handleActionWithLoginCheck(() => openEditModal(currentSelectedAssistant))"
              @settings="handleActionWithLoginCheck(() => openSettingsModal(currentSelectedAssistant))"
              @delete="handleActionWithLoginCheck(() => openDeleteModal(currentSelectedAssistant))"
              @history="handleActionWithLoginCheck(() => showHistoryModal = true)"
              @logout="handleLogout"
          />

          <VoiceSettingsModal
              
              :show="showVoiceSettingsModal"

              @close="showVoiceSettingsModal = false"

          />
          <!-- 右侧对话展示 -->
          <ChatArea
              ref="chatAreaRef"
              :current-selected-assistant="currentSelectedAssistant"
              :messages="currentMessages"
              :message-input="messageInput"
              :is-logged-in="isLoggedIn"
              @reset="resetChat"
              @input="messageInput = $event"
              @send="handleActionWithLoginCheck(sendMessage)"
              @dial="handleActionWithLoginCheck(() => showDialModal = true)"
              @chat-settings="handleActionWithLoginCheck(() => showChatSettingsModal = true)"
              @voice-settings="showVoiceSettingsModal = true"

          />
        </template>
      </div>

      <!-- 知识库管理视图 -->
      <div v-else-if="currentView === 'knowledge'" class="h-full">
        <KnowledgeBaseDemo />
      </div>

      <!-- 文档解析测试视图 -->
      <div v-else-if="currentView === 'parser-test'" class="h-full overflow-y-auto">
        <DocumentParserTest />
      </div>
    </div>

    <!-- 弹窗组件 - 仅在非登录/注册页面显示 -->
    <div v-if="!($route.name === 'Login' || $route.name === 'Register')">
      <ModalComponents
          :show="showVoiceSettingsModal"
          :show-add-modal="showAddModal"
          :show-settings-modal="showSettingsModal"
          :show-delete-modal="showDeleteModal"
          :show-dial-modal="showDialModal"
          :show-chat-settings-modal="showChatSettingsModal"
          :is-editing="isEditing"
          :current-assistant="currentAssistant"
          :current-settings-assistant="currentSettingsAssistant"
          :current-personality="currentPersonality"
          :delete-assistant-name="deleteAssistantName"
          :dial-number="dialNumber"
          :chat-settings="chatSettings"
          :current-selected-assistant="currentSelectedAssistant"
          :assistants="assistants"
          @close-add="closeModal"
          @update-name="currentAssistant.name = $event"
          @update-description="currentAssistant.description = $event"
          @save="saveAssistant"
          @close-settings="closeSettingsModal"
          @update-personality="currentPersonality = $event"
          @save-personality="savePersonality"
          @close-delete="closeDeleteModal"
          @confirm-delete="confirmDelete"
          @close-dial="showDialModal = false"
          @add-number="addNumber"
          @make-call="makeCall"
          @clear-number="clearNumber"
          @close-chat-settings="showChatSettingsModal = false"
          @save-chat-settings="saveChatSettings"
          @update-chat-setting="updateChatSetting"
          @voice-settings="showVoiceSettingsModal = true"
          @select-robot="selectAssistant"
      />
      <HistoryModal
          :show="showHistoryModal"
          :assistants="assistants"
          @close="showHistoryModal = false"
          @restore-conversation="restoreConversation"
      />

      <!-- 机器人设置模态框 -->
      <RobotSettingsModal
          :show="showRobotSettingsModal"
          :robot="currentSettingsAssistant"
          @close="closeRobotSettingsModal"
          @save="handleRobotSettingsSave"
      />
    </div>
  </div>
</template>

<script>
import AssistantList from './components/AssistantList.vue'
import ChatArea from './components/ChatArea.vue'
import ModalComponents from './components/ModalComponents.vue'
import HistoryModal from './components/HistoryModal.vue'
import RobotSettingsModal from './components/RobotSettingsModal.vue'
import KnowledgeBaseDemo from './views/KnowledgeBaseDemo.vue'
import DocumentParserTest from './views/DocumentParserTest.vue'
import VoiceSettingsModal from "@/components/VoiceSettingsModal.vue";
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'


export default {
  name: 'App',
  components: {
    VoiceSettingsModal,
    AssistantList,
    ChatArea,
    ModalComponents,
    HistoryModal,
    RobotSettingsModal,
    KnowledgeBaseDemo,
    DocumentParserTest
  },

  data() {

    const router = useRouter()

    // 登录状态管理
    const isLoggedIn = ref(false)
    const token = ref('')
    const userInfo = ref(null)

    // 初始化登录状态
    const initLoginStatus = () => {
      const loginStatus = localStorage.getItem('isLoggedIn')
      isLoggedIn.value = loginStatus === 'true'
      token.value = localStorage.getItem('token') || ''
      // 获取用户信息
      const userInfoStr = localStorage.getItem('userInfo')
      userInfo.value = userInfoStr ? JSON.parse(userInfoStr) : null
    }

    // 检查登录状态
    onMounted(() => {
      initLoginStatus()

      // 监听路由变化，确保登录状态正确
      router.beforeEach((to, from, next) => {
        initLoginStatus()
        next()
      })
      
      
      // 初始加载机器人列表
      if (isLoggedIn.value) {
        this.fetchRobots()
      }
    })

    // 监听登录状态变化
    watch(isLoggedIn, (newValue) => {
      if (newValue) {
        // 登录成功，刷新页面数据
        initLoginStatus() // 确保用户信息已初始化
        this.fetchRobots()
      } else {
        // 登出，清空机器人列表
        this.assistants = []
        this.assistantPersonalities = {}
        this.currentSelectedAssistant = null
      }
    })



    // 处理需要登录的操作
    const handleActionWithLoginCheck = (action) => {
      if (isLoggedIn.value) {
        action()
      } else {
        alert('请先登录')
        // 不自动跳转，只有点击登录按钮时才跳转
      }
    }

    // 处理登出事件
    const handleLogout = () => {
      isLoggedIn.value = false
      // 清除会话数据
      localStorage.removeItem('isLoggedIn')
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
    }

    return {
      isLoggedIn,
      token,
      userInfo,
      router,
      handleLogout,
      currentView: 'chat', // 'chat' | 'knowledge' | 'parser-test'
      showAddModal: ref(false),
      showDialModal: ref(false),
      showSettingsModal: ref(false),
      showDeleteModal: ref(false),
      showHistoryModal: ref(false),
      showChatSettingsModal: ref(false),
      showRobotSettingsModal: false,
      currentConversationId: ref(null),
      dialNumber: ref(''),
      messageInput: ref(''),
      searchQuery: ref(''),
      isEditing: ref(false),
      currentSelectedAssistant: ref(null),
      currentSettingsAssistant: ref(null),
      currentPersonality: ref(''),
      deleteAssistantId: ref(null),
      showVoiceSettingsModal: false,
      deleteAssistantName: ref(''),
      currentAssistant: ref({
        id: null,
        name: '',
        description: ''
      }),
      assistantMessages: ref({}),
      handleActionWithLoginCheck,
      assistantPersonalities: ref({}),
      assistants: ref([]), // 空初始化，由fetchRobots填充
      chatSettings: {
        language: 'zh-cn',
        speaker: '601002',
        systemRole: '你是一个专业的对话大模型工程师，专注于帮助用户解决技术相关的问题。',
        dialogCommand: '请以简洁、专业的方式回答用户的问题，尽量控制在合理的长度内',
        responseMode: 'simple',
        temperature: 0.6,
        maxTokens: 50,
        speechSpeed: 1.0
      }
    }
  },
  computed: {
    currentMessages() {
      if (!this.currentSelectedAssistant) return []
      return this.assistantMessages[this.currentSelectedAssistant.id] || []
    }
  },

  methods: {
    getViewName() {
      const viewNames = {
        'chat': '对话助手',
        'knowledge': '知识库管理',
        'parser-test': '文档解析测试'
      }
      return viewNames[this.currentView] || '未知视图'
    },

    openAddModal() {
      this.isEditing = false
      this.currentAssistant = {
        id: null,
        name: '',
        description: ''
      }
      this.showAddModal = true
    },
    openEditModal(assistant) {
      this.isEditing = true
      this.currentAssistant = {
        id: assistant.id,
        name: assistant.name,
        description: assistant.description
      }
      this.showAddModal = true
    },
    closeModal() {
      this.showAddModal = false
      this.currentAssistant = {
        id: null,
        name: '',
        description: ''
      }
    },
    async saveAssistant() {
      if (this.currentAssistant.name.trim() && this.currentAssistant.description.trim()) {
        if (this.isEditing) {
          // 编辑机器人
          try {
            const response = await fetch('http://localhost:8080/api/robot/update', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.token}`
              },
              body: JSON.stringify({
                id: this.currentAssistant.id,
                name: this.currentAssistant.name.trim(),
                description: this.currentAssistant.description.trim(),
                personnel_design: this.assistantPersonalities[this.currentAssistant.id] || "你是一个友善、专业的AI助手。"
              })
            })

            const result = await response.json()
            if (result.code === 200) {
              const index = this.assistants.findIndex(a => a.id === this.currentAssistant.id)
              if (index !== -1) {
                this.assistants[index] = { ...this.currentAssistant }
                if (this.currentSelectedAssistant?.id === this.currentAssistant.id) {
                  this.currentSelectedAssistant = { ...this.currentAssistant }
                }
              }
            } else {
              console.error('更新失败:', result.msg)
            }
          } catch (error) {
            console.error('网络错误:', error)
          }
        } else {
          // 创建新机器人
          const robotData = {
            name: this.currentAssistant.name.trim(),
            description: this.currentAssistant.description.trim(),
            personnel_design: this.assistantPersonalities[this.currentAssistant.id] || "你是一个友善、专业的AI助手。"
          }

          try {
            const response = await fetch('http://localhost:8080/api/robot/create', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.token}`
              },
              body: JSON.stringify(robotData)
            })

            const result = await response.json()
            if (result.code === 200) {
              // 直接添加新机器人到列表，不等待fetchRobots
              const newRobot = {
                id: result.data.id,
                name: result.data.name,
                description: result.data.description
              }
              this.assistants.push(newRobot)
              // 同时更新人设配置
              if (result.data.personnel_design) {
                this.assistantPersonalities[newRobot.id] = result.data.personnel_design
              }
              // 自动选中新建的机器人并重置会话ID
              this.currentConversationId = null
              this.selectAssistant(newRobot)
            } else {
              console.error('创建失败:', result.msg)
            }
          } catch (error) {
            console.error('网络错误:', error)
          }
        }

        this.closeModal()
      }
    },

    openSettingsModal(assistant) {
      this.currentSettingsAssistant = assistant
      // 从本地存储或后端获取人设配置
      this.currentPersonality = this.assistantPersonalities[assistant.id] || '你是一个友善、专业的AI助手。'
      this.showRobotSettingsModal = true
    },
    closeSettingsModal() {
      this.showSettingsModal = false
      this.currentSettingsAssistant = null
      this.currentPersonality = ''
    },
    async savePersonality() {
      if (this.currentSettingsAssistant) {
        try {
          const response = await fetch('http://localhost:8080/api/robot/update', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${this.token}`
            },
            body: JSON.stringify({
              id: this.currentSettingsAssistant.id,
              name: this.currentSettingsAssistant.name,
              description: this.currentSettingsAssistant.description,
              personnel_design: this.currentPersonality
            })
          })

          const result = await response.json()
          if (result.code === 200) {
            // 更新本地存储
            this.assistantPersonalities[this.currentSettingsAssistant.id] = this.currentPersonality
            console.log('人设配置保存成功')
          } else {
            console.error('人设配置保存失败:', result.msg)
            alert('保存失败: ' + result.msg)
          }
        } catch (error) {
          console.error('网络错误:', error)
          alert('网络错误，保存失败')
        }

        this.closeSettingsModal()
      }
    },
    openDeleteModal(assistant) {
      this.deleteAssistantId = assistant.id
      this.deleteAssistantName = assistant.name
      this.showDeleteModal = true
    },
    closeDeleteModal() {
      this.showDeleteModal = false
      this.deleteAssistantId = null
      this.deleteAssistantName = ''
    },
    async confirmDelete() {
      if (this.deleteAssistantId) {
        try {
              const response = await fetch('http://localhost:8080/api/robot/delete', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': `Bearer ${this.token}`
                },
                body: JSON.stringify({
                  id: this.deleteAssistantId
                })
              })

              const result = await response.json()
              if (result.code === 200) {
                // 使用splice方法触发响应式更新
                const index = this.assistants.findIndex(assistant => assistant.id === this.deleteAssistantId)
                if (index !== -1) {
                  this.assistants.splice(index, 1)
                }
                if (this.currentSelectedAssistant?.id === this.deleteAssistantId) {
                  this.currentSelectedAssistant = null
                }
                delete this.assistantMessages[this.deleteAssistantId]
                delete this.assistantPersonalities[this.deleteAssistantId]
              } else {
                console.error('删除失败:', result.msg)
              }
            } catch (error) {
              console.error('网络错误:', error)
            }

        this.closeDeleteModal()
      }
    },
    deleteAssistant(id) {
      // 这个方法现在被 openDeleteModal 替代
      const assistant = this.assistants.find(a => a.id === id)
      if (assistant) {
        this.openDeleteModal(assistant)
      }
    },
    addNumber(number) {
      this.dialNumber += number
    },
    clearNumber() {
      this.dialNumber = ''
    },
    async makeCall() {
      // 验证输入是否为空
      if (!this.dialNumber) {
        alert('请输入机器人编号');
        return;
      }

      try {
        // 尝试将输入转换为整数ID
        const robotId = parseInt(this.dialNumber, 10);
        if (isNaN(robotId)) {
          alert('请输入有效的数字编号');
          return;
        }

        // 检查是否存在对应ID的机器人
        const robotExists = this.assistants.some(assistant => assistant.id === robotId);
        if (!robotExists) {
          alert('没有该机器人，请重新输入编号');
          return;
        }

        const encodedDialNumber = encodeURIComponent(this.dialNumber);
        // 根据电话号码查询机器人
        const response = await fetch(`http://localhost:8080/api/robot/find/by_id?id=${encodedDialNumber}`, {
          headers: {
            'Authorization': `Bearer ${this.token}`
          }
        });
        // 先检查响应状态是否正常
        if (!response.ok) {
          throw new Error(`服务器错误: ${response.status}`);
        }

        const result = await response.json();

        if (result.code === 200) {
          this.currentCallRobot = result.data;
          this.isCalling = true;

          // 切换到该机器人的聊天界面
          const targetRobot = this.assistants.find(assistant => assistant.id === robotId);
          if (targetRobot) {
            this.selectAssistant(targetRobot);
          }

          // 更新后端通话状态
          await fetch('http://localhost:8080/api/call/status', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${this.token}`
            },
            body: JSON.stringify({
              robot_id: this.currentCallRobot.id,
              status: 'calling',
              id: this.dialNumber
            })
          });

          // 调用语音通话功能
          this.startVoiceCall(this.currentCallRobot);
        } else {
          alert('未找到对应的机器人');
        }
      } catch (error) {
        console.error('拨号失败', error);
        alert('拨号失败，请重试');
      }
    },

    async endCall() {
      if (!this.currentCallRobot) return;

      this.isCalling = false;

      // 停止语音通话
      this.stopVoiceCall();

      // 更新后端通话状态
      await fetch('http://localhost:8080/api/call/status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.token}`
        },
        body: JSON.stringify({
          robot_id: this.currentCallRobot.id,
          status: 'ended'
        })
      });

      this.currentCallRobot = null;
      this.dialNumber = '';
      this.showDialModal = false;
    },

    startVoiceCall(robot) {
      // 调用现有WebRTC功能
      if (this.$refs.chatAreaRef) {
        this.$refs.chatAreaRef.callVoiceRecorderToggle(robot);
      }
    },

    stopVoiceCall() {
      // 停止现有WebRTC通话
      if (this.$refs.chatAreaRef && this.$refs.chatAreaRef.isWebRTCConnected) {
        this.$refs.chatAreaRef.callVoiceRecorderDisconnect();
      }
    },
    // 新增：处理语音消息，转发给ChatArea
    handleVoiceMessage(message) {
      if (this.$refs.chatAreaRef) {
        this.$refs.chatAreaRef.handleVoiceMessage(message)
      }
    },
    selectAssistant(assistant) {
      this.currentSelectedAssistant = assistant
      this.currentConversationId = null // 重置会话ID

      if (!this.assistantMessages[assistant.id]) {
        this.assistantMessages[assistant.id] = [
          {
            id: Date.now(),
            content: `您好！我是${assistant.name}，源于通义千问，阿里巴巴集团旗下的超大规模语言模型助手，有什么可以帮助您的吗？`,
            isUser: false,
            timestamp: new Date()
          }
        ]
      }
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },

 
    async sendMessage() {
      if (this.messageInput.trim() && this.currentSelectedAssistant) {
        // 添加用户消息到界面
        this.assistantMessages[this.currentSelectedAssistant.id].push({
          id: Date.now(),
          content: this.messageInput.trim(),
          isUser: true,
          messageType: 'text',
          timestamp: new Date()
        })

        const userMessage = this.messageInput.trim()
        this.messageInput = ''

        try {
          // 统一请求格式：先发送人设配置（如果有）
          const personality = this.assistantPersonalities[this.currentSelectedAssistant.id]
          if (personality && personality.trim()) {
            await fetch('http://localhost:8080/api/text/send', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.token}`
              },
              body: JSON.stringify({
                user_id: this.currentSelectedAssistant.id.toString(),
                message: {
                  role: "system",
                  content: personality
                },
                streaming: false
              })
            })
          }

          // 发送用户消息
          const response = await fetch('http://localhost:8080/api/text/send', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${this.token}`
            },
            body: JSON.stringify({
              user_id: this.currentSelectedAssistant.id.toString(),
              message: {
                role: "user",
                content: userMessage
              },
              streaming: false
            })
          });

          if (!response.ok) {
            const errorText = await response.text();
            console.error('服务器响应:', errorText);
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const result = await response.json();

          if (result.code === 200) {
            // 添加AI回复
            this.assistantMessages[this.currentSelectedAssistant.id].push({
              id: Date.now() + 1,
              content: result.data,
              isUser: false,
              messageType: 'text',
              timestamp: new Date()
            });

            // 每次对话后自动保存
            await this.saveConversation()
          } else {
            throw new Error(result.msg || '服务器返回错误');
          }
        } catch (error) {
          console.error('发送消息失败:', error)
          this.assistantMessages[this.currentSelectedAssistant.id].push({
            id: Date.now() + 1,
            content: '抱歉，AI服务暂时不可用，请稍后重试',
            isUser: false,
            timestamp: new Date()
          })
        }

        this.scrollToBottom()
      }
    },
    // 修复后的滚动到底部方法（采用第一个文件的实现）
    scrollToBottom() {
      this.$nextTick(() => {
        // 使用ref直接获取组件
        const chatArea = this.$refs.chatAreaRef
        if (chatArea && chatArea.$refs.chatContainer) {
          const container = chatArea.$refs.chatContainer
          container.scrollTop = container.scrollHeight
          console.log('滚动到底部')
        } else {
          console.warn('找不到聊天容器元素')
        }
      })
    },
    resetChat() {
      if (this.currentSelectedAssistant) {
        this.currentConversationId = null // 重置会话ID
        this.assistantMessages[this.currentSelectedAssistant.id] = [
          {
            id: Date.now(),
            content: `您好！我是${this.currentSelectedAssistant.name}，我是通义千问，阿里巴巴集团旗下的超大规模语言模型助手，有什么可以帮助您的吗？`,
            isUser: false,
            timestamp: new Date()
          }
        ]
        this.messageInput = ''
        this.scrollToBottom()
      }
    },
    // 检查机器人访问权限
    checkRobotAccess(robot) {
      // 确保用户信息存在且机器人有user_id字段
      if (!this.userInfo || !this.userInfo.id || robot.user_id === undefined) {
        // 未登录状态或数据不完整时，只允许访问默认机器人
        return robot.user_id === 0;
      }
      // 用户可以访问自己的机器人或系统默认机器人
      return robot.user_id === this.userInfo.id || robot.user_id === 0;
    },

    async fetchRobots() {
      try {
        const response = await fetch('http://localhost:8080/api/robot/list', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.token}`
          }
        })
        const result = await response.json()

        console.log('获取到的机器人数据:', result)

        if (result.code === 200 && result.data && result.data.length > 0) {
          // 过滤出用户有权访问的机器人
          const accessibleRobots = result.data.filter(robot => this.checkRobotAccess(robot));

          this.assistants = accessibleRobots.map(robot => ({
            id: robot.id,
            name: robot.name,
            description: robot.description,
            user_id: robot.user_id // 保存user_id以便前端验证
          }))

          // 加载人设配置
          accessibleRobots.forEach(robot => {
            if (robot.personnel_design) {
              this.assistantPersonalities[robot.id] = robot.personnel_design
            }
          })
        } else {
          console.log('后端无数据，使用默认机器人')
        }
      } catch (error) {
        console.error('获取机器人列表失败:', error)
      }
    },
    handleVoiceInput() {
      // 语音输入处理逻辑，暂时显示提示
      alert('语音功能开发中...')
      // 后续可以在这里添加语音识别逻辑
    },
    async saveConversation() {
      if (!this.currentSelectedAssistant || !this.currentMessages.length) return

      // 过滤掉初始欢迎消息，只保存真实对话
      const realMessages = this.currentMessages.filter(msg =>
          !(msg.content.includes('您好！我是') && !msg.isUser)
      )

      if (realMessages.length === 0) return

      try {
        const messagesToSave = realMessages.map(msg => ({
          content: msg.content,
          isUser: msg.isUser,
          messageType: msg.messageType || 'text'
        }))

        console.log('保存对话数据:', {
          assistant_id: this.currentSelectedAssistant.id,
          assistant_name: this.currentSelectedAssistant.name,
          messages: messagesToSave,
          conversation_id: this.currentConversationId
        })

        const response = await fetch('http://localhost:8080/api/history/save', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${this.token}`
            },
            body: JSON.stringify({
              assistant_id: this.currentSelectedAssistant.id,
              assistant_name: this.currentSelectedAssistant.name,
              messages: messagesToSave,
              conversation_id: this.currentConversationId
            })
          })

        const result = await response.json()
        if (result.code === 200) {
          this.currentConversationId = result.data.conversation_id
          console.log('对话保存成功:', result)
        } else {
          console.error('保存对话失败:', result.msg)
        }
      } catch (error) {
        console.error('保存对话失败:', error)
      }
    },
    async restoreConversation(data) {
      console.log('接收到恢复对话请求:', data)

      // 尝试按ID查找助手
      let assistant = this.assistants.find(a => a.id == data.assistant.id)

      // 如果助手不存在，尝试从数据库恢复
      if (!assistant) {
        console.log('助手不存在，尝试从数据库恢复')
        try {
          const response = await fetch(`http://localhost:8080/api/robot/findById?id=${data.assistant.id}`, {
              headers: {
                'Authorization': `Bearer ${this.token}`
              }
            })
          const result = await response.json()

          if (result.code === 200 && result.data) {
            // 从数据库恢复助手
            assistant = {
              id: result.data.id,
              name: result.data.name,
              description: result.data.description
            }

            // 添加到助手列表
            this.assistants.push(assistant)

            // 恢复人设配置
            if (result.data.personnel_design) {
              this.assistantPersonalities[assistant.id] = result.data.personnel_design
            }

            console.log('从数据库恢复助手成功:', assistant)
          } else {
            // 数据库中也没有，重新创建到数据库
            console.log('数据库中没有找到助手，重新创建到数据库')
            try {
              const createResponse = await fetch('http://localhost:8080/api/robot/create', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': `Bearer ${this.token}`
                },
                body: JSON.stringify({
                  name: data.assistant.name,
                  description: data.assistant.description || `恢复的助手: ${data.assistant.name}`,
                  personnel_design: "你是一个友善、专业的AI助手。"
                })
              })

              const createResult = await createResponse.json()
              if (createResult.code === 200) {
                assistant = {
                  id: createResult.data.id,
                  name: createResult.data.name,
                  description: createResult.data.description
                }

                // 添加到助手列表
                this.assistants.push(assistant)

                console.log('重新创建助手到数据库成功:', assistant)
              } else {
                throw new Error('创建助手失败')
              }
            } catch (createError) {
              console.error('重新创建助手失败:', createError)
              // 创建临时助手作为备选
              assistant = {
                id: data.assistant.id,
                name: data.assistant.name,
                description: `已删除的助手: ${data.assistant.name}`
              }
              this.assistants.push(assistant)
            }
          }
        } catch (error) {
          console.error('从数据库恢复助手失败:', error)
          // 创建临时助手作为备选
          assistant = {
            id: data.assistant.id,
            name: data.assistant.name,
            description: `已删除的助手: ${data.assistant.name}`
          }
          this.assistants.push(assistant)
        }
      }

      // 设置当前选中的助手
      this.currentSelectedAssistant = assistant

      // 重置会话ID
      this.currentConversationId = null

      // 恢复消息列表
      this.assistantMessages[assistant.id] = data.messages.map(msg => ({
        id: msg.id || Date.now() + Math.random(),
        content: msg.content,
        isUser: msg.is_user,
        messageType: msg.message_type || 'text',
        timestamp: new Date(msg.created_at)
      }))

      console.log('恢复的消息:', this.assistantMessages[assistant.id])

      // 滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },
    addMessage(message) {
      if (this.currentSelectedAssistant) {
        if (!this.assistantMessages[this.currentSelectedAssistant.id]) {
          this.assistantMessages[this.currentSelectedAssistant.id] = []
        }

        // 确保消息包含类型信息
        const messageWithType = {
          ...message,
          messageType: message.messageType || 'text'
        }

        this.assistantMessages[this.currentSelectedAssistant.id].push(messageWithType)
        this.scrollToBottom()
      }
    },
    saveChatSettings() {
      console.log('保存对话设置:', this.chatSettings)
      // 这里可以添加保存到本地存储或发送到后端的逻辑
      this.showChatSettingsModal = false
    },
    updateChatSetting(key, value) {
      this.chatSettings[key] = value
    },

    closeRobotSettingsModal() {
      this.showRobotSettingsModal = false
      this.currentSettingsAssistant = null
    },

    async handleRobotSettingsSave(updatedRobot) {
      // 更新本地助手数据
      const index = this.assistants.findIndex(a => a.id === updatedRobot.id)
      if (index !== -1) {
        this.assistants[index] = { ...this.assistants[index], ...updatedRobot }

        // 更新人设配置到本地存储
        if (updatedRobot.personnel_design) {
          this.assistantPersonalities[updatedRobot.id] = updatedRobot.personnel_design
        }

        // 如果是当前选中的助手，也要更新
        if (this.currentSelectedAssistant?.id === updatedRobot.id) {
          this.currentSelectedAssistant = { ...this.currentSelectedAssistant, ...updatedRobot }
        }
      }

      // 重新获取最新的机器人列表
      await this.fetchRobots()
    }
  },

  async mounted() {
    // 页面加载时获取所有机器人
    await this.fetchRobots()

    // 如果有机器人，默认选中第一个
    if (this.assistants.length > 0) {
      this.selectAssistant(this.assistants[0])
    }
  }
}
</script>